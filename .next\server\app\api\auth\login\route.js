(()=>{var e={};e.id=873,e.ids=[873],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},66988:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>_,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>x});var a=t(49303),i=t(88716),u=t(60670),o=t(87070),n=t(72438),p=t(42023),l=t.n(p),c=t(41482),m=t.n(c);let d=(0,n.eI)(process.env.SUPABASE_URL,process.env.SUPABASE_ANON_KEY);async function x(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return o.NextResponse.json({error:"Email and password are required"},{status:400});let{data:s,error:a}=await d.from("users").select("*").eq("email",r.toLowerCase()).single();if(a||!s||!await l().compare(t,s.password_hash))return o.NextResponse.json({error:"Invalid email or password"},{status:401});if(!s.is_active)return o.NextResponse.json({error:"Account is deactivated. Please contact support."},{status:403});let i=m().sign({userId:s.id,email:s.email,username:s.username},process.env.JWT_SECRET||"fallback-secret",{expiresIn:"7d"}),{data:u}=await d.from("telegram_users").select("telegram_id, username, first_name, last_name").eq("user_id",s.id).single(),n={id:s.id,username:s.username,email:s.email,full_name:s.full_name,phone:s.phone,is_verified:s.is_verified,created_at:s.created_at,telegram_linked:!!u,telegram_info:u?{telegram_id:u.telegram_id,telegram_username:u.username,first_name:u.first_name,last_name:u.last_name}:null},p=o.NextResponse.json({success:!0,message:"Login successful",user:n});return p.cookies.set("auth-token",i,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}),p}catch(e){return console.error("Login error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:f}=_,q="/api/auth/login/route";function v(){return(0,u.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169,482,23],()=>t(66988));module.exports=s})();