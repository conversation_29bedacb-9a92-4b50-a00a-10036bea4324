(()=>{var e={};e.id=716,e.ids=[716],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},78780:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>x,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c});var o=r(49303),i=r(88716),u=r(60670),a=r(87070),n=r(1066);async function c(e){try{e.cookies.get("sb-access-token")?.value&&await n.OQ.auth.signOut();let t=a.NextResponse.json({success:!0});return t.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t}catch(e){return console.error("Logout error:",e),a.NextResponse.json({error:"Logout failed"},{status:500})}}async function p(e){try{let t=a.NextResponse.redirect(new URL("/login",e.url));return t.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),t}catch(t){return console.error("Logout redirect error:",t),a.NextResponse.redirect(new URL("/login?error=logout_failed",e.url))}}let l=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:d,serverHooks:h}=l,g="/api/auth/logout/route";function m(){return(0,u.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}},1066:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>i});var s=r(72438);let o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!o)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let i=(0,s.eI)("https://fgubaqoftdeefcakejwu.supabase.co",o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,169],()=>r(78780));module.exports=s})();