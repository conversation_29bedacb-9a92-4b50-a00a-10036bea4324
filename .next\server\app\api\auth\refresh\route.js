(()=>{var e={};e.id=575,e.ids=[575],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},30420:(e,r,s)=>{"use strict";s.r(r),s.d(r,{originalPathname:()=>f,patchFetch:()=>l,requestAsyncStorage:()=>h,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{POST:()=>p});var o=s(49303),i=s(88716),u=s(60670),a=s(87070),n=s(1066);async function p(e){try{let r=e.cookies.get("sb-refresh-token")?.value;if(!r)return a.NextResponse.json({error:"No refresh token provided"},{status:401});let{data:{session:s},error:t}=await n.OQ.auth.refreshSession({refresh_token:r});if(t||!s){let e=a.NextResponse.json({error:"Failed to refresh session"},{status:401});return e.cookies.set("sb-access-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),e.cookies.set("sb-refresh-token","",{path:"/",expires:new Date(0),sameSite:"lax",secure:!0}),e}let o=a.NextResponse.json({success:!0,user:s.user});return o.cookies.set("sb-access-token",s.access_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),s.refresh_token&&o.cookies.set("sb-refresh-token",s.refresh_token,{path:"/",maxAge:2592e3,sameSite:"lax",secure:!0}),o}catch(e){return console.error("Session refresh error:",e),a.NextResponse.json({error:"Session refresh failed"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/refresh/route",pathname:"/api/auth/refresh",filename:"route",bundlePath:"app/api/auth/refresh/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\refresh\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:d}=c,f="/api/auth/refresh/route";function l(){return(0,u.patchFetch)({serverHooks:d,staticGenerationAsyncStorage:x})}},1066:(e,r,s)=>{"use strict";s.d(r,{OQ:()=>i});var t=s(72438);let o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!o)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let i=(0,t.eI)("https://fgubaqoftdeefcakejwu.supabase.co",o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,169],()=>s(30420));module.exports=t})();