(()=>{var e={};e.id=2,e.ids=[2],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},20547:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>v,patchFetch:()=>_,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>q,staticGenerationAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>x});var a=t(49303),i=t(88716),o=t(60670),u=t(87070),n=t(72438),c=t(42023),p=t.n(c),l=t(41482),d=t.n(l);let m=(0,n.eI)(process.env.SUPABASE_URL,process.env.SUPABASE_ANON_KEY);async function x(e){try{let{email:r,password:t,confirmPassword:s}=await e.json();if(!r||!t||!s)return u.NextResponse.json({error:"Email, password, and password confirmation are required"},{status:400});if(t!==s)return u.NextResponse.json({error:"Passwords do not match"},{status:400});if(t.length<8)return u.NextResponse.json({error:"Password must be at least 8 characters long"},{status:400});let a=r.toLowerCase(),{data:i}=await m.from("users").select("id").eq("email",a).single();if(i)return u.NextResponse.json({error:"An account with this email already exists"},{status:409});let o=await p().hash(t,12),n=a.split("@")[0]+"_"+Date.now(),{data:c,error:l}=await m.from("users").insert({username:n,email:a,password_hash:o,is_active:!0,is_verified:!1}).select().single();if(l)return console.error("User creation error:",l),u.NextResponse.json({error:"Failed to create account. Please try again."},{status:500});let x=d().sign({userId:c.id,email:c.email,username:c.username},process.env.JWT_SECRET||"fallback-secret",{expiresIn:"7d"}),h={id:c.id,username:c.username,email:c.email,full_name:c.full_name,phone:c.phone,is_verified:c.is_verified,created_at:c.created_at,telegram_linked:!1,telegram_info:null},f=u.NextResponse.json({success:!0,message:"Account created successfully",user:h});return f.cookies.set("auth-token",x,{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}),f}catch(e){return console.error("Registration error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:q}=h,v="/api/auth/register/route";function _(){return(0,o.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169,482,23],()=>t(20547));module.exports=s})();