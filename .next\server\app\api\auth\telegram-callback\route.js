(()=>{var e={};e.id=128,e.ids=[128],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},60287:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>_,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>x,staticGenerationAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{GET:()=>d});var s=t(49303),i=t(88716),o=t(60670),u=t(87070),n=t(72438),c=t(84770);let l=process.env.SUPABASE_SERVICE_ROLE_KEY,p=(0,n.eI)("https://fgubaqoftdeefcakejwu.supabase.co",l,{auth:{autoRefreshToken:!1,persistSession:!1}});async function d(e){try{let r=new URL(e.url),t=Object.fromEntries(r.searchParams.entries());if(!function(e){if(!e)return!1;let{hash:r,...t}=e,a=Object.keys(t).sort().map(e=>`${e}=${t[e]}`).join("\n"),s=(0,c.createHash)("sha256").update("7858706839:AAFRXBSlREW0wPvIyI57uFpHfYopi2CY464").digest();return(0,c.createHmac)("sha256",s).update(a).digest("hex")===r}(t))return u.NextResponse.redirect(new URL("/login?error=Invalid+authentication",e.url));let a={id:t.id.toString(),username:t.username||"",firstName:t.first_name||"",lastName:t.last_name||"",photoUrl:t.photo_url||"",authDate:t.auth_date},{data:s,error:i}=await p.from("telegram_users").select("*").eq("telegram_id",a.id).single();if(i&&"PGRST116"!==i.code)return console.error("Error finding user:",i),u.NextResponse.redirect(new URL("/login?error=Database+error",e.url));let{data:o,error:n}=await p.auth.admin.createUser({email:`telegram_${a.id}@aureus.africa`,password:`telegram_${a.id}_${Date.now()}`,email_confirm:!0,user_metadata:{telegram_id:a.id,telegram_username:a.username,telegram_first_name:a.firstName,telegram_last_name:a.lastName}});if(n)return console.error("Error creating auth user:",n),u.NextResponse.redirect(new URL("/login?error=Auth+error",e.url));let{data:l,error:d}=await p.auth.admin.createSession({user_id:o.user.id,expires_in:604800});if(d)return console.error("Error creating session:",d),u.NextResponse.redirect(new URL("/login?error=Session+error",e.url));let m=u.NextResponse.redirect(new URL("/dashboard",e.url));return m.cookies.set("sb-access-token",l.access_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),m.cookies.set("sb-refresh-token",l.refresh_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),m}catch(r){return console.error("Telegram auth error:",r),u.NextResponse.redirect(new URL("/login?error=Server+error",e.url))}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/telegram-callback/route",pathname:"/api/auth/telegram-callback",filename:"route",bundlePath:"app/api/auth/telegram-callback/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\telegram-callback\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:x}=m,f="/api/auth/telegram-callback/route";function _(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[276,169],()=>t(60287));module.exports=a})();