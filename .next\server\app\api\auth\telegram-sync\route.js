(()=>{var e={};e.id=61,e.ids=[61],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},59007:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>v,requestAsyncStorage:()=>x,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{DELETE:()=>g,POST:()=>m});var a=t(49303),n=t(88716),u=t(60670),o=t(87070),i=t(72438),l=t(41482),c=t.n(l);let p=(0,i.eI)(process.env.SUPABASE_URL,process.env.SUPABASE_ANON_KEY);function d(e){try{return c().verify(e,process.env.JWT_SECRET||"fallback-secret")}catch{return null}}async function m(e){try{let r=e.cookies.get("auth-token")?.value;if(!r)return o.NextResponse.json({error:"Authentication required"},{status:401});let t=d(r);if(!t)return o.NextResponse.json({error:"Invalid authentication token"},{status:401});let{telegram_username:s,telegram_id:a}=await e.json();if(!s&&!a)return o.NextResponse.json({error:"Telegram username or ID is required"},{status:400});let n=null;if(a){let{data:e}=await p.from("telegram_users").select("*").eq("telegram_id",a).single();n=e}else if(s){let{data:e}=await p.from("telegram_users").select("*").eq("username",s.replace("@","")).single();n=e}if(!n)return o.NextResponse.json({error:"Telegram account not found. Please make sure you have used the bot first."},{status:404});if(n.user_id&&n.user_id!==t.userId)return o.NextResponse.json({error:"This Telegram account is already linked to another user"},{status:409});let{error:u}=await p.from("telegram_users").update({user_id:t.userId,updated_at:new Date().toISOString()}).eq("id",n.id);if(u)return console.error("Telegram sync error:",u),o.NextResponse.json({error:"Failed to link Telegram account"},{status:500});let{data:i}=await p.from("users").select("*").eq("id",t.userId).single(),l={id:i.id,username:i.username,email:i.email,full_name:i.full_name,phone:i.phone,is_verified:i.is_verified,created_at:i.created_at,telegram_linked:!0,telegram_info:{telegram_id:n.telegram_id,telegram_username:n.username,first_name:n.first_name,last_name:n.last_name}};return o.NextResponse.json({success:!0,message:"Telegram account linked successfully",user:l})}catch(e){return console.error("Telegram sync error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=e.cookies.get("auth-token")?.value;if(!r)return o.NextResponse.json({error:"Authentication required"},{status:401});let t=d(r);if(!t)return o.NextResponse.json({error:"Invalid authentication token"},{status:401});let{error:s}=await p.from("telegram_users").update({user_id:null,updated_at:new Date().toISOString()}).eq("user_id",t.userId);if(s)return console.error("Telegram unlink error:",s),o.NextResponse.json({error:"Failed to unlink Telegram account"},{status:500});return o.NextResponse.json({success:!0,message:"Telegram account unlinked successfully"})}catch(e){return console.error("Telegram unlink error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/telegram-sync/route",pathname:"/api/auth/telegram-sync",filename:"route",bundlePath:"app/api/auth/telegram-sync/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\telegram-sync\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:x,staticGenerationAsyncStorage:_,serverHooks:h}=f,q="/api/auth/telegram-sync/route";function v(){return(0,u.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:_})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169,482],()=>t(59007));module.exports=s})();