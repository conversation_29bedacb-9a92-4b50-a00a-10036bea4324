(()=>{var e={};e.id=420,e.ids=[420],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},97258:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>x,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>p,PUT:()=>c});var o=t(49303),a=t(88716),i=t(60670),u=t(87070),n=t(1066);async function p(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return u.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await n.OQ.auth.getUser(r);if(s||!t)return u.NextResponse.json({error:"Invalid or expired token"},{status:401});let o=t.user_metadata?.telegram_id,a=null;if(o){let{data:e,error:r}=await n.OQ.from("telegram_users").select("*").eq("telegram_id",o).single();!r&&e&&(a=e)}let i=!1,p=!1,c=!1;if(a){let{data:e}=await n.OQ.from("terms_acceptance").select("*").eq("user_id",a.id).single();i=!!e,p=!!a.country;let{data:r}=await n.OQ.from("kyc_information").select("verification_status").eq("user_id",a.id).single();c=r?.verification_status==="approved"}return u.NextResponse.json({user:{...t,telegram_profile:a,hasAcceptedTerms:i,hasSelectedCountry:p,hasCompletedKYC:c}})}catch(e){return console.error("Get user error:",e),u.NextResponse.json({error:"Failed to get user data"},{status:500})}}async function c(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return u.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await n.OQ.auth.getUser(r);if(s||!t)return u.NextResponse.json({error:"Invalid or expired token"},{status:401});let{country:o,phone:a,address:i}=await e.json(),p=t.user_metadata?.telegram_id;if(!p)return u.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:c,error:d}=await n.OQ.from("telegram_users").update({country:o,phone:a,address:i,updated_at:new Date().toISOString()}).eq("telegram_id",p).select().single();if(d)return console.error("Update user error:",d),u.NextResponse.json({error:"Failed to update user profile"},{status:500});return u.NextResponse.json({success:!0,user:c})}catch(e){return console.error("Update user error:",e),u.NextResponse.json({error:"Failed to update user profile"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:f,serverHooks:m}=d,x="/api/auth/user/route";function g(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:f})}},1066:(e,r,t)=>{"use strict";t.d(r,{OQ:()=>a});var s=t(72438);let o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!o)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let a=(0,s.eI)("https://fgubaqoftdeefcakejwu.supabase.co",o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169],()=>t(97258));module.exports=s})();