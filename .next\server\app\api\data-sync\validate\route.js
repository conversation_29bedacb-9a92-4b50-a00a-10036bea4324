(()=>{var e={};e.id=595,e.ids=[595],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},87061:(e,s,t)=>{"use strict";t.r(s),t.d(s,{originalPathname:()=>_,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var r={};t.r(r),t.d(r,{POST:()=>h});var a=t(49303),i=t(88716),n=t(60670),o=t(87070),u=t(1066);class c{subscribeToTable(e,s,t){if(this.subscribers.has(e)||this.subscribers.set(e,new Set),this.subscribers.get(e).add(s),!this.channels.has(e)){let s=u.OQ.channel(`${e}_changes`).on("postgres_changes",{event:"*",schema:"public",table:e,...t&&{filter:`${t.column}=eq.${t.value}`}},s=>{let t=this.subscribers.get(e);t&&t.forEach(e=>e(s))}).subscribe();this.channels.set(e,s)}return()=>{let t=this.subscribers.get(e);if(t&&(t.delete(s),0===t.size)){let s=this.channels.get(e);s&&(u.OQ.removeChannel(s),this.channels.delete(e),this.subscribers.delete(e))}}}subscribeToUserUpdates(e,s){let t=[];return t.push(this.subscribeToTable("share_purchases",s,{column:"user_id",value:e})),t.push(this.subscribeToTable("crypto_payment_transactions",s,{column:"user_id",value:e})),t.push(this.subscribeToTable("commission_transactions",s,{column:"user_id",value:e})),t.push(this.subscribeToTable("referrals",s,{column:"referrer_user_id",value:e})),t.push(this.subscribeToTable("kyc_information",s,{column:"user_id",value:e})),()=>{t.forEach(e=>e())}}cleanup(){this.channels.forEach(e=>{u.OQ.removeChannel(e)}),this.channels.clear(),this.subscribers.clear()}constructor(){this.channels=new Map,this.subscribers=new Map}}class l{static async validateUserData(e){let s=[],t=[];try{let{data:r,error:a}=await u.OQ.from("telegram_users").select("*").eq("id",e).single();if(a||!r)return s.push("User not found in telegram_users table"),{isValid:!1,errors:s,warnings:t};let{data:i,error:n}=await u.OQ.from("share_purchases").select("*").eq("user_id",e);if(n)s.push(`Error fetching share purchases: ${n.message}`);else if(i){let e=i.reduce((e,s)=>e+(s.shares_purchased||0),0),t=i.reduce((e,s)=>e+(s.total_amount||0),0);e<0&&s.push("Negative total shares detected"),t<0&&s.push("Negative total amount detected")}let{data:o,error:c}=await u.OQ.from("commission_balances").select("*").eq("user_id",e).single();c&&"PGRST116"!==c.code?t.push(`Commission balance check failed: ${c.message}`):o&&o.usdt_balance<0&&s.push("Negative commission balance detected");let{data:l,error:h}=await u.OQ.from("crypto_payment_transactions").select("*").eq("user_id",e);h?t.push(`Payment transaction check failed: ${h.message}`):l&&l.filter(e=>"pending"===e.status).length>10&&t.push("High number of pending payments detected");let{data:d,error:p}=await u.OQ.from("referrals").select("*").eq("referrer_user_id",e);p&&t.push(`Referral data check failed: ${p.message}`)}catch(e){s.push(`Validation error: ${e}`)}return{isValid:0===s.length,errors:s,warnings:t}}static async validateBusinessLogic(){let e=[],s=[];try{let{data:t}=await u.OQ.from("share_purchases").select("id, user_id").not("user_id","in","(SELECT id FROM telegram_users)");t&&t.length>0&&e.push(`Found ${t.length} orphaned share purchases`);let{data:r}=await u.OQ.from("commission_balances").select("user_id, usdt_balance").lt("usdt_balance",0);r&&r.length>0&&e.push(`Found ${r.length} negative commission balances`);let{data:a}=await u.OQ.from("referrals").select("referred_user_id, count(*)").group("referred_user_id").having("count(*)","gt",1);a&&a.length>0&&s.push(`Found ${a.length} users with multiple referrers`);let{data:i}=await u.OQ.from("investment_phases").select("*").eq("is_active",!0);i&&i.length>1?e.push("Multiple active investment phases detected"):i&&0!==i.length||s.push("No active investment phase found")}catch(s){e.push(`Business logic validation error: ${s}`)}return{isValid:0===e.length,errors:e,warnings:s}}}async function h(e){try{let s=e.cookies.get("sb-access-token")?.value;if(!s)return o.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:r}=await u.OQ.auth.getUser(s);if(r||!t)return o.NextResponse.json({error:"Invalid or expired token"},{status:401});let{userId:a}=await e.json();if(!a)return o.NextResponse.json({error:"User ID is required"},{status:400});let i=await l.validateUserData(a),n=await l.validateBusinessLogic(),c=[],{data:h}=await u.OQ.from("share_purchases").select("id, user_id").eq("user_id",a).not("user_id","in","(SELECT id FROM telegram_users)");h&&h.length>0&&c.push({type:"orphaned_shares",count:h.length,description:"Share purchases without valid user references"});let{data:d}=await u.OQ.from("commission_balances").select("user_id, usdt_balance").eq("user_id",a).lt("usdt_balance",0);d&&d.length>0&&c.push({type:"negative_balance",count:d.length,description:"Negative commission balance detected"});let{data:p}=await u.OQ.from("referrals").select("referred_id, count(*)").eq("referred_id",a).group("referred_id").having("count(*)","gt",1);p&&p.length>0&&c.push({type:"duplicate_referrals",count:p.length,description:"User has multiple referrers"});let{data:m}=await u.OQ.from("crypto_payment_transactions").select("*").eq("user_id",a),{data:g}=await u.OQ.from("share_purchases").select("*").eq("user_id",a),_=m?.reduce((e,s)=>e+parseFloat(s.amount||0),0)||0,f=g?.reduce((e,s)=>e+parseFloat(s.total_amount||0),0)||0;Math.abs(_-f)>.01&&c.push({type:"payment_share_mismatch",paymentTotal:_,shareTotal:f,difference:_-f,description:"Payment total does not match share purchase total"});let b=new Date;b.setDate(b.getDate()-7);let{data:v}=await u.OQ.from("crypto_payment_transactions").select("id, created_at").eq("user_id",a).eq("status","pending").lt("created_at",b.toISOString());v&&v.length>0&&c.push({type:"stale_pending_payments",count:v.length,description:"Payments pending for more than 7 days"});let y=i.errors.length+i.warnings.length+n.errors.length+n.warnings.length+c.length;return o.NextResponse.json({success:!0,isConsistent:0===y,consistencyScore:Math.max(0,100-10*y),userValidation:i,businessValidation:n,consistencyIssues:c,summary:{totalIssues:y,criticalErrors:i.errors.length+n.errors.length,warnings:i.warnings.length+n.warnings.length,consistencyIssues:c.length},recommendations:function(e,s,t){let r=[];return e.errors.length>0&&r.push("Review and fix user data errors to ensure proper synchronization"),e.warnings.length>0&&r.push("Address user data warnings to improve data quality"),s.errors.length>0&&r.push("Fix business logic errors to maintain system integrity"),t.forEach(e=>{switch(e.type){case"orphaned_shares":r.push("Clean up orphaned share purchase records");break;case"negative_balance":r.push("Investigate and correct negative commission balances");break;case"duplicate_referrals":r.push("Resolve duplicate referral relationships");break;case"payment_share_mismatch":r.push("Reconcile payment and share purchase totals");break;case"stale_pending_payments":r.push("Review and process stale pending payments")}}),0===r.length?(r.push("Data synchronization is working correctly"),r.push("Continue monitoring for any future inconsistencies")):(r.push("Run validation tests regularly to catch issues early"),r.push("Consider implementing automated data cleanup processes")),r}(i,n,c),timestamp:new Date().toISOString()})}catch(e){return console.error("Data validation error:",e),o.NextResponse.json({error:"Failed to validate data synchronization"},{status:500})}}new c;let d=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/data-sync/validate/route",pathname:"/api/data-sync/validate",filename:"route",bundlePath:"app/api/data-sync/validate/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\data-sync\\validate\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:g}=d,_="/api/data-sync/validate/route";function f(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},1066:(e,s,t)=>{"use strict";t.d(s,{OQ:()=>i});var r=t(72438);let a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let i=(0,r.eI)("https://fgubaqoftdeefcakejwu.supabase.co",a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,169],()=>t(87061));module.exports=r})();