(()=>{var e={};e.id=784,e.ids=[784],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},78925:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>_,staticGenerationAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>d});var o=r(49303),a=r(88716),n=r(60670),i=r(87070),u=r(1066);async function c(e){try{let t=e.cookies.get("sb-access-token")?.value;if(!t)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:r},error:s}=await u.OQ.auth.getUser(t);if(s||!r)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let o=r.user_metadata?.telegram_id;if(!o)return i.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:a,error:n}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",o).single();if(n||!a)return i.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:c}=await u.OQ.from("terms_acceptance").select("*").eq("user_id",a.id).single(),d=!!a.country,{data:p}=await u.OQ.from("kyc_information").select("*").eq("user_id",a.id).single(),l=p?.kyc_status||"incomplete",m=function(e,t,r,s){let o=[e,t,r],a=o.filter(Boolean).length,n=o.length,i="complete",u=!1;return e?t?r?(i="complete",u=!0):i="kyc":i="country":i="terms",{hasAcceptedTerms:e,hasSelectedCountry:t,hasCompletedKYC:r,kycStatus:s,completionPercentage:Math.round(a/n*100),nextStep:i,canAccessDashboard:u}}(!!c,d,!!p,l);return i.NextResponse.json({success:!0,status:m,details:{termsAcceptance:c?{accepted_at:c.accepted_at,version:c.version}:null,country:a.country,kycInfo:p?{status:p.kyc_status,submitted_at:p.created_at,verified_at:p.verified_at}:null}})}catch(e){return console.error("Get onboarding status error:",e),i.NextResponse.json({error:"Failed to get onboarding status"},{status:500})}}async function d(e){try{let t;let r=e.cookies.get("sb-access-token")?.value;if(!r)return i.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:s},error:o}=await u.OQ.auth.getUser(r);if(o||!s)return i.NextResponse.json({error:"Invalid or expired token"},{status:401});let{step:a,data:n}=await e.json(),c=s.user_metadata?.telegram_id;if(!c)return i.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:d,error:p}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",c).single();if(p||!d)return i.NextResponse.json({error:"Telegram user profile not found"},{status:404});switch(a){case"terms":let{error:l}=await u.OQ.from("terms_acceptance").insert({user_id:d.id,terms_type:"general",version:n.version||"1.0",accepted_at:new Date().toISOString()});if(l)throw Error(l.message);t={step:"terms",completed:!0};break;case"country":let{error:m}=await u.OQ.from("telegram_users").update({country:n.country,updated_at:new Date().toISOString()}).eq("id",d.id);if(m)throw Error(m.message);t={step:"country",completed:!0,country:n.country};break;case"kyc":let{error:_}=await u.OQ.from("kyc_information").insert({user_id:d.id,first_name:n.firstName,last_name:n.lastName,id_type:n.idType,id_number_encrypted:n.idNumber,id_number_hash:n.idNumber,phone_number:n.phoneNumber,email_address:n.emailAddress,street_address:n.address,city:n.city,postal_code:n.postalCode,country_code:d.country||"ZA",country_name:d.country||"South Africa",data_consent_given:n.acceptedPrivacy,privacy_policy_accepted:n.acceptedPrivacy,kyc_status:"pending",created_by_telegram_id:d.telegram_id});if(_)throw Error(_.message);t={step:"kyc",completed:!0,status:"pending"};break;default:return i.NextResponse.json({error:"Invalid onboarding step"},{status:400})}return i.NextResponse.json({success:!0,result:t})}catch(e){return console.error("Update onboarding step error:",e),i.NextResponse.json({error:"Failed to update onboarding step"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/onboarding/status/route",pathname:"/api/onboarding/status",filename:"route",bundlePath:"app/api/onboarding/status/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\onboarding\\status\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:_}=p,g="/api/onboarding/status/route";function f(){return(0,n.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:m})}},1066:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>a});var s=r(72438);let o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!o)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let a=(0,s.eI)("https://fgubaqoftdeefcakejwu.supabase.co",o,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,169],()=>r(78925));module.exports=s})();