(()=>{var e={};e.id=322,e.ids=[322],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},47413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>_,staticGenerationAsyncStorage:()=>p});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>l});var a=t(49303),o=t(88716),i=t(60670),n=t(87070),u=t(1066);async function c(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return n.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await u.OQ.auth.getUser(r);if(s||!t)return n.NextResponse.json({error:"Invalid or expired token"},{status:401});let a=t.user_metadata?.telegram_id;if(!a)return n.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:o,error:i}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",a).single();if(i||!o)return n.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:c,error:l}=await u.OQ.from("referrals").select(`
        id,
        referred_id,
        referral_code,
        commission_rate,
        total_commission,
        status,
        created_at,
        telegram_users!referrals_referred_id_fkey (
          id,
          telegram_id,
          username,
          first_name,
          last_name,
          country,
          created_at
        )
      `).eq("referrer_id",o.id).order("created_at",{ascending:!1});if(l)return n.NextResponse.json({error:"Failed to get referrals"},{status:500});let{data:d,error:m}=await u.OQ.from("commission_balances").select("*").eq("user_id",o.id).single(),{data:p,error:_}=await u.OQ.from("commission_transactions").select("*").eq("referrer_id",o.id).order("created_at",{ascending:!1}).limit(10),f=c?.length||0,g=c?.filter(e=>"active"===e.status).length||0,h=c?.map(e=>e.referred_id)||[],x=0,w=0;if(h.length>0){let{data:e}=await u.OQ.from("share_purchases").select("user_id, total_amount").in("user_id",h).eq("status","active");e&&(x=e.reduce((e,r)=>e+parseFloat(r.total_amount),0),w=.15*x)}let v=f>0?x/f:0,N=c?.map(e=>({id:e.id,firstName:e.telegram_users?.first_name||"",lastName:e.telegram_users?.last_name||"",username:e.telegram_users?.username||"",country:e.telegram_users?.country||"",joinedDate:e.created_at,status:e.status,commissionRate:e.commission_rate,totalCommission:e.total_commission||0}))||[];return n.NextResponse.json({success:!0,referralCode:a.toString(),statistics:{totalReferrals:f,activeReferrals:g,totalCommissionEarned:w,totalInvestmentVolume:x,averageInvestmentPerReferral:v,conversionRate:f>0?g/f*100:0},commissionBalance:d||{usdt_balance:0,share_balance:0,total_earned:0,total_withdrawn:0,pending_withdrawals:0},referrals:N,recentTransactions:p||[]})}catch(e){return console.error("Get referrals error:",e),n.NextResponse.json({error:"Failed to get referral data"},{status:500})}}async function l(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return n.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:s}=await u.OQ.auth.getUser(r);if(s||!t)return n.NextResponse.json({error:"Invalid or expired token"},{status:401});let{action:a,amount:o,network:i,walletAddress:c}=await e.json(),l=t.user_metadata?.telegram_id;if(!l)return n.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:d,error:m}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",l).single();if(m||!d)return n.NextResponse.json({error:"Telegram user profile not found"},{status:404});switch(a){case"withdraw_commission":if(!o||o<25)return n.NextResponse.json({error:"Minimum withdrawal amount is $25"},{status:400});if(!c||!i)return n.NextResponse.json({error:"Wallet address and network are required"},{status:400});let{data:p}=await u.OQ.from("commission_balances").select("usdt_balance").eq("user_id",d.id).single();if(!p||p.usdt_balance<o)return n.NextResponse.json({error:"Insufficient commission balance"},{status:400});let{error:_}=await u.OQ.from("commission_withdrawals").insert({user_id:d.id,amount:o,wallet_address:c,network:i,status:"pending",created_at:new Date().toISOString()});if(_)return n.NextResponse.json({error:"Failed to create withdrawal request"},{status:500});return n.NextResponse.json({success:!0,message:"Withdrawal request submitted successfully"});case"convert_to_shares":if(!o||o<=0)return n.NextResponse.json({error:"Invalid conversion amount"},{status:400});let{data:f}=await u.OQ.from("commission_balances").select("usdt_balance").eq("user_id",d.id).single();if(!f||f.usdt_balance<o)return n.NextResponse.json({error:"Insufficient commission balance"},{status:400});let{data:g}=await u.OQ.from("investment_phases").select("price_per_share").eq("is_active",!0).single(),h=g?.price_per_share||1,x=Math.floor(o/h),{error:w}=await u.OQ.from("share_purchases").insert({user_id:d.id,package_name:"Commission Conversion",shares_purchased:x,total_amount:o,payment_method:"Commission Conversion",status:"active",created_at:new Date().toISOString()});if(w)return n.NextResponse.json({error:"Failed to convert commission to shares"},{status:500});let{error:v}=await u.OQ.from("commission_balances").update({usdt_balance:f.usdt_balance-o,share_balance:(f.share_balance||0)+x,updated_at:new Date().toISOString()}).eq("user_id",d.id);return v&&console.error("Failed to update commission balance:",v),n.NextResponse.json({success:!0,message:`Successfully converted $${o} to ${x} shares`,sharesReceived:x});default:return n.NextResponse.json({error:"Invalid action"},{status:400})}}catch(e){return console.error("Referral action error:",e),n.NextResponse.json({error:"Failed to process referral action"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/referrals/route",pathname:"/api/referrals",filename:"route",bundlePath:"app/api/referrals/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\referrals\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:p,serverHooks:_}=d,f="/api/referrals/route";function g(){return(0,i.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:p})}},1066:(e,r,t)=>{"use strict";t.d(r,{OQ:()=>o});var s=t(72438);let a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let o=(0,s.eI)("https://fgubaqoftdeefcakejwu.supabase.co",a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,169],()=>t(47413));module.exports=s})();