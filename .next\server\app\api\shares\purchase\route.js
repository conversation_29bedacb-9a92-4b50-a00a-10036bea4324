(()=>{var e={};e.id=237,e.ids=[237],e.modules={20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},58359:()=>{},93739:()=>{},51032:(e,r,s)=>{"use strict";s.r(r),s.d(r,{originalPathname:()=>f,patchFetch:()=>_,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var t={};s.r(t),s.d(t,{GET:()=>m,POST:()=>l});var a=s(49303),n=s(88716),i=s(60670),o=s(87070),u=s(1066);async function l(e){try{var r;let s=e.cookies.get("sb-access-token")?.value;if(!s)return o.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:t},error:a}=await u.OQ.auth.getUser(s);if(a||!t)return o.NextResponse.json({error:"Invalid or expired token"},{status:401});let{amount:n,paymentMethod:i,network:l,senderWallet:m,transactionHash:p,screenshotUrl:c}=await e.json(),d=t.user_metadata?.telegram_id;if(!d)return o.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:h,error:f}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",d).single();if(f||!h)return o.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:_,error:g}=await u.OQ.from("investment_phases").select("*").eq("is_active",!0).single();if(g||!_)return o.NextResponse.json({error:"No active investment phase found"},{status:400});let x=parseFloat(n);if(isNaN(x)||x<=0)return o.NextResponse.json({error:"Invalid purchase amount"},{status:400});let v=function(e,r,s=!1){let t=[],a=[];(!e||e<=0)&&t.push("Amount must be greater than 0"),(!r||r<=0)&&t.push("Share price must be greater than 0"),e<25&&t.push("Minimum investment amount is $25"),e>1e4&&a.push("Large investment amount - please verify");let n=e/r,i=s?n:Math.floor(n),o=i*r,u=e-o;return u>0&&!s&&a.push(`$${u.toFixed(2)} will be refunded as it doesn't purchase a full share`),0===i&&t.push(`Amount too small to purchase shares at $${r} per share`),{amount:e,sharePrice:r,sharesAmount:i,totalCost:o,remainingAmount:u,isValid:0===t.length,errors:t,warnings:a}}(x,_.price_per_share);if(!v.isValid)return o.NextResponse.json({error:"Invalid share calculation",details:v.errors},{status:400});let A=function(e,r){let s=[],t=[];r.is_active||s.push("Investment phase is not currently active");let a=r.shares_available-r.shares_sold;return e>a&&s.push(`Insufficient shares available. Requested: ${e}, Available: ${a}`),a<100&&t.push("Phase is nearly sold out - limited shares remaining"),r.end_date&&new Date>new Date(r.end_date)&&s.push("Investment phase has ended"),{isValid:0===s.length,errors:s,warnings:t}}(v.sharesAmount,_);if(!A.isValid)return o.NextResponse.json({error:"Phase validation failed",details:A.errors},{status:400});let{data:N}=await u.OQ.from("share_purchases").select("total_amount").eq("user_id",h.id),w=N?.reduce((e,r)=>e+parseFloat(r.total_amount),0)||0,R=function(e,r,s=0){let t=[],a=[];return e<25&&t.push("Minimum purchase amount is $25"),e>1e4&&t.push("Maximum purchase amount is $10,000 per transaction"),s+e>5e4&&a.push("Large total investment amount - may require additional verification"),{isValid:0===t.length,errors:t,warnings:a}}(x,h.id,w);if(!R.isValid)return o.NextResponse.json({error:"Purchase limits validation failed",details:R.errors},{status:400});let I=(r=x,[{name:"Shovel",minAmount:25,maxAmount:74,shares:25,roi:12,description:"Entry-level mining package"},{name:"Miner",minAmount:75,maxAmount:249,shares:75,roi:15,description:"Small-scale mining operation"},{name:"Excavator",minAmount:250,maxAmount:499,shares:250,roi:18,description:"Medium-scale mining operation"},{name:"Crusher",minAmount:500,maxAmount:749,shares:500,roi:20,description:"Large-scale mining operation"},{name:"Refinery",minAmount:750,maxAmount:999,shares:750,roi:22,description:"Industrial mining operation"},{name:"Aureus",minAmount:1e3,maxAmount:2499,shares:1e3,roi:25,description:"Premium mining package"},{name:"Titan",minAmount:2500,maxAmount:4999,shares:2500,roi:28,description:"Elite mining operation"},{name:"Empire",minAmount:5e3,maxAmount:1e4,shares:5e3,roi:30,description:"Ultimate mining empire"}].find(e=>r>=e.minAmount&&r<=e.maxAmount)||{name:"Custom Package",minAmount:r,maxAmount:r,shares:Math.floor(r),roi:20,description:"Custom investment package"}),j={user_id:h.id,amount:v.totalCost,currency:"ZAR"===i?"ZAR":"USDT",network:"ZAR"===i?"BANK_TRANSFER":l,sender_wallet:m||"",receiver_wallet:"",transaction_hash:p||null,screenshot_url:c||null,status:"pending",admin_notes:`Package: ${I.name}, Shares: ${v.sharesAmount}`};if("ZAR"===i)j.receiver_wallet="Aureus Alliance Holdings Bank Account";else{let{data:e}=await u.OQ.from("company_wallets").select("wallet_address").eq("network",l).eq("is_active",!0).single();e&&(j.receiver_wallet=e.wallet_address)}let{data:q,error:y}=await u.OQ.from("crypto_payment_transactions").insert(j).select().single();if(y)return o.NextResponse.json({error:"Failed to create payment transaction",details:y.message},{status:500});let k=null,{data:S}=await u.OQ.from("referrals").select("referrer_id").eq("referred_id",h.id).single();if(S){let e=function(e,r,s=15){return{usdtCommission:Math.round(s/100*e*100)/100,shareCommission:Math.round(s/100*r*100)/100}}(v.totalCost,v.sharesAmount);k={referrer_id:S.referrer_id,referred_id:h.id,share_purchase_amount:v.totalCost,usdt_commission:e.usdtCommission,share_commission:e.shareCommission,commission_rate:15,status:"pending",payment_date:new Date().toISOString()}}return o.NextResponse.json({success:!0,payment:{id:q.id,amount:q.amount,currency:q.currency,network:q.network,status:q.status},shares:{amount:v.sharesAmount,pricePerShare:_.price_per_share,totalCost:v.totalCost,package:I.name},commission:k,phase:{name:_.phase_name,number:_.phase_number},warnings:[...v.warnings,...A.warnings,...R.warnings]})}catch(e){return console.error("Share purchase error:",e),o.NextResponse.json({error:"Failed to process share purchase"},{status:500})}}async function m(e){try{let r=e.cookies.get("sb-access-token")?.value;if(!r)return o.NextResponse.json({error:"No access token provided"},{status:401});let{data:{user:s},error:t}=await u.OQ.auth.getUser(r);if(t||!s)return o.NextResponse.json({error:"Invalid or expired token"},{status:401});let a=s.user_metadata?.telegram_id;if(!a)return o.NextResponse.json({error:"No Telegram ID found"},{status:400});let{data:n,error:i}=await u.OQ.from("telegram_users").select("*").eq("telegram_id",a).single();if(i||!n)return o.NextResponse.json({error:"Telegram user profile not found"},{status:404});let{data:l,error:m}=await u.OQ.from("investment_phases").select("*").eq("is_active",!0).single();if(m||!l)return o.NextResponse.json({error:"No active investment phase found"},{status:400});let{data:p,error:c}=await u.OQ.from("share_purchases").select("*").eq("user_id",n.id).order("created_at",{ascending:!1});if(c)return o.NextResponse.json({error:"Failed to get share purchases"},{status:500});let d=p?.reduce((e,r)=>e+(r.shares_purchased||0),0)||0,h=p?.reduce((e,r)=>e+parseFloat(r.total_amount||0),0)||0;return o.NextResponse.json({success:!0,currentPhase:{name:l.phase_name,number:l.phase_number,pricePerShare:l.price_per_share,sharesAvailable:l.shares_available,sharesSold:l.shares_sold,remainingShares:l.shares_available-l.shares_sold},userShares:{totalShares:d,totalInvested:h,purchases:p||[]}})}catch(e){return console.error("Get share purchase info error:",e),o.NextResponse.json({error:"Failed to get share purchase information"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/shares/purchase/route",pathname:"/api/shares/purchase",filename:"route",bundlePath:"app/api/shares/purchase/route"},resolvedPagePath:"C:\\xampp\\htdocs\\aureus_africa\\app\\api\\shares\\purchase\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:h}=p,f="/api/shares/purchase/route";function _(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}},1066:(e,r,s)=>{"use strict";s.d(r,{OQ:()=>n});var t=s(72438);let a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let n=(0,t.eI)("https://fgubaqoftdeefcakejwu.supabase.co",a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[276,169],()=>s(51032));module.exports=t})();