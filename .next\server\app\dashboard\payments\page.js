(()=>{var e={};e.id=996,e.ids=[996],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},278:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c}),t(87730),t(13321),t(73375),t(35866);var r=t(23191),a=t(88716),n=t(37922),i=t.n(n),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c=["",{children:["dashboard",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87730)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\payments\\page.tsx"],m="/dashboard/payments/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/payments/page",pathname:"/dashboard/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2766:(e,s,t)=>{Promise.resolve().then(t.bind(t,58767))},25955:(e,s,t)=>{Promise.resolve().then(t.bind(t,68306))},58767:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(10326),a=t(84979),n=t(30668);function i({children:e}){let{user:s,signOut:t}=(0,a.a)(),i=async()=>{await t()};return r.jsx(n.Z,{children:(0,r.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[r.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:r.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),r.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s?.telegram_profile&&(0,r.jsxs)("div",{className:"text-sm",children:[r.jsx("span",{className:"text-gray-300",children:"Welcome, "}),r.jsx("span",{className:"text-yellow-400 font-medium",children:s.telegram_profile.first_name})]}),r.jsx("button",{onClick:i,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),r.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},68306:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(10326),a=t(17577),n=t(35047),i=t(84979);t(91579);var l=t(30668);function d(){let{user:e}=(0,i.a)();(0,n.useSearchParams)();let[s,t]=(0,a.useState)([]),[d,c]=(0,a.useState)(!0),[o,m]=(0,a.useState)(!1),x=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},u=e=>{switch(e){case"approved":return"✅";case"pending":return"⏳";case"rejected":return"❌";default:return"❓"}},h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return d?r.jsx(l.Z,{requireTerms:!0,requireCountry:!0,children:(0,r.jsxs)("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),r.jsx("div",{className:"h-64 bg-gray-200 rounded mb-4"})]})}):r.jsx(l.Z,{requireTerms:!0,requireCountry:!0,children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-xl p-6 mb-8 text-white",children:[r.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Payment History"}),r.jsx("p",{className:"text-blue-100",children:"Track your share purchase payments and their status"})]}),o&&r.jsx("div",{className:"mb-6 p-4 bg-green-100 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-green-600 text-xl mr-2",children:"✅"}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium text-green-800",children:"Payment Submitted Successfully!"}),r.jsx("p",{className:"text-green-700 text-sm",children:"Your payment has been submitted for admin review. You'll be notified once it's processed."})]})]})}),(0,r.jsxs)("div",{className:"mb-6 flex flex-wrap gap-4",children:[r.jsx("a",{href:"/dashboard/purchase",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Make New Purchase"}),r.jsx("a",{href:"/dashboard",className:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Back to Dashboard"})]}),0===s.length?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[r.jsx("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCB3"}),r.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-2",children:"No Payments Yet"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"You haven't made any share purchase payments yet."}),r.jsx("a",{href:"/dashboard/purchase",className:"inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200",children:"Make Your First Purchase"})]}):r.jsx("div",{className:"space-y-4",children:s.map(e=>r.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[r.jsx("span",{className:"text-2xl mr-2",children:"USDT"===e.currency?"₮":"ZAR"===e.currency?"R":"$"}),(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-800",children:[e.currency," ",e.amount.toFixed(2)]}),(0,r.jsxs)("span",{className:`ml-3 px-2 py-1 rounded-full text-xs font-medium ${x(e.status)}`,children:[u(e.status)," ",e.status.toUpperCase()]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:"Network:"})," ",e.network]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:"Date:"})," ",h(e.created_at)]}),e.transaction_hash&&(0,r.jsxs)("div",{className:"md:col-span-2",children:[r.jsx("span",{className:"font-medium",children:"Transaction Hash:"}),r.jsx("span",{className:"font-mono text-xs ml-1 break-all",children:e.transaction_hash})]}),e.admin_notes&&(0,r.jsxs)("div",{className:"md:col-span-2",children:[r.jsx("span",{className:"font-medium",children:"Notes:"})," ",e.admin_notes]})]})]}),(0,r.jsxs)("div",{className:"mt-4 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-2",children:[e.screenshot_url&&r.jsx("a",{href:e.screenshot_url,target:"_blank",rel:"noopener noreferrer",className:"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium transition-colors duration-200 text-center",children:"View Proof"}),"pending"===e.status&&r.jsx("div",{className:"bg-yellow-50 text-yellow-700 px-3 py-2 rounded text-sm text-center",children:"Awaiting Review"}),"approved"===e.status&&r.jsx("div",{className:"bg-green-50 text-green-700 px-3 py-2 rounded text-sm text-center",children:"Payment Approved"}),"rejected"===e.status&&r.jsx("div",{className:"bg-red-50 text-red-700 px-3 py-2 rounded text-sm text-center",children:"Payment Rejected"})]})]})},e.id))}),(0,r.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-6",children:[r.jsx("h3",{className:"text-lg font-semibold mb-4 text-gray-800",children:"Payment Status Guide"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("span",{className:"text-yellow-500 text-lg mr-2",children:"⏳"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800",children:"Pending"}),r.jsx("p",{className:"text-gray-600",children:"Payment submitted and awaiting admin review"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("span",{className:"text-green-500 text-lg mr-2",children:"✅"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800",children:"Approved"}),r.jsx("p",{className:"text-gray-600",children:"Payment verified and shares allocated to your account"})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[r.jsx("span",{className:"text-red-500 text-lg mr-2",children:"❌"}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-gray-800",children:"Rejected"}),r.jsx("p",{className:"text-gray-600",children:"Payment could not be verified - contact support"})]})]})]})]})]})})}},30668:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});var r=t(10326),a=t(17577),n=t(35047),i=t(84979);function l({children:e,requireTerms:s=!1,requireCountry:t=!1,requireKYC:l=!1}){let{user:d,loading:c,checkOnboardingStatus:o}=(0,i.a)();(0,n.useRouter)();let[m,x]=(0,a.useState)(!1),[u,h]=(0,a.useState)(!0);return u||c?r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),r.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):m?r.jsx(r.Fragment,{children:e}):null}},35047:(e,s,t)=>{"use strict";var r=t(77389);t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},13321:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},87730:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\payments\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[276,728,797],()=>t(278));module.exports=r})();