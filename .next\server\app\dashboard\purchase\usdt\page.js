(()=>{var e={};e.id=427,e.ids=[427],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},69924:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(15544),t(13321),t(73375),t(35866);var s=t(23191),a=t(88716),n=t(37922),i=t.n(n),o=t(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["dashboard",{children:["purchase",{children:["usdt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,15544)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\usdt\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,13321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\usdt\\page.tsx"],u="/dashboard/purchase/usdt/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/purchase/usdt/page",pathname:"/dashboard/purchase/usdt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2766:(e,r,t)=>{Promise.resolve().then(t.bind(t,58767))},98438:(e,r,t)=>{Promise.resolve().then(t.bind(t,22347))},58767:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(10326),a=t(84979),n=t(30668);function i({children:e}){let{user:r,signOut:t}=(0,a.a)(),i=async()=>{await t()};return s.jsx(n.Z,{children:(0,s.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[s.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:s.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),s.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[r?.telegram_profile&&(0,s.jsxs)("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-300",children:"Welcome, "}),s.jsx("span",{className:"text-yellow-400 font-medium",children:r.telegram_profile.first_name})]}),s.jsx("button",{onClick:i,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),s.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},22347:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(10326),a=t(17577),n=t(35047),i=t(84979),o=t(91579);t(20286);var l=t(30668);let d=[{code:"ETH",name:"Ethereum (ETH)",fee:"High fees, secure"},{code:"BSC",name:"Binance Smart Chain (BSC)",fee:"Low fees, fast"},{code:"POLYGON",name:"Polygon (MATIC)",fee:"Very low fees, fast"},{code:"TRON",name:"TRON (TRX)",fee:"Low fees, popular in Asia"}],c={ETH:"******************************************",BSC:"******************************************",POLYGON:"******************************************",TRON:"TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7"};function u(){let{user:e}=(0,i.a)(),r=(0,n.useRouter)();(0,n.useSearchParams)();let[t,u]=(0,a.useState)(""),[m,p]=(0,a.useState)(""),[h,x]=(0,a.useState)(""),[b,f]=(0,a.useState)(""),[g,y]=(0,a.useState)(""),[j,w]=(0,a.useState)(null),[v,N]=(0,a.useState)(!1),[S,P]=(0,a.useState)(null),C=async r=>{try{let t=r.name.split(".").pop(),s=`${e?.telegram_profile?.telegram_id}_${Date.now()}.${t}`,{data:a,error:n}=await o.OQ.storage.from("payment-proofs").upload(s,r);if(n)return console.error("Upload error:",n),null;let{data:{publicUrl:i}}=o.OQ.storage.from("payment-proofs").getPublicUrl(s);return i}catch(e){return console.error("Upload error:",e),null}},_=async s=>{if(s.preventDefault(),!e?.telegram_profile?.id){P("User profile not found");return}if(!b||!h){P("Please fill in all required fields");return}N(!0),P(null);try{let s=null;if(j&&!(s=await C(j)))throw Error("Failed to upload screenshot");let{error:a}=await o.OQ.from("crypto_payment_transactions").insert({user_id:e.telegram_profile.id,amount:parseFloat(t),currency:"USDT",network:h,sender_wallet:b,receiver_wallet:c[h],transaction_hash:g||null,screenshot_url:s,status:"pending"});if(a)throw Error(a.message);r.push("/dashboard/payments?status=submitted")}catch(e){console.error("Payment submission error:",e),P("Failed to submit payment. Please try again.")}finally{N(!1)}},q=h?c[h]:"";return s.jsx(l.Z,{requireTerms:!0,requireCountry:!0,children:s.jsx("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"USDT Payment"}),s.jsx("p",{className:"text-gray-600",children:"Complete your share purchase using USDT cryptocurrency"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6 border border-blue-100",children:[s.jsx("h2",{className:"font-semibold text-blue-800 mb-2",children:"Purchase Summary"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-blue-700",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Amount:"}),(0,s.jsxs)("span",{children:["$",parseFloat(t||"0").toFixed(2)," USD"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Shares:"}),s.jsx("span",{children:parseInt(m||"0").toLocaleString()})]})]})]}),(0,s.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"USDT Network *"}),(0,s.jsxs)("select",{value:h,onChange:e=>x(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",children:[s.jsx("option",{value:"",children:"Select network"}),d.map(e=>(0,s.jsxs)("option",{value:e.code,children:[e.name," - ",e.fee]},e.code))]})]}),h&&(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4 border border-yellow-200",children:[s.jsx("h3",{className:"font-medium text-yellow-800 mb-2",children:"Send USDT to this address:"}),s.jsx("div",{className:"bg-white p-3 rounded border font-mono text-sm break-all",children:q}),(0,s.jsxs)("p",{className:"text-yellow-700 text-sm mt-2",children:["⚠️ Only send USDT on ",d.find(e=>e.code===h)?.name," network to this address"]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your Wallet Address *"}),s.jsx("input",{type:"text",value:b,onChange:e=>f(e.target.value),placeholder:"Enter the wallet address you're sending from",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),s.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"This helps us verify the transaction"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Transaction Hash (Optional)"}),s.jsx("input",{type:"text",value:g,onChange:e=>y(e.target.value),placeholder:"Enter transaction hash if available",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),s.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"You can add this later if not available now"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Payment Screenshot (Optional)"}),s.jsx("input",{type:"file",accept:"image/*",onChange:e=>{let r=e.target.files?.[0];if(r){if(!r.type.startsWith("image/")){P("Please upload an image file");return}if(r.size>5242880){P("File size must be less than 5MB");return}w(r),P(null)}},className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),s.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Upload a screenshot of your transaction for faster processing"})]}),S&&s.jsx("div",{className:"p-3 bg-red-100 text-red-700 rounded-md text-sm",children:S}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("button",{type:"button",onClick:()=>r.push("/dashboard/purchase"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Back"}),s.jsx("button",{type:"submit",disabled:v||!h||!b,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${!v&&h&&b?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-300 text-white cursor-not-allowed"}`,children:v?"Submitting...":"Submit Payment"})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-4",children:[s.jsx("h3",{className:"font-medium text-gray-800 mb-2",children:"Payment Instructions"}),(0,s.jsxs)("ol",{className:"text-sm text-gray-600 space-y-1 list-decimal list-inside",children:[s.jsx("li",{children:"Select the USDT network you want to use"}),(0,s.jsxs)("li",{children:["Send exactly $",t," worth of USDT to the displayed address"]}),s.jsx("li",{children:"Enter your wallet address and transaction details"}),s.jsx("li",{children:"Submit this form for admin review"}),s.jsx("li",{children:"You'll be notified once your payment is approved"})]})]})]})})})}},30668:(e,r,t)=>{"use strict";t.d(r,{Z:()=>o});var s=t(10326),a=t(17577),n=t(35047),i=t(84979);function o({children:e,requireTerms:r=!1,requireCountry:t=!1,requireKYC:o=!1}){let{user:l,loading:d,checkOnboardingStatus:c}=(0,i.a)();(0,n.useRouter)();let[u,m]=(0,a.useState)(!1),[p,h]=(0,a.useState)(!0);return p||d?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):u?s.jsx(s.Fragment,{children:e}):null}},20286:(e,r,t)=>{"use strict";t.d(r,{B_:()=>d,f6:()=>l,fj:()=>o,od:()=>s,pD:()=>a});let s=[{code:"ZA",name:"South Africa",region:"africa"},{code:"SZ",name:"Eswatini",region:"africa"},{code:"NA",name:"Namibia",region:"africa"},{code:"BW",name:"Botswana",region:"africa"},{code:"ZW",name:"Zimbabwe",region:"africa"},{code:"MZ",name:"Mozambique",region:"africa"},{code:"LS",name:"Lesotho",region:"africa"},{code:"US",name:"United States",region:"western"},{code:"GB",name:"United Kingdom",region:"western"},{code:"CA",name:"Canada",region:"western"},{code:"AU",name:"Australia",region:"western"},{code:"NZ",name:"New Zealand",region:"western"},{code:"SG",name:"Singapore",region:"asia"},{code:"AE",name:"United Arab Emirates",region:"asia"},{code:"OTHER",name:"Other Country",region:"other"}],a=e=>{let r=s.find(r=>r.code===e);return r?r.name:"Unknown Country"},n=e=>["ZA","SZ","NA"].includes(e),i=e=>!0,o=e=>{let r=[];return i(e)&&r.push("USDT"),n(e)&&r.push("ZAR"),r},l=e=>["US","GB","CA"].includes(e),d=e=>n(e)?"You can pay using ZAR bank transfer or USDT cryptocurrency.":"You can pay using USDT cryptocurrency on multiple networks."},35047:(e,r,t)=>{"use strict";var s=t(77389);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},13321:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},15544:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\purchase\usdt\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[276,728,797],()=>t(69924));module.exports=s})();