(()=>{var e={};e.id=103,e.ids=[103],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},54649:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(92632),s(13321),s(73375),s(35866);var a=s(23191),t=s(88716),n=s(37922),l=s.n(n),i=s(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let c=["",{children:["dashboard",{children:["purchase",{children:["zar",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,92632)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\zar\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,13321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\purchase\\zar\\page.tsx"],u="/dashboard/purchase/zar/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/dashboard/purchase/zar/page",pathname:"/dashboard/purchase/zar",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2766:(e,r,s)=>{Promise.resolve().then(s.bind(s,58767))},171:(e,r,s)=>{Promise.resolve().then(s.bind(s,76533))},58767:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var a=s(10326),t=s(84979),n=s(30668);function l({children:e}){let{user:r,signOut:s}=(0,t.a)(),l=async()=>{await s()};return a.jsx(n.Z,{children:(0,a.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[a.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:a.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),a.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[r?.telegram_profile&&(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("span",{className:"text-gray-300",children:"Welcome, "}),a.jsx("span",{className:"text-yellow-400 font-medium",children:r.telegram_profile.first_name})]}),a.jsx("button",{onClick:l,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),a.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},76533:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var a=s(10326),t=s(17577),n=s(35047),l=s(84979),i=s(91579),o=s(30668);let c={bankName:"First National Bank (FNB)",accountName:"AUREUS ALLIANCE HOLDINGS (PTY) LTD",accountNumber:"***********",branchCode:"250655",accountType:"Business Current Account"};function d(){let{user:e}=(0,l.a)(),r=(0,n.useRouter)();(0,n.useSearchParams)();let[s,d]=(0,t.useState)(""),[u,m]=(0,t.useState)(""),[p,x]=(0,t.useState)(""),[h,b]=(0,t.useState)(""),[f,g]=(0,t.useState)(""),[y,j]=(0,t.useState)(""),[N,v]=(0,t.useState)(""),[w,_]=(0,t.useState)(null),[P,S]=(0,t.useState)(!1),[A,k]=(0,t.useState)(null),q=async r=>{try{let s=r.name.split(".").pop(),a=`zar_${e?.telegram_profile?.telegram_id}_${Date.now()}.${s}`,{data:t,error:n}=await i.OQ.storage.from("payment-proofs").upload(a,r);if(n)return console.error("Upload error:",n),null;let{data:{publicUrl:l}}=i.OQ.storage.from("payment-proofs").getPublicUrl(a);return l}catch(e){return console.error("Upload error:",e),null}},C=async a=>{if(a.preventDefault(),!e?.telegram_profile?.id){k("User profile not found");return}if(!h||!f||!y||!w){k("Please fill in all required fields and upload proof of payment");return}S(!0),k(null);try{let a=await q(w);if(!a)throw Error("Failed to upload proof of payment");let{error:t}=await i.OQ.from("crypto_payment_transactions").insert({user_id:e.telegram_profile.id,amount:parseFloat(s),currency:"ZAR",network:"BANK_TRANSFER",sender_wallet:`${h} - ${f} (${y})`,receiver_wallet:`${c.accountName} - ${c.accountNumber}`,transaction_hash:N||null,screenshot_url:a,status:"pending",admin_notes:`ZAR Amount: R${p}, Reference: ${N||"N/A"}`});if(t)throw Error(t.message);r.push("/dashboard/payments?status=submitted")}catch(e){console.error("Payment submission error:",e),k("Failed to submit payment. Please try again.")}finally{S(!1)}};return a.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:a.jsx("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"ZAR Bank Transfer"}),a.jsx("p",{className:"text-gray-600",children:"Complete your share purchase using South African Rand bank transfer"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6 border border-blue-100",children:[a.jsx("h2",{className:"font-semibold text-blue-800 mb-2",children:"Purchase Summary"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-blue-700",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"USD Amount:"}),(0,a.jsxs)("span",{children:["$",parseFloat(s||"0").toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"ZAR Amount (approx):"}),(0,a.jsxs)("span",{children:["R",parseFloat(p||"0").toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{children:"Shares:"}),a.jsx("span",{children:parseInt(u||"0").toLocaleString()})]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 mb-6 border border-green-200",children:[a.jsx("h3",{className:"font-medium text-green-800 mb-3",children:"Company Bank Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Bank:"}),a.jsx("span",{className:"font-medium",children:c.bankName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Account Name:"}),a.jsx("span",{className:"font-medium",children:c.accountName})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Account Number:"}),a.jsx("span",{className:"font-medium font-mono",children:c.accountNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Branch Code:"}),a.jsx("span",{className:"font-medium font-mono",children:c.branchCode})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Account Type:"}),a.jsx("span",{className:"font-medium",children:c.accountType})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("span",{className:"text-green-700",children:"Reference:"}),a.jsx("span",{className:"font-medium",children:e?.telegram_profile?.telegram_id||"SHARE_PURCHASE"})]})]})]}),(0,a.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your Full Name *"}),a.jsx("input",{type:"text",value:h,onChange:e=>b(e.target.value),placeholder:"As it appears on your bank account",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your Account Number *"}),a.jsx("input",{type:"text",value:f,onChange:e=>g(e.target.value),placeholder:"Your bank account number",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your Bank Name *"}),a.jsx("input",{type:"text",value:y,onChange:e=>j(e.target.value),placeholder:"e.g., Standard Bank, ABSA, FNB, Nedbank",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Reference Number (Optional)"}),a.jsx("input",{type:"text",value:N,onChange:e=>v(e.target.value),placeholder:"Transaction reference from your bank",className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Proof of Payment *"}),a.jsx("input",{type:"file",accept:"image/*,.pdf",onChange:e=>{let r=e.target.files?.[0];if(r){if(!r.type.startsWith("image/")&&"application/pdf"!==r.type){k("Please upload an image or PDF file");return}if(r.size>********){k("File size must be less than 10MB");return}_(r),k(null)}},required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"}),a.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Upload a screenshot or PDF of your bank transfer confirmation"})]}),A&&a.jsx("div",{className:"p-3 bg-red-100 text-red-700 rounded-md text-sm",children:A}),(0,a.jsxs)("div",{className:"flex justify-between",children:[a.jsx("button",{type:"button",onClick:()=>r.push("/dashboard/purchase"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Back"}),a.jsx("button",{type:"submit",disabled:P||!h||!f||!y||!w,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${!P&&h&&f&&y&&w?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-300 text-white cursor-not-allowed"}`,children:P?"Submitting...":"Submit Payment"})]})]}),(0,a.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-4",children:[a.jsx("h3",{className:"font-medium text-gray-800 mb-2",children:"Payment Instructions"}),(0,a.jsxs)("ol",{className:"text-sm text-gray-600 space-y-1 list-decimal list-inside",children:[(0,a.jsxs)("li",{children:["Transfer R",p," to the company bank account above"]}),(0,a.jsxs)("li",{children:["Use your Telegram ID (",e?.telegram_profile?.telegram_id,") as the reference"]}),a.jsx("li",{children:"Take a screenshot or save the confirmation from your bank"}),a.jsx("li",{children:"Fill in your details and upload the proof of payment"}),a.jsx("li",{children:"Submit this form for admin review"}),a.jsx("li",{children:"You'll be notified once your payment is approved"})]}),(0,a.jsxs)("div",{className:"mt-3 p-2 bg-yellow-100 rounded text-yellow-800 text-sm",children:[a.jsx("strong",{children:"Note:"})," Exchange rates are approximate. Final share allocation will be based on the USD equivalent at the time of processing."]})]})]})})})}},30668:(e,r,s)=>{"use strict";s.d(r,{Z:()=>i});var a=s(10326),t=s(17577),n=s(35047),l=s(84979);function i({children:e,requireTerms:r=!1,requireCountry:s=!1,requireKYC:i=!1}){let{user:o,loading:c,checkOnboardingStatus:d}=(0,l.a)();(0,n.useRouter)();let[u,m]=(0,t.useState)(!1),[p,x]=(0,t.useState)(!0);return p||c?a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),a.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):u?a.jsx(a.Fragment,{children:e}):null}},35047:(e,r,s)=>{"use strict";var a=s(77389);s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},13321:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},92632:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\purchase\zar\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[276,728,797],()=>s(54649));module.exports=a})();