(()=>{var e={};e.id=287,e.ids=[287],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},19400:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),a(62460),a(13321),a(73375),a(35866);var s=a(23191),t=a(88716),i=a(37922),n=a.n(i),l=a(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(r,o);let d=["",{children:["dashboard",{children:["referrals",{children:["withdraw",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,62460)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\referrals\\withdraw\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,13321)),"C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\xampp\\htdocs\\aureus_africa\\app\\dashboard\\referrals\\withdraw\\page.tsx"],u="/dashboard/referrals/withdraw/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/dashboard/referrals/withdraw/page",pathname:"/dashboard/referrals/withdraw",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2766:(e,r,a)=>{Promise.resolve().then(a.bind(a,58767))},8473:(e,r,a)=>{Promise.resolve().then(a.bind(a,20427))},58767:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>n});var s=a(10326),t=a(84979),i=a(30668);function n({children:e}){let{user:r,signOut:a}=(0,t.a)(),n=async()=>{await a()};return s.jsx(i.Z,{children:(0,s.jsxs)("div",{className:"dashboard-layout min-h-screen bg-gray-50",children:[s.jsx("header",{className:"bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg",children:s.jsx("div",{className:"container mx-auto px-4 py-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx("h1",{className:"text-2xl font-bold text-yellow-400",children:"Aureus Alliance"}),s.jsx("span",{className:"text-sm text-gray-300",children:"Dashboard"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[r?.telegram_profile&&(0,s.jsxs)("div",{className:"text-sm",children:[s.jsx("span",{className:"text-gray-300",children:"Welcome, "}),s.jsx("span",{className:"text-yellow-400 font-medium",children:r.telegram_profile.first_name})]}),s.jsx("button",{onClick:n,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium",children:"Logout"})]})]})})}),s.jsx("main",{className:"container mx-auto px-4 py-8",children:e})]})})}},20427:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>c});var s=a(10326),t=a(17577),i=a(35047),n=a(84979),l=a(91579);a(20286);var o=a(30668);let d=[{code:"ETH",name:"Ethereum (ETH)",fee:"High fees, secure"},{code:"BSC",name:"Binance Smart Chain (BSC)",fee:"Low fees, fast"},{code:"POLYGON",name:"Polygon (MATIC)",fee:"Very low fees, fast"},{code:"TRON",name:"TRON (TRX)",fee:"Low fees, popular in Asia"}];function c(){let{user:e}=(0,n.a)(),r=(0,i.useRouter)(),[a,c]=(0,t.useState)(0),[u,m]=(0,t.useState)(""),[h,x]=(0,t.useState)(""),[p,b]=(0,t.useState)(""),[f,g]=(0,t.useState)(!1),[w,y]=(0,t.useState)(null),[j,N]=(0,t.useState)(!0),v=async s=>{if(s.preventDefault(),!e?.telegram_profile?.id){y("User profile not found");return}let t=parseFloat(u);if(!t||t<=0){y("Please enter a valid withdrawal amount");return}if(t>a){y("Withdrawal amount exceeds available balance");return}if(t<10){y("Minimum withdrawal amount is $10 USDT");return}if(!h||!p){y("Please fill in all required fields");return}g(!0),y(null);try{let{error:a}=await l.OQ.from("commission_withdrawal_requests").insert({user_id:e.telegram_profile.id,withdrawal_amount:t,wallet_address:h,network:p,currency:"USDT",status:"pending"});if(a)throw Error(a.message);let{error:s}=await l.OQ.from("commission_transactions").insert({user_id:e.telegram_profile.id,transaction_type:"withdrawal",amount:-t,description:`USDT withdrawal to ${p} network`,status:"pending"});s&&console.error("Error creating transaction record:",s),r.push("/dashboard/referrals?status=withdrawal_submitted")}catch(e){console.error("Withdrawal error:",e),y("Failed to submit withdrawal request. Please try again.")}finally{g(!1)}};return j?s.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:(0,s.jsxs)("div",{className:"animate-pulse",children:[s.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),s.jsx("div",{className:"h-64 bg-gray-200 rounded mb-4"})]})}):s.jsx(o.Z,{requireTerms:!0,requireCountry:!0,children:s.jsx("div",{className:"max-w-2xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Withdraw Commission"}),s.jsx("p",{className:"text-gray-600",children:"Request a withdrawal of your USDT commission earnings"})]}),(0,s.jsxs)("div",{className:"bg-green-50 rounded-lg p-4 mb-6 border border-green-100",children:[s.jsx("h2",{className:"font-semibold text-green-800 mb-2",children:"Available Balance"}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-green-600 mb-1",children:["$",a.toFixed(2)," USDT"]}),s.jsx("p",{className:"text-green-700 text-sm",children:"Available for withdrawal"})]}),a<10?(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-lg p-4 mb-6 border border-yellow-200",children:[s.jsx("h3",{className:"font-medium text-yellow-800 mb-2",children:"Minimum Withdrawal Not Met"}),(0,s.jsxs)("p",{className:"text-yellow-700 text-sm",children:["The minimum withdrawal amount is $10 USDT. You need $",(10-a).toFixed(2)," more to make a withdrawal."]}),s.jsx("div",{className:"mt-3",children:s.jsx("a",{href:"/dashboard/referrals",className:"text-yellow-800 hover:text-yellow-900 font-medium text-sm underline",children:"← Back to Referrals Dashboard"})})]}):(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Withdrawal Amount (USDT) *"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("input",{type:"number",value:u,onChange:e=>m(e.target.value),placeholder:"Enter amount to withdraw",min:"10",max:a,step:"0.01",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 pr-16"}),s.jsx("button",{type:"button",onClick:()=>{m(a.toString())},className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors duration-200",children:"MAX"})]}),(0,s.jsxs)("p",{className:"text-gray-500 text-sm mt-1",children:["Minimum: $10 USDT • Maximum: $",a.toFixed(2)," USDT"]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"USDT Network *"}),(0,s.jsxs)("select",{value:p,onChange:e=>b(e.target.value),required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",children:[s.jsx("option",{value:"",children:"Select network"}),d.map(e=>(0,s.jsxs)("option",{value:e.code,children:[e.name," - ",e.fee]},e.code))]}),s.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"Choose the network that matches your wallet"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Your USDT Wallet Address *"}),s.jsx("input",{type:"text",value:h,onChange:e=>x(e.target.value),placeholder:"Enter your USDT wallet address",required:!0,className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"}),s.jsx("p",{className:"text-gray-500 text-sm mt-1",children:"⚠️ Make sure this address supports USDT on the selected network"})]}),p&&(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-100",children:[s.jsx("h3",{className:"font-medium text-blue-800 mb-2",children:"Network Information"}),(0,s.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Network:"}),s.jsx("span",{className:"font-medium",children:d.find(e=>e.code===p)?.name})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Processing Time:"}),s.jsx("span",{className:"font-medium",children:"1-3 business days"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Network Fees:"}),s.jsx("span",{className:"font-medium",children:"Covered by Aureus Alliance"})]})]})]}),w&&s.jsx("div",{className:"p-3 bg-red-100 text-red-700 rounded-md text-sm",children:w}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("button",{type:"button",onClick:()=>r.push("/dashboard/referrals"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Cancel"}),s.jsx("button",{type:"submit",disabled:f||!u||!h||!p,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${!f&&u&&h&&p?"bg-blue-600 hover:bg-blue-700 text-white":"bg-blue-300 text-white cursor-not-allowed"}`,children:f?"Submitting...":"Submit Withdrawal Request"})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-gray-50 rounded-lg p-4",children:[s.jsx("h3",{className:"font-medium text-gray-800 mb-2",children:"Important Information"}),(0,s.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1 list-disc list-inside",children:[s.jsx("li",{children:"Minimum withdrawal amount is $10 USDT"}),s.jsx("li",{children:"Withdrawals are processed within 1-3 business days"}),s.jsx("li",{children:"Network fees are covered by Aureus Alliance"}),s.jsx("li",{children:"All withdrawals require admin approval"}),s.jsx("li",{children:"You'll be notified once your withdrawal is processed"}),s.jsx("li",{children:"Double-check your wallet address - incorrect addresses cannot be recovered"})]})]})]})})})}},30668:(e,r,a)=>{"use strict";a.d(r,{Z:()=>l});var s=a(10326),t=a(17577),i=a(35047),n=a(84979);function l({children:e,requireTerms:r=!1,requireCountry:a=!1,requireKYC:l=!1}){let{user:o,loading:d,checkOnboardingStatus:c}=(0,n.a)();(0,i.useRouter)();let[u,m]=(0,t.useState)(!1),[h,x]=(0,t.useState)(!0);return h||d?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):u?s.jsx(s.Fragment,{children:e}):null}},20286:(e,r,a)=>{"use strict";a.d(r,{B_:()=>d,f6:()=>o,fj:()=>l,od:()=>s,pD:()=>t});let s=[{code:"ZA",name:"South Africa",region:"africa"},{code:"SZ",name:"Eswatini",region:"africa"},{code:"NA",name:"Namibia",region:"africa"},{code:"BW",name:"Botswana",region:"africa"},{code:"ZW",name:"Zimbabwe",region:"africa"},{code:"MZ",name:"Mozambique",region:"africa"},{code:"LS",name:"Lesotho",region:"africa"},{code:"US",name:"United States",region:"western"},{code:"GB",name:"United Kingdom",region:"western"},{code:"CA",name:"Canada",region:"western"},{code:"AU",name:"Australia",region:"western"},{code:"NZ",name:"New Zealand",region:"western"},{code:"SG",name:"Singapore",region:"asia"},{code:"AE",name:"United Arab Emirates",region:"asia"},{code:"OTHER",name:"Other Country",region:"other"}],t=e=>{let r=s.find(r=>r.code===e);return r?r.name:"Unknown Country"},i=e=>["ZA","SZ","NA"].includes(e),n=e=>!0,l=e=>{let r=[];return n(e)&&r.push("USDT"),i(e)&&r.push("ZAR"),r},o=e=>["US","GB","CA"].includes(e),d=e=>i(e)?"You can pay using ZAR bank transfer or USDT cryptocurrency.":"You can pay using USDT cryptocurrency on multiple networks."},35047:(e,r,a)=>{"use strict";var s=a(77389);a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},13321:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\layout.tsx#default`)},62460:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\dashboard\referrals\withdraw\page.tsx#default`)}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[276,728,797],()=>a(19400));module.exports=s})();