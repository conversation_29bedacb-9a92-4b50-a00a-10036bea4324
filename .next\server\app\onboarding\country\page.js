(()=>{var e={};e.id=12,e.ids=[12],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},60528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(44739),r(60291),r(73375),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["onboarding",{children:["country",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44739)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\country\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60291)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\country\\page.tsx"],u="/onboarding/country/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/onboarding/country/page",pathname:"/onboarding/country",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73634:(e,t,r)=>{Promise.resolve().then(r.bind(r,91032))},27554:(e,t,r)=>{Promise.resolve().then(r.bind(r,51745))},91032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(10326),a=r(17577),n=r(35047),i=r(84979),o=r(91579),l=r(20286);function c(){let{user:e,refreshUser:t}=(0,i.a)(),r=(0,n.useRouter)(),[c,d]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(null),h=async()=>{if(!c){x("Please select your country");return}if(!e?.telegram_profile?.id){x("User profile not found");return}m(!0),x(null);try{let{error:s}=await o.OQ.from("telegram_users").update({country:c}).eq("id",e.telegram_profile.id);if(s)throw Error(s.message);await t(),r.push("/onboarding/kyc")}catch(e){console.error("Error updating country:",e),x("Failed to save country selection. Please try again.")}finally{m(!1)}};return(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Select Your Country"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"This helps us provide you with the appropriate payment options"})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 font-medium mb-2",children:"Country of Residence"}),(0,s.jsxs)("select",{value:c,onChange:e=>d(e.target.value),className:"w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",children:[s.jsx("option",{value:"",children:"Select your country"}),l.od.map(e=>s.jsx("option",{value:e.code,children:e.name},e.code))]})]}),c&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100",children:[s.jsx("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"Payment Options Available"}),s.jsx("p",{className:"text-sm text-blue-700 mb-2",children:(0,l.B_)(c)}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:(0,l.fj)(c).map(e=>s.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded",children:e},e))})]}),p&&s.jsx("div",{className:"mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm",children:p}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("button",{onClick:()=>r.push("/onboarding/terms"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Back"}),s.jsx("button",{onClick:h,disabled:u||!c,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${u||!c?"bg-blue-300 text-white cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:u?"Processing...":"Continue to KYC"})]})]})}},51745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),a=r(17577),n=r(35047),i=r(84979),o=r(93167);let l=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z",clipRule:"evenodd"}))});function c({steps:e,currentStep:t,totalSteps:r}){let a=t/r*100;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Setup Progress"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[t," of ",r," completed"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${a}%`}})})]}),s.jsx("div",{className:"space-y-3",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:e.completed?s.jsx(o.Z,{className:"h-5 w-5 text-green-500"}):e.current?s.jsx(l,{className:"h-5 w-5 text-blue-500"}):s.jsx("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:`text-sm font-medium ${e.completed?"text-green-700":e.current?"text-blue-700":"text-gray-500"}`,children:e.title}),s.jsx("p",{className:`text-xs ${e.completed?"text-green-600":e.current?"text-blue-600":"text-gray-400"}`,children:e.description})]})]},e.id))})]})}var d=r(30668);function u({children:e}){let{user:t,checkOnboardingStatus:r}=(0,i.a)();(0,n.useRouter)();let o=(0,n.usePathname)(),[l,u]=(0,a.useState)({hasAcceptedTerms:!1,hasSelectedCountry:!1,hasCompletedKYC:!1}),[m,p]=(0,a.useState)(!0);if(m)return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Loading onboarding..."})]})});let x=[{id:"terms",title:"Accept Terms & Conditions",description:"Review and accept our terms of service",completed:l.hasAcceptedTerms,current:"/onboarding/terms"===o},{id:"country",title:"Select Country",description:"Choose your country of residence",completed:l.hasSelectedCountry,current:"/onboarding/country"===o},{id:"kyc",title:"KYC Verification",description:"Provide your identification details",completed:l.hasCompletedKYC,current:"/onboarding/kyc"===o},{id:"complete",title:"Setup Complete",description:"Access your dashboard",completed:l.hasAcceptedTerms&&l.hasSelectedCountry&&l.hasCompletedKYC,current:!1}];x.findIndex(e=>e.current);let h=x.filter(e=>e.completed).length;return s.jsx(d.Z,{children:s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",children:s.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome to Aureus Alliance"}),s.jsx("p",{className:"text-gray-300",children:"Let's get your account set up in just a few steps"})]}),s.jsx(c,{steps:x,currentStep:h,totalSteps:x.length}),s.jsx("div",{className:"bg-white rounded-lg shadow-xl overflow-hidden",children:e}),s.jsx("div",{className:"text-center mt-6",children:(0,s.jsxs)("p",{className:"text-gray-300 text-sm",children:["Need help? Contact us at"," ",s.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"text-yellow-400 hover:text-yellow-300 underline",children:"@AureusAllianceBot"})]})})]})})})})}},30668:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(10326),a=r(17577),n=r(35047),i=r(84979);function o({children:e,requireTerms:t=!1,requireCountry:r=!1,requireKYC:o=!1}){let{user:l,loading:c,checkOnboardingStatus:d}=(0,i.a)();(0,n.useRouter)();let[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!0);return p||c?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):u?s.jsx(s.Fragment,{children:e}):null}},20286:(e,t,r)=>{"use strict";r.d(t,{B_:()=>c,f6:()=>l,fj:()=>o,od:()=>s,pD:()=>a});let s=[{code:"ZA",name:"South Africa",region:"africa"},{code:"SZ",name:"Eswatini",region:"africa"},{code:"NA",name:"Namibia",region:"africa"},{code:"BW",name:"Botswana",region:"africa"},{code:"ZW",name:"Zimbabwe",region:"africa"},{code:"MZ",name:"Mozambique",region:"africa"},{code:"LS",name:"Lesotho",region:"africa"},{code:"US",name:"United States",region:"western"},{code:"GB",name:"United Kingdom",region:"western"},{code:"CA",name:"Canada",region:"western"},{code:"AU",name:"Australia",region:"western"},{code:"NZ",name:"New Zealand",region:"western"},{code:"SG",name:"Singapore",region:"asia"},{code:"AE",name:"United Arab Emirates",region:"asia"},{code:"OTHER",name:"Other Country",region:"other"}],a=e=>{let t=s.find(t=>t.code===e);return t?t.name:"Unknown Country"},n=e=>["ZA","SZ","NA"].includes(e),i=e=>!0,o=e=>{let t=[];return i(e)&&t.push("USDT"),n(e)&&t.push("ZAR"),t},l=e=>["US","GB","CA"].includes(e),c=e=>n(e)?"You can pay using ZAR bank transfer or USDT cryptocurrency.":"You can pay using USDT cryptocurrency on multiple networks."},35047:(e,t,r)=>{"use strict";var s=r(77389);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},44739:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\country\page.tsx#default`)},60291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\layout.tsx#default`)},93167:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,728,797],()=>r(60528));module.exports=s})();