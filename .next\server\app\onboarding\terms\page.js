(()=>{var e={};e.id=828,e.ids=[828],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},68621:e=>{"use strict";e.exports=require("punycode")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},17360:e=>{"use strict";e.exports=require("url")},71568:e=>{"use strict";e.exports=require("zlib")},54507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),r(90874),r(60291),r(73375),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["onboarding",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90874)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\terms\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60291)),"C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,73375)),"C:\\xampp\\htdocs\\aureus_africa\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\xampp\\htdocs\\aureus_africa\\app\\onboarding\\terms\\page.tsx"],u="/onboarding/terms/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/onboarding/terms/page",pathname:"/onboarding/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27554:(e,t,r)=>{Promise.resolve().then(r.bind(r,51745))},4495:(e,t,r)=>{Promise.resolve().then(r.bind(r,56926))},51745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(10326),a=r(17577),n=r(35047),i=r(84979),o=r(93167);let l=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z",clipRule:"evenodd"}))});function c({steps:e,currentStep:t,totalSteps:r}){let a=t/r*100;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:"Setup Progress"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[t," of ",r," completed"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${a}%`}})})]}),s.jsx("div",{className:"space-y-3",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:e.completed?s.jsx(o.Z,{className:"h-5 w-5 text-green-500"}):e.current?s.jsx(l,{className:"h-5 w-5 text-blue-500"}):s.jsx("div",{className:"h-5 w-5 rounded-full border-2 border-gray-300"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:`text-sm font-medium ${e.completed?"text-green-700":e.current?"text-blue-700":"text-gray-500"}`,children:e.title}),s.jsx("p",{className:`text-xs ${e.completed?"text-green-600":e.current?"text-blue-600":"text-gray-400"}`,children:e.description})]})]},e.id))})]})}var d=r(30668);function u({children:e}){let{user:t,checkOnboardingStatus:r}=(0,i.a)();(0,n.useRouter)();let o=(0,n.usePathname)(),[l,u]=(0,a.useState)({hasAcceptedTerms:!1,hasSelectedCountry:!1,hasCompletedKYC:!1}),[m,h]=(0,a.useState)(!0);if(m)return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Loading onboarding..."})]})});let p=[{id:"terms",title:"Accept Terms & Conditions",description:"Review and accept our terms of service",completed:l.hasAcceptedTerms,current:"/onboarding/terms"===o},{id:"country",title:"Select Country",description:"Choose your country of residence",completed:l.hasSelectedCountry,current:"/onboarding/country"===o},{id:"kyc",title:"KYC Verification",description:"Provide your identification details",completed:l.hasCompletedKYC,current:"/onboarding/kyc"===o},{id:"complete",title:"Setup Complete",description:"Access your dashboard",completed:l.hasAcceptedTerms&&l.hasSelectedCountry&&l.hasCompletedKYC,current:!1}];p.findIndex(e=>e.current);let x=p.filter(e=>e.completed).length;return s.jsx(d.Z,{children:s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",children:s.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Welcome to Aureus Alliance"}),s.jsx("p",{className:"text-gray-300",children:"Let's get your account set up in just a few steps"})]}),s.jsx(c,{steps:p,currentStep:x,totalSteps:p.length}),s.jsx("div",{className:"bg-white rounded-lg shadow-xl overflow-hidden",children:e}),s.jsx("div",{className:"text-center mt-6",children:(0,s.jsxs)("p",{className:"text-gray-300 text-sm",children:["Need help? Contact us at"," ",s.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"text-yellow-400 hover:text-yellow-300 underline",children:"@AureusAllianceBot"})]})})]})})})})}},56926:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(10326),a=r(17577),n=r(35047),i=r(84979),o=r(91579);function l(){let{user:e,refreshUser:t}=(0,i.a)(),r=(0,n.useRouter)(),[l,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(null),[m,h]=(0,a.useState)(!1),p=async()=>{if(!m){u("You must accept the terms and conditions to continue");return}if(!e?.telegram_profile?.id){u("User profile not found");return}c(!0),u(null);try{let{error:s}=await o.OQ.from("terms_acceptance").insert({user_id:e.telegram_profile.id,terms_type:"general",version:"1.0",accepted_at:new Date().toISOString()});if(s)throw Error(s.message);await t(),r.push("/onboarding/country")}catch(e){console.error("Error accepting terms:",e),u("Failed to save terms acceptance. Please try again.")}finally{c(!1)}};return(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Terms and Conditions"}),s.jsx("p",{className:"text-gray-600 mt-2",children:"Please review and accept our terms to continue"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 h-96 overflow-y-auto",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"AUREUS ALLIANCE HOLDINGS (PTY) LTD"}),s.jsx("h3",{className:"text-lg font-medium mb-2",children:"Terms and Conditions"}),s.jsx("p",{className:"mb-4",children:'These Terms and Conditions ("Terms") govern your use of the Aureus Alliance Holdings (Pty) Ltd ("Aureus Alliance") platform, website, and services. By accessing or using our services, you agree to be bound by these Terms.'}),s.jsx("h4",{className:"font-medium mb-2",children:"1. Share Ownership"}),s.jsx("p",{className:"mb-4",children:"When you purchase shares through Aureus Alliance Holdings, you are acquiring ownership shares in a South African registered company that owns and operates gold placer deposit mining operations. These shares represent partial ownership in the company and entitle you to dividends as declared by the company."}),s.jsx("h4",{className:"font-medium mb-2",children:"2. Risk Disclosure"}),s.jsx("p",{className:"mb-4",children:"Mining operations involve inherent risks, including but not limited to operational challenges, regulatory changes, market fluctuations, and environmental factors. The value of shares may fluctuate, and past performance is not indicative of future results. You should only purchase shares with funds you can afford to risk."}),s.jsx("h4",{className:"font-medium mb-2",children:"3. Compliance with Laws"}),s.jsx("p",{className:"mb-4",children:"Aureus Alliance operates under South African law. By using our services, you agree to comply with all applicable laws and regulations in your jurisdiction. You represent that your purchase of shares does not violate any laws in your country of residence."}),s.jsx("h4",{className:"font-medium mb-2",children:"4. KYC Requirements"}),s.jsx("p",{className:"mb-4",children:"To comply with regulatory requirements, Aureus Alliance implements Know Your Customer (KYC) procedures. You agree to provide accurate and complete information as requested and to update this information if it changes."}),s.jsx("h4",{className:"font-medium mb-2",children:"5. Privacy Policy"}),s.jsx("p",{className:"mb-4",children:"Your privacy is important to us. Our collection and use of your personal information is governed by our Privacy Policy, which is incorporated into these Terms by reference."}),s.jsx("h4",{className:"font-medium mb-2",children:"6. Limitation of Liability"}),s.jsx("p",{className:"mb-4",children:"To the maximum extent permitted by law, Aureus Alliance shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including loss of profits, data, or goodwill, arising from or in connection with your use of our services."}),s.jsx("h4",{className:"font-medium mb-2",children:"7. Amendments"}),s.jsx("p",{className:"mb-4",children:"Aureus Alliance reserves the right to modify these Terms at any time. We will provide notice of significant changes. Your continued use of our services after such modifications constitutes your acceptance of the updated Terms."}),s.jsx("h4",{className:"font-medium mb-2",children:"8. Governing Law"}),s.jsx("p",{className:"mb-4",children:"These Terms shall be governed by and construed in accordance with the laws of South Africa, without regard to its conflict of law provisions."})]}),s.jsx("div",{className:"mb-6",children:(0,s.jsxs)("label",{className:"flex items-start cursor-pointer",children:[s.jsx("input",{type:"checkbox",className:"mt-1 h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500",checked:m,onChange:e=>h(e.target.checked)}),s.jsx("span",{className:"ml-2 text-gray-700",children:"I have read, understood, and agree to the Terms and Conditions of Aureus Alliance Holdings (Pty) Ltd"})]})}),d&&s.jsx("div",{className:"mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm",children:d}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("button",{onClick:()=>r.push("/login"),className:"px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200",children:"Cancel"}),s.jsx("button",{onClick:p,disabled:l||!m,className:`px-6 py-2 rounded-lg transition-colors duration-200 ${l||!m?"bg-blue-300 text-white cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:l?"Processing...":"Accept & Continue"})]})]})}},30668:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(10326),a=r(17577),n=r(35047),i=r(84979);function o({children:e,requireTerms:t=!1,requireCountry:r=!1,requireKYC:o=!1}){let{user:l,loading:c,checkOnboardingStatus:d}=(0,i.a)();(0,n.useRouter)();let[u,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!0);return h||c?s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white text-lg",children:"Verifying authentication..."})]})}):u?s.jsx(s.Fragment,{children:e}):null}},35047:(e,t,r)=>{"use strict";var s=r(77389);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},60291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\layout.tsx#default`)},90874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\onboarding\terms\page.tsx#default`)},93167:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,728,797],()=>r(54507));module.exports=s})();