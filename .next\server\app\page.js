/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUN4YW1wcCU1QyU1Q2h0ZG9jcyU1QyU1Q2F1cmV1c19hZnJpY2ElNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQW9GIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXVyZXVzLWFsbGlhbmNlLWhvbGRpbmdzLz9mYmVkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxceGFtcHBcXFxcaHRkb2NzXFxcXGF1cmV1c19hZnJpY2FcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Caureus-design-system.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CSiteContentContext.tsx%22%2C%22ids%22%3A%5B%22SiteContentProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Caureus-design-system.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CSiteContentContext.tsx%22%2C%22ids%22%3A%5B%22SiteContentProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/ErrorBoundary.tsx */ \"(ssr)/./components/ui/ErrorBoundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/Toast.tsx */ \"(ssr)/./components/ui/Toast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(ssr)/./contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/SiteContentContext.tsx */ \"(ssr)/./contexts/SiteContentContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/ThemeContext.tsx */ \"(ssr)/./contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Caureus-design-system.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccomponents%5C%5Cui%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CSiteContentContext.tsx%22%2C%22ids%22%3A%5B%22SiteContentProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Caureus_africa%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Dynamically import the existing App component to preserve homepage\nconst ExistingApp = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\page.tsx -> \" + \"@/App\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"Loading Aureus Alliance...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined)\n});\nfunction HomePage() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg\",\n                        children: \"Loading Aureus Alliance...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExistingApp, {}, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\page.tsx\",\n        lineNumber: 37,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/ErrorBoundary.tsx":
/*!*****************************************!*\
  !*** ./components/ui/ErrorBoundary.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncErrorBoundary: () => (/* binding */ AsyncErrorBoundary),\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorBoundary: () => (/* binding */ useErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/error-handling */ \"(ssr)/./lib/error-handling.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,useErrorBoundary,withErrorBoundary,AsyncErrorBoundary auto */ \n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            errorInfo\n        });\n        // Log error\n        const appError = new _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.AppError(`React Error Boundary: ${error.message}`, _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorType.SYSTEM, _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorSeverity.HIGH, {\n            stack: error.stack,\n            componentStack: errorInfo.componentStack,\n            originalError: error\n        });\n        _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorLogger.logError(appError);\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            // Custom fallback UI\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            // Default error UI\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorFallback, {\n                error: this.state.error,\n                errorInfo: this.state.errorInfo,\n                showDetails: this.props.showDetails,\n                onRetry: ()=>this.setState({\n                        hasError: false,\n                        error: undefined,\n                        errorInfo: undefined\n                    })\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction ErrorFallback({ error, errorInfo, showDetails = false, onRetry }) {\n    const handleReload = ()=>{\n        if (false) {}\n    };\n    const handleRetry = ()=>{\n        if (onRetry) {\n            onRetry();\n        } else {\n            handleReload();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-8 w-8 text-red-600 dark:text-red-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-gray-900 dark:text-white mb-2\",\n                    children: \"Something went wrong\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                    children: \"We're sorry, but something unexpected happened. Please try again.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                showDetails && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-white mb-2\",\n                            children: \"Error Details:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap break-words\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        errorInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"text-xs text-gray-600 dark:text-gray-400 cursor-pointer\",\n                                    children: \"Component Stack\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-500 mt-1 whitespace-pre-wrap break-words\",\n                                    children: errorInfo.componentStack\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleReload,\n                            className: \"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200\",\n                            children: \"Reload Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-6 text-sm text-gray-500 dark:text-gray-400\",\n                    children: [\n                        \"If this problem persists, please\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://t.me/AureusAllianceBot\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-blue-600 dark:text-blue-400 hover:underline\",\n                            children: \"contact support\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n// Hook for error boundary\nfunction useErrorBoundary() {\n    const throwError = (error)=>{\n        throw error;\n    };\n    return {\n        throwError\n    };\n}\n// Higher-order component for error boundary\nfunction withErrorBoundary(Component, errorBoundaryProps) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            ...errorBoundaryProps,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n            lineNumber: 198,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Async Error Boundary for handling async errors\nfunction AsyncErrorBoundary({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n        onError: (error, errorInfo)=>{\n            // Handle async errors specifically\n            const appError = new _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.AppError(`Async Error: ${error.message}`, _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorType.SYSTEM, _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorSeverity.HIGH, {\n                stack: error.stack,\n                componentStack: errorInfo.componentStack,\n                isAsync: true\n            });\n            _lib_error_handling__WEBPACK_IMPORTED_MODULE_2__.ErrorLogger.logError(appError);\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\ErrorBoundary.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/Toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/Toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast),\n/* harmony export */   useToastActions: () => (/* binding */ useToastActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast,useToastActions auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Toast provider\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substring(2, 9);\n        const newToast = {\n            ...toast,\n            id,\n            duration: toast.duration ?? 5000\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove toast after duration\n        if (newToast.duration > 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, newToast.duration);\n        }\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const clearToasts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setToasts([]);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            clearToasts\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use toast\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\n// Toast container component\nfunction ToastContainer() {\n    const { toasts } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast\n            }, toast.id, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n// Individual toast item\nfunction ToastItem({ toast }) {\n    const { removeToast } = useToast();\n    const typeStyles = {\n        success: {\n            bg: \"bg-green-50 border-green-200\",\n            icon: \"✅\",\n            iconColor: \"text-green-500\",\n            titleColor: \"text-green-800\",\n            messageColor: \"text-green-700\"\n        },\n        error: {\n            bg: \"bg-red-50 border-red-200\",\n            icon: \"❌\",\n            iconColor: \"text-red-500\",\n            titleColor: \"text-red-800\",\n            messageColor: \"text-red-700\"\n        },\n        warning: {\n            bg: \"bg-yellow-50 border-yellow-200\",\n            icon: \"⚠️\",\n            iconColor: \"text-yellow-500\",\n            titleColor: \"text-yellow-800\",\n            messageColor: \"text-yellow-700\"\n        },\n        info: {\n            bg: \"bg-blue-50 border-blue-200\",\n            icon: \"ℹ️\",\n            iconColor: \"text-blue-500\",\n            titleColor: \"text-blue-800\",\n            messageColor: \"text-blue-700\"\n        }\n    };\n    const style = typeStyles[toast.type];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border rounded-lg p-4 shadow-lg animate-in slide-in-from-right-full duration-300\", style.bg),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg mr-3\", style.iconColor),\n                    children: style.icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm font-semibold mb-1\", style.titleColor),\n                            children: toast.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm\", style.messageColor),\n                            children: toast.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toast.action.onClick,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 text-sm font-medium underline hover:no-underline\", style.titleColor),\n                            children: toast.action.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>removeToast(toast.id),\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-3 text-lg hover:opacity-70 transition-opacity\", style.iconColor),\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n// Convenience functions\nconst toast = {\n    success: (message, options)=>{\n        const context = useToast();\n        context.addToast({\n            type: \"success\",\n            message,\n            ...options\n        });\n    },\n    error: (message, options)=>{\n        const context = useToast();\n        context.addToast({\n            type: \"error\",\n            message,\n            ...options\n        });\n    },\n    warning: (message, options)=>{\n        const context = useToast();\n        context.addToast({\n            type: \"warning\",\n            message,\n            ...options\n        });\n    },\n    info: (message, options)=>{\n        const context = useToast();\n        context.addToast({\n            type: \"info\",\n            message,\n            ...options\n        });\n    }\n};\n// Toast hook with convenience methods\nfunction useToastActions() {\n    const { addToast } = useToast();\n    return {\n        success: (message, options)=>{\n            addToast({\n                type: \"success\",\n                message,\n                ...options\n            });\n        },\n        error: (message, options)=>{\n            addToast({\n                type: \"error\",\n                message,\n                ...options\n            });\n        },\n        warning: (message, options)=>{\n            addToast({\n                type: \"warning\",\n                message,\n                ...options\n            });\n        },\n        info: (message, options)=>{\n            addToast({\n                type: \"info\",\n                message,\n                ...options\n            });\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-client */ \"(ssr)/./lib/supabase-client.ts\");\n/* harmony import */ var _lib_data_sync__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/data-sync */ \"(ssr)/./lib/data-sync.ts\");\n/* harmony import */ var _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/error-handling */ \"(ssr)/./lib/error-handling.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [syncError, setSyncError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check onboarding status\n    const checkOnboardingStatus = async ()=>{\n        if (!user?.telegram_profile) {\n            return {\n                hasAcceptedTerms: false,\n                hasSelectedCountry: false,\n                hasCompletedKYC: false\n            };\n        }\n        try {\n            // Check terms acceptance\n            const { data: termsData } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"terms_acceptance\").select(\"*\").eq(\"user_id\", user.telegram_profile.id).single();\n            // Check country selection\n            const hasSelectedCountry = !!user.telegram_profile.country;\n            // Check KYC status using correct table name\n            const { data: kycData } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"kyc_information\").select(\"*\").eq(\"user_id\", user.telegram_profile.id).single();\n            return {\n                hasAcceptedTerms: !!termsData,\n                hasSelectedCountry,\n                hasCompletedKYC: !!kycData\n            };\n        } catch (error) {\n            console.error(\"Error checking onboarding status:\", error);\n            return {\n                hasAcceptedTerms: false,\n                hasSelectedCountry: false,\n                hasCompletedKYC: false\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        try {\n            const { data: { user: authUser } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (authUser) {\n                // Get Telegram profile\n                const telegramId = authUser.user_metadata?.telegram_id;\n                if (telegramId) {\n                    const { data: telegramProfile } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"telegram_users\").select(\"*\").eq(\"telegram_id\", telegramId).single();\n                    if (telegramProfile) {\n                        const onboardingStatus = await checkOnboardingStatus();\n                        setUser({\n                            ...authUser,\n                            telegram_profile: telegramProfile,\n                            ...onboardingStatus\n                        });\n                    } else {\n                        setUser(authUser);\n                    }\n                } else {\n                    setUser(authUser);\n                }\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    // Sign in with Telegram\n    const signInWithTelegram = async (telegramData)=>{\n        try {\n            // Verify Telegram data (basic client-side check)\n            if (!telegramData.id || !telegramData.first_name) {\n                return {\n                    success: false,\n                    error: \"Invalid Telegram data\"\n                };\n            }\n            // Check if user exists in telegram_users table\n            const { data: existingUser } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"telegram_users\").select(\"*\").eq(\"telegram_id\", telegramData.id.toString()).single();\n            if (existingUser) {\n                // Create auth session for existing user\n                const { data: authData, error: authError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                    email: `telegram_${telegramData.id}@aureus.africa`,\n                    password: `telegram_${telegramData.id}_auth`\n                });\n                if (authError) {\n                    // Try to create auth user if doesn't exist\n                    const { data: signUpData, error: signUpError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                        email: `telegram_${telegramData.id}@aureus.africa`,\n                        password: `telegram_${telegramData.id}_auth`,\n                        options: {\n                            data: {\n                                telegram_id: telegramData.id,\n                                telegram_username: telegramData.username,\n                                telegram_first_name: telegramData.first_name,\n                                telegram_last_name: telegramData.last_name\n                            }\n                        }\n                    });\n                    if (signUpError) {\n                        return {\n                            success: false,\n                            error: \"Authentication failed\"\n                        };\n                    }\n                }\n                await refreshUser();\n                return {\n                    success: true\n                };\n            } else {\n                // New user - create in telegram_users table first\n                const { data: newUser, error: createError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"telegram_users\").insert({\n                    telegram_id: telegramData.id.toString(),\n                    username: telegramData.username || \"\",\n                    first_name: telegramData.first_name,\n                    last_name: telegramData.last_name || \"\",\n                    registration_mode: \"web\"\n                }).select().single();\n                if (createError) {\n                    return {\n                        success: false,\n                        error: \"Failed to create user profile\"\n                    };\n                }\n                // Create auth user\n                const { data: authData, error: authError } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                    email: `telegram_${telegramData.id}@aureus.africa`,\n                    password: `telegram_${telegramData.id}_auth`,\n                    options: {\n                        data: {\n                            telegram_id: telegramData.id,\n                            telegram_username: telegramData.username,\n                            telegram_first_name: telegramData.first_name,\n                            telegram_last_name: telegramData.last_name\n                        }\n                    }\n                });\n                if (authError) {\n                    return {\n                        success: false,\n                        error: \"Authentication setup failed\"\n                    };\n                }\n                await refreshUser();\n                return {\n                    success: true\n                };\n            }\n        } catch (error) {\n            console.error(\"Telegram sign in error:\", error);\n            return {\n                success: false,\n                error: \"Sign in failed\"\n            };\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        try {\n            // Call logout API to clear cookies and sign out\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state\n            setUser(null);\n            // Redirect to login\n            window.location.href = \"/login\";\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            // Fallback: clear local state and redirect\n            setUser(null);\n            window.location.href = \"/login\";\n        }\n    };\n    // Refresh session using API\n    const refreshSession = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    await refreshUser();\n                    return true;\n                }\n            }\n            // If refresh failed, sign out\n            setUser(null);\n            return false;\n        } catch (error) {\n            console.error(\"Session refresh error:\", error);\n            setUser(null);\n            return false;\n        }\n    };\n    // Initialize auth state and real-time sync\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let userSyncUnsubscribe = null;\n        const initializeAuth = async ()=>{\n            try {\n                const { data: { session } } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (session) {\n                    await refreshUser();\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                await _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorLogger.logError(new _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.AppError(`Auth initialization failed: ${error}`, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorType.AUTHENTICATION, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorSeverity.HIGH, {\n                    originalError: error\n                }));\n            } finally{\n                setLoading(false);\n            }\n        };\n        const setupUserSync = (userId)=>{\n            try {\n                // Subscribe to user-specific real-time updates\n                userSyncUnsubscribe = _lib_data_sync__WEBPACK_IMPORTED_MODULE_3__.dataSyncService.subscribeToUserUpdates(userId, async (payload)=>{\n                    try {\n                        // Refresh user data when changes occur\n                        await refreshUser();\n                        setSyncError(null);\n                    } catch (error) {\n                        console.error(\"User sync error:\", error);\n                        setSyncError(\"Failed to sync user data\");\n                        await _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorLogger.logError(new _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.AppError(`User sync failed: ${error}`, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorType.SYSTEM, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorSeverity.MEDIUM, {\n                            userId,\n                            payload,\n                            originalError: error\n                        }));\n                    }\n                });\n            } catch (error) {\n                console.error(\"Failed to setup user sync:\", error);\n                setSyncError(\"Failed to setup real-time synchronization\");\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            try {\n                if (event === \"SIGNED_IN\" && session) {\n                    await refreshUser();\n                    // Setup real-time sync for the authenticated user\n                    const telegramId = session.user.user_metadata?.telegram_id;\n                    if (telegramId) {\n                        const { data: telegramProfile } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"telegram_users\").select(\"id\").eq(\"telegram_id\", telegramId).single();\n                        if (telegramProfile) {\n                            setupUserSync(telegramProfile.id);\n                        }\n                    }\n                } else if (event === \"SIGNED_OUT\") {\n                    setUser(null);\n                    setSyncError(null);\n                    // Clean up sync subscription\n                    if (userSyncUnsubscribe) {\n                        userSyncUnsubscribe();\n                        userSyncUnsubscribe = null;\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth state change error:\", error);\n                await _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorLogger.logError(new _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.AppError(`Auth state change failed: ${error}`, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorType.AUTHENTICATION, _lib_error_handling__WEBPACK_IMPORTED_MODULE_4__.ErrorSeverity.MEDIUM, {\n                    event,\n                    originalError: error\n                }));\n            } finally{\n                setLoading(false);\n            }\n        });\n        return ()=>{\n            subscription.unsubscribe();\n            if (userSyncUnsubscribe) {\n                userSyncUnsubscribe();\n            }\n        };\n    }, []);\n    const value = {\n        user,\n        loading,\n        signInWithTelegram,\n        signOut,\n        refreshUser,\n        refreshSession,\n        checkOnboardingStatus\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 385,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/SiteContentContext.tsx":
/*!*****************************************!*\
  !*** ./contexts/SiteContentContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SiteContentProvider: () => (/* binding */ SiteContentProvider),\n/* harmony export */   useContent: () => (/* binding */ useContent),\n/* harmony export */   useSiteContentContext: () => (/* binding */ useSiteContentContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSiteContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useSiteContent */ \"(ssr)/./hooks/useSiteContent.ts\");\n/* __next_internal_client_entry_do_not_use__ SiteContentProvider,useSiteContentContext,useContent auto */ \n\n\nconst SiteContentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst SiteContentProvider = ({ children })=>{\n    const siteContent = (0,_hooks_useSiteContent__WEBPACK_IMPORTED_MODULE_2__.useSiteContent)();\n    // Don't block rendering if there's an error loading content\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SiteContentContext.Provider, {\n        value: siteContent,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\SiteContentContext.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\nconst useSiteContentContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SiteContentContext);\n    if (context === undefined) {\n        // Return a fallback context instead of throwing an error\n        console.warn(\"useSiteContentContext used outside of SiteContentProvider, using fallback\");\n        return {\n            content: {},\n            loading: false,\n            error: null,\n            getContent: (section, key, defaultValue = \"\")=>defaultValue,\n            refreshContent: async ()=>{}\n        };\n    }\n    return context;\n};\n// Helper hook for easy content access\nconst useContent = (section, key, defaultValue = \"\")=>{\n    try {\n        const { getContent } = useSiteContentContext();\n        return getContent(section, key, defaultValue);\n    } catch (error) {\n        console.warn(\"Error accessing site content:\", error);\n        return defaultValue;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/SiteContentContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   ThemeSelector: () => (/* binding */ ThemeSelector),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,ThemeToggle,ThemeSelector auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children, defaultTheme = \"system\", storageKey = \"aureus-theme\" }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const stored = localStorage.getItem(storageKey);\n        if (stored && [\n            \"light\",\n            \"dark\",\n            \"system\"\n        ].includes(stored)) {\n            setThemeState(stored);\n        }\n    }, [\n        storageKey\n    ]);\n    // Update resolved theme based on current theme and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const updateResolvedTheme = ()=>{\n            if (theme === \"system\") {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                setResolvedTheme(systemTheme);\n            } else {\n                setResolvedTheme(theme);\n            }\n        };\n        updateResolvedTheme();\n        // Listen for system theme changes\n        const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n        const handleChange = ()=>{\n            if (theme === \"system\") {\n                updateResolvedTheme();\n            }\n        };\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const root = window.document.documentElement;\n        root.classList.remove(\"light\", \"dark\");\n        root.classList.add(resolvedTheme);\n    }, [\n        resolvedTheme\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        localStorage.setItem(storageKey, newTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme,\n            resolvedTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Theme Toggle Component\nfunction ThemeToggle() {\n    const { theme, setTheme, resolvedTheme } = useTheme();\n    const toggleTheme = ()=>{\n        if (theme === \"light\") {\n            setTheme(\"dark\");\n        } else if (theme === \"dark\") {\n            setTheme(\"system\");\n        } else {\n            setTheme(\"light\");\n        }\n    };\n    const getIcon = ()=>{\n        if (theme === \"system\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this);\n        } else if (resolvedTheme === \"dark\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    const getLabel = ()=>{\n        if (theme === \"system\") return \"System\";\n        return theme === \"dark\" ? \"Dark\" : \"Light\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\",\n        title: `Current theme: ${getLabel()}`,\n        children: [\n            getIcon(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"hidden sm:inline\",\n                children: getLabel()\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n// Theme Selector Dropdown\nfunction ThemeSelector() {\n    const { theme, setTheme } = useTheme();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const themes = [\n        {\n            value: \"light\",\n            label: \"Light\",\n            icon: \"☀️\"\n        },\n        {\n            value: \"dark\",\n            label: \"Dark\",\n            icon: \"\\uD83C\\uDF19\"\n        },\n        {\n            value: \"system\",\n            label: \"System\",\n            icon: \"\\uD83D\\uDCBB\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: themes.find((t)=>t.value === theme)?.icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:inline\",\n                        children: themes.find((t)=>t.value === theme)?.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-1\",\n                            children: themes.map((themeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setTheme(themeOption.value);\n                                        setIsOpen(false);\n                                    },\n                                    className: `w-full flex items-center space-x-3 px-4 py-2 text-sm text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 ${theme === themeOption.value ? \"bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300\" : \"text-gray-700 dark:text-gray-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: themeOption.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: themeOption.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        theme === themeOption.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 ml-auto\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, themeOption.value, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useSiteContent.ts":
/*!*********************************!*\
  !*** ./hooks/useSiteContent.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSiteContent: () => (/* binding */ useSiteContent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n\n\nconst useSiteContent = (sections)=>{\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const loadContent = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading site content...\");\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.getSiteContent)();\n            console.log(\"\\uD83D\\uDCC4 Site content loaded:\", data);\n            if (data && Array.isArray(data)) {\n                const contentMap = {};\n                data.forEach((item)=>{\n                    if (item && item.section && item.key) {\n                        const sectionKey = `${item.section}.${item.key}`;\n                        try {\n                            // Parse JSON value, fallback to string if parsing fails\n                            contentMap[sectionKey] = typeof item.value === \"string\" ? JSON.parse(item.value) : item.value;\n                        } catch  {\n                            contentMap[sectionKey] = item.value;\n                        }\n                    }\n                });\n                setContent(contentMap);\n            } else {\n                // If no data or invalid data, just set empty content (don't error)\n                setContent({});\n            }\n        } catch (err) {\n            console.warn(\"⚠️ Site content not available, using defaults:\", err);\n            setError(null); // Don't set error, just use defaults\n            setContent({}); // Set empty content so defaults are used\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        loadContent();\n    }, []);\n    const getContent = (section, key, defaultValue = \"\")=>{\n        const contentKey = `${section}.${key}`;\n        return content[contentKey] || defaultValue;\n    };\n    const refreshContent = async ()=>{\n        await loadContent();\n    };\n    return {\n        content,\n        loading,\n        error,\n        getContent,\n        refreshContent\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useSiteContent.ts\n");

/***/ }),

/***/ "(ssr)/./lib/data-sync.ts":
/*!**************************!*\
  !*** ./lib/data-sync.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConflictResolutionService: () => (/* binding */ ConflictResolutionService),\n/* harmony export */   DataSyncService: () => (/* binding */ DataSyncService),\n/* harmony export */   DataValidationService: () => (/* binding */ DataValidationService),\n/* harmony export */   cleanupDataSync: () => (/* binding */ cleanupDataSync),\n/* harmony export */   dataSyncService: () => (/* binding */ dataSyncService)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase-client */ \"(ssr)/./lib/supabase-client.ts\");\n\n// Data synchronization service for real-time updates between web and bot\nclass DataSyncService {\n    // Subscribe to real-time updates for a specific table\n    subscribeToTable(tableName, callback, filter) {\n        // Add callback to subscribers\n        if (!this.subscribers.has(tableName)) {\n            this.subscribers.set(tableName, new Set());\n        }\n        this.subscribers.get(tableName).add(callback);\n        // Create channel if it doesn't exist\n        if (!this.channels.has(tableName)) {\n            const channel = _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.channel(`${tableName}_changes`).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: tableName,\n                ...filter && {\n                    filter: `${filter.column}=eq.${filter.value}`\n                }\n            }, (payload)=>{\n                // Notify all subscribers\n                const subscribers = this.subscribers.get(tableName);\n                if (subscribers) {\n                    subscribers.forEach((cb)=>cb(payload));\n                }\n            }).subscribe();\n            this.channels.set(tableName, channel);\n        }\n        // Return unsubscribe function\n        return ()=>{\n            const subscribers = this.subscribers.get(tableName);\n            if (subscribers) {\n                subscribers.delete(callback);\n                // If no more subscribers, remove channel\n                if (subscribers.size === 0) {\n                    const channel = this.channels.get(tableName);\n                    if (channel) {\n                        _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.removeChannel(channel);\n                        this.channels.delete(tableName);\n                        this.subscribers.delete(tableName);\n                    }\n                }\n            }\n        };\n    }\n    // Subscribe to user-specific updates\n    subscribeToUserUpdates(userId, callback) {\n        const unsubscribeFunctions = [];\n        // Subscribe to user's share purchases\n        unsubscribeFunctions.push(this.subscribeToTable(\"share_purchases\", callback, {\n            column: \"user_id\",\n            value: userId\n        }));\n        // Subscribe to user's payments\n        unsubscribeFunctions.push(this.subscribeToTable(\"crypto_payment_transactions\", callback, {\n            column: \"user_id\",\n            value: userId\n        }));\n        // Subscribe to user's commission transactions\n        unsubscribeFunctions.push(this.subscribeToTable(\"commission_transactions\", callback, {\n            column: \"user_id\",\n            value: userId\n        }));\n        // Subscribe to user's referrals\n        unsubscribeFunctions.push(this.subscribeToTable(\"referrals\", callback, {\n            column: \"referrer_user_id\",\n            value: userId\n        }));\n        // Subscribe to user's KYC information\n        unsubscribeFunctions.push(this.subscribeToTable(\"kyc_information\", callback, {\n            column: \"user_id\",\n            value: userId\n        }));\n        // Return function to unsubscribe from all\n        return ()=>{\n            unsubscribeFunctions.forEach((unsub)=>unsub());\n        };\n    }\n    // Clean up all subscriptions\n    cleanup() {\n        this.channels.forEach((channel)=>{\n            _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.removeChannel(channel);\n        });\n        this.channels.clear();\n        this.subscribers.clear();\n    }\n    constructor(){\n        this.channels = new Map();\n        this.subscribers = new Map();\n    }\n}\n// Data validation service to ensure consistency\nclass DataValidationService {\n    // Validate user data consistency\n    static async validateUserData(userId) {\n        const errors = [];\n        const warnings = [];\n        try {\n            // Get user data\n            const { data: userData, error: userError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"telegram_users\").select(\"*\").eq(\"id\", userId).single();\n            if (userError || !userData) {\n                errors.push(\"User not found in telegram_users table\");\n                return {\n                    isValid: false,\n                    errors,\n                    warnings\n                };\n            }\n            // Validate share purchases\n            const { data: shareData, error: shareError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"share_purchases\").select(\"*\").eq(\"user_id\", userId);\n            if (shareError) {\n                errors.push(`Error fetching share purchases: ${shareError.message}`);\n            } else if (shareData) {\n                // Validate share purchase totals\n                const totalShares = shareData.reduce((sum, purchase)=>sum + (purchase.shares_purchased || 0), 0);\n                const totalAmount = shareData.reduce((sum, purchase)=>sum + (purchase.total_amount || 0), 0);\n                if (totalShares < 0) {\n                    errors.push(\"Negative total shares detected\");\n                }\n                if (totalAmount < 0) {\n                    errors.push(\"Negative total amount detected\");\n                }\n            }\n            // Validate commission balance\n            const { data: commissionData, error: commissionError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"commission_balances\").select(\"*\").eq(\"user_id\", userId).single();\n            if (commissionError && commissionError.code !== \"PGRST116\") {\n                warnings.push(`Commission balance check failed: ${commissionError.message}`);\n            } else if (commissionData) {\n                if (commissionData.usdt_balance < 0) {\n                    errors.push(\"Negative commission balance detected\");\n                }\n            }\n            // Validate payment transactions\n            const { data: paymentData, error: paymentError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"crypto_payment_transactions\").select(\"*\").eq(\"user_id\", userId);\n            if (paymentError) {\n                warnings.push(`Payment transaction check failed: ${paymentError.message}`);\n            } else if (paymentData) {\n                const pendingPayments = paymentData.filter((p)=>p.status === \"pending\");\n                if (pendingPayments.length > 10) {\n                    warnings.push(\"High number of pending payments detected\");\n                }\n            }\n            // Validate referral data\n            const { data: referralData, error: referralError } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"referrals\").select(\"*\").eq(\"referrer_user_id\", userId);\n            if (referralError) {\n                warnings.push(`Referral data check failed: ${referralError.message}`);\n            }\n        } catch (error) {\n            errors.push(`Validation error: ${error}`);\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            warnings\n        };\n    }\n    // Validate business logic consistency\n    static async validateBusinessLogic() {\n        const errors = [];\n        const warnings = [];\n        try {\n            // Check for orphaned records\n            const { data: orphanedShares } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"share_purchases\").select(\"id, user_id\").not(\"user_id\", \"in\", `(SELECT id FROM telegram_users)`);\n            if (orphanedShares && orphanedShares.length > 0) {\n                errors.push(`Found ${orphanedShares.length} orphaned share purchases`);\n            }\n            // Check for negative balances\n            const { data: negativeBalances } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"commission_balances\").select(\"user_id, usdt_balance\").lt(\"usdt_balance\", 0);\n            if (negativeBalances && negativeBalances.length > 0) {\n                errors.push(`Found ${negativeBalances.length} negative commission balances`);\n            }\n            // Check for duplicate referrals\n            const { data: duplicateReferrals } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"referrals\").select(\"referred_user_id, count(*)\").group(\"referred_user_id\").having(\"count(*)\", \"gt\", 1);\n            if (duplicateReferrals && duplicateReferrals.length > 0) {\n                warnings.push(`Found ${duplicateReferrals.length} users with multiple referrers`);\n            }\n            // Check for inconsistent phase data\n            const { data: phaseData } = await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"investment_phases\").select(\"*\").eq(\"is_active\", true);\n            if (phaseData && phaseData.length > 1) {\n                errors.push(\"Multiple active investment phases detected\");\n            } else if (!phaseData || phaseData.length === 0) {\n                warnings.push(\"No active investment phase found\");\n            }\n        } catch (error) {\n            errors.push(`Business logic validation error: ${error}`);\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            warnings\n        };\n    }\n}\n// Conflict resolution service\nclass ConflictResolutionService {\n    // Resolve conflicts in concurrent updates\n    static async resolveUserDataConflict(userId, conflictType, webData, botData) {\n        switch(conflictType){\n            case \"commission\":\n                // For commission conflicts, use the higher balance (safer approach)\n                const finalCommission = Math.max(webData.usdt_balance || 0, botData.usdt_balance || 0);\n                return {\n                    resolved: true,\n                    finalData: {\n                        ...webData,\n                        usdt_balance: finalCommission\n                    },\n                    strategy: \"max_balance\"\n                };\n            case \"shares\":\n                // For share conflicts, use the higher share count (safer approach)\n                const finalShares = Math.max(webData.shares_purchased || 0, botData.shares_purchased || 0);\n                return {\n                    resolved: true,\n                    finalData: {\n                        ...webData,\n                        shares_purchased: finalShares\n                    },\n                    strategy: \"max_shares\"\n                };\n            case \"payment\":\n                // For payment conflicts, prefer 'approved' status over others\n                const statusPriority = {\n                    \"approved\": 3,\n                    \"pending\": 2,\n                    \"rejected\": 1\n                };\n                const webPriority = statusPriority[webData.status] || 0;\n                const botPriority = statusPriority[botData.status] || 0;\n                const finalStatus = webPriority >= botPriority ? webData.status : botData.status;\n                return {\n                    resolved: true,\n                    finalData: {\n                        ...webData,\n                        status: finalStatus\n                    },\n                    strategy: \"status_priority\"\n                };\n            default:\n                return {\n                    resolved: false,\n                    finalData: webData,\n                    strategy: \"no_resolution\"\n                };\n        }\n    }\n}\n// Create singleton instance\nconst dataSyncService = new DataSyncService();\n// Cleanup function for app shutdown\nconst cleanupDataSync = ()=>{\n    dataSyncService.cleanup();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/data-sync.ts\n");

/***/ }),

/***/ "(ssr)/./lib/error-handling.ts":
/*!*******************************!*\
  !*** ./lib/error-handling.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppError: () => (/* binding */ AppError),\n/* harmony export */   DatabaseOperations: () => (/* binding */ DatabaseOperations),\n/* harmony export */   ErrorLogger: () => (/* binding */ ErrorLogger),\n/* harmony export */   ErrorSeverity: () => (/* binding */ ErrorSeverity),\n/* harmony export */   ErrorType: () => (/* binding */ ErrorType),\n/* harmony export */   ValidationHelper: () => (/* binding */ ValidationHelper),\n/* harmony export */   setupGlobalErrorHandling: () => (/* binding */ setupGlobalErrorHandling)\n/* harmony export */ });\n/* harmony import */ var _supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase-client */ \"(ssr)/./lib/supabase-client.ts\");\n\nvar ErrorType;\n(function(ErrorType) {\n    ErrorType[\"DATABASE\"] = \"database\";\n    ErrorType[\"VALIDATION\"] = \"validation\";\n    ErrorType[\"AUTHENTICATION\"] = \"authentication\";\n    ErrorType[\"AUTHORIZATION\"] = \"authorization\";\n    ErrorType[\"NETWORK\"] = \"network\";\n    ErrorType[\"BUSINESS_LOGIC\"] = \"business_logic\";\n    ErrorType[\"SYSTEM\"] = \"system\";\n})(ErrorType || (ErrorType = {}));\nvar ErrorSeverity;\n(function(ErrorSeverity) {\n    ErrorSeverity[\"LOW\"] = \"low\";\n    ErrorSeverity[\"MEDIUM\"] = \"medium\";\n    ErrorSeverity[\"HIGH\"] = \"high\";\n    ErrorSeverity[\"CRITICAL\"] = \"critical\";\n})(ErrorSeverity || (ErrorSeverity = {}));\n// Custom error class for application errors\nclass AppError extends Error {\n    constructor(message, type = \"system\", severity = \"medium\", context = {}, userId){\n        super(message);\n        this.name = \"AppError\";\n        this.type = type;\n        this.severity = severity;\n        this.context = context;\n        this.timestamp = new Date();\n        this.userId = userId;\n        // Capture browser context if available\n        if (false) {}\n        // Maintain proper stack trace\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, AppError);\n        }\n    }\n}\n// Error logging service\nclass ErrorLogger {\n    // Log error to database and console\n    static async logError(error, additionalContext) {\n        try {\n            // Prepare error data\n            const errorData = {\n                message: error.message,\n                stack: error.stack,\n                type: error instanceof AppError ? error.type : \"system\",\n                severity: error instanceof AppError ? error.severity : \"medium\",\n                context: {\n                    ...error instanceof AppError ? error.context : {},\n                    ...additionalContext\n                },\n                user_id: error instanceof AppError ? error.userId : null,\n                user_agent: error instanceof AppError ? error.userAgent : null,\n                url: error instanceof AppError ? error.url : null,\n                timestamp: new Date().toISOString()\n            };\n            // Log to console in development\n            if (true) {\n                console.error(\"Application Error:\", errorData);\n            }\n            // Log to database (create error_logs table if needed)\n            await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"error_logs\").insert(errorData);\n        } catch (logError) {\n            // Fallback to console if database logging fails\n            console.error(\"Failed to log error to database:\", logError);\n            console.error(\"Original error:\", error);\n        }\n    }\n    // Log user action for audit trail\n    static async logUserAction(userId, action, details = {}, success = true) {\n        try {\n            await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"user_action_logs\").insert({\n                user_id: userId,\n                action,\n                details,\n                success,\n                timestamp: new Date().toISOString(),\n                user_agent:  false ? 0 : null,\n                url:  false ? 0 : null\n            });\n        } catch (error) {\n            console.error(\"Failed to log user action:\", error);\n        }\n    }\n    // Log system event\n    static async logSystemEvent(event, details = {}, severity = \"low\") {\n        try {\n            await _supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"system_event_logs\").insert({\n                event,\n                details,\n                severity,\n                timestamp: new Date().toISOString()\n            });\n        } catch (error) {\n            console.error(\"Failed to log system event:\", error);\n        }\n    }\n}\n// Database operation wrapper with error handling\nclass DatabaseOperations {\n    // Safe database query with error handling\n    static async safeQuery(operation, context = {}) {\n        try {\n            const result = await operation();\n            if (result.error) {\n                const appError = new AppError(`Database operation failed: ${result.error.message}`, \"database\", \"high\", {\n                    ...context,\n                    originalError: result.error\n                });\n                await ErrorLogger.logError(appError);\n                return {\n                    data: null,\n                    error: appError\n                };\n            }\n            return {\n                data: result.data,\n                error: null\n            };\n        } catch (error) {\n            const appError = new AppError(`Database operation exception: ${error}`, \"database\", \"critical\", {\n                ...context,\n                originalError: error\n            });\n            await ErrorLogger.logError(appError);\n            return {\n                data: null,\n                error: appError\n            };\n        }\n    }\n    // Transaction wrapper with rollback\n    static async safeTransaction(operations, context = {}) {\n        const results = [];\n        try {\n            // Execute all operations\n            for (const operation of operations){\n                const result = await operation();\n                results.push(result);\n            }\n            return {\n                success: true,\n                results,\n                error: null\n            };\n        } catch (error) {\n            const appError = new AppError(`Transaction failed: ${error}`, \"database\", \"high\", {\n                ...context,\n                originalError: error,\n                completedOperations: results.length\n            });\n            await ErrorLogger.logError(appError);\n            // Log transaction rollback\n            await ErrorLogger.logSystemEvent(\"transaction_rollback\", {\n                context,\n                completedOperations: results.length,\n                totalOperations: operations.length\n            }, \"medium\");\n            return {\n                success: false,\n                results: [],\n                error: appError\n            };\n        }\n    }\n}\n// Validation helper with error handling\nclass ValidationHelper {\n    // Validate required fields\n    static validateRequired(data, requiredFields, context = {}) {\n        const errors = [];\n        requiredFields.forEach((field)=>{\n            if (!data[field] || typeof data[field] === \"string\" && data[field].trim() === \"\") {\n                errors.push(`${field} is required`);\n            }\n        });\n        if (errors.length > 0) {\n            const appError = new AppError(`Validation failed: ${errors.join(\", \")}`, \"validation\", \"medium\", {\n                ...context,\n                validationErrors: errors,\n                data\n            });\n            return {\n                isValid: false,\n                errors,\n                appError\n            };\n        }\n        return {\n            isValid: true,\n            errors: []\n        };\n    }\n    // Validate data types\n    static validateTypes(data, typeMap, context = {}) {\n        const errors = [];\n        Object.entries(typeMap).forEach(([field, expectedType])=>{\n            if (data[field] !== undefined && typeof data[field] !== expectedType) {\n                errors.push(`${field} must be of type ${expectedType}`);\n            }\n        });\n        if (errors.length > 0) {\n            const appError = new AppError(`Type validation failed: ${errors.join(\", \")}`, \"validation\", \"medium\", {\n                ...context,\n                typeErrors: errors,\n                data\n            });\n            return {\n                isValid: false,\n                errors,\n                appError\n            };\n        }\n        return {\n            isValid: true,\n            errors: []\n        };\n    }\n    // Validate business rules\n    static validateBusinessRules(data, rules, context = {}) {\n        const errors = [];\n        rules.forEach((rule)=>{\n            const result = rule(data);\n            if (!result.isValid && result.error) {\n                errors.push(result.error);\n            }\n        });\n        if (errors.length > 0) {\n            const appError = new AppError(`Business rule validation failed: ${errors.join(\", \")}`, \"business_logic\", \"high\", {\n                ...context,\n                businessRuleErrors: errors,\n                data\n            });\n            return {\n                isValid: false,\n                errors,\n                appError\n            };\n        }\n        return {\n            isValid: true,\n            errors: []\n        };\n    }\n}\n// Global error handler for unhandled errors\nconst setupGlobalErrorHandling = ()=>{\n    // Handle unhandled promise rejections\n    if (false) {}\n} // Error handling utilities are exported above as individual classes\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/error-handling.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase-client.ts":
/*!********************************!*\
  !*** ./lib/supabase-client.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCommissionBalance: () => (/* binding */ getCommissionBalance),\n/* harmony export */   getCurrentPhase: () => (/* binding */ getCurrentPhase),\n/* harmony export */   getUserByTelegramId: () => (/* binding */ getUserByTelegramId),\n/* harmony export */   getUserPayments: () => (/* binding */ getUserPayments),\n/* harmony export */   getUserReferrals: () => (/* binding */ getUserReferrals),\n/* harmony export */   getUserShares: () => (/* binding */ getUserShares),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://fgubaqoftdeefcakejwu.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI\";\nif (!supabaseAnonKey) {\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true\n    }\n});\n// Helper functions for database operations\nconst getUserByTelegramId = async (telegramId)=>{\n    const { data, error } = await supabase.from(\"telegram_users\").select(\"*\").eq(\"telegram_id\", telegramId).single();\n    if (error) {\n        console.error(\"Error fetching user:\", error);\n        return null;\n    }\n    return data;\n};\nconst getUserShares = async (userId)=>{\n    const { data, error } = await supabase.from(\"aureus_share_purchases\").select(\"*\").eq(\"user_id\", userId).eq(\"status\", \"active\");\n    if (error) {\n        console.error(\"Error fetching user shares:\", error);\n        return [];\n    }\n    return data || [];\n};\nconst getUserPayments = async (userId)=>{\n    const { data, error } = await supabase.from(\"crypto_payment_transactions\").select(\"*\").eq(\"user_id\", userId).order(\"created_at\", {\n        ascending: false\n    });\n    if (error) {\n        console.error(\"Error fetching user payments:\", error);\n        return [];\n    }\n    return data || [];\n};\nconst getUserReferrals = async (userId)=>{\n    const { data, error } = await supabase.from(\"referrals\").select(`\n      *,\n      referred_user:telegram_users!referrals_referred_id_fkey(\n        first_name,\n        last_name,\n        username,\n        created_at\n      )\n    `).eq(\"sponsor_id\", userId);\n    if (error) {\n        console.error(\"Error fetching user referrals:\", error);\n        return [];\n    }\n    return data || [];\n};\nconst getCommissionBalance = async (userId)=>{\n    const { data, error } = await supabase.from(\"commission_balances\").select(\"*\").eq(\"user_id\", userId).single();\n    if (error) {\n        console.error(\"Error fetching commission balance:\", error);\n        return null;\n    }\n    return data;\n};\nconst getCurrentPhase = async ()=>{\n    const { data, error } = await supabase.from(\"investment_phases\").select(\"*\").eq(\"is_active\", true).single();\n    if (error) {\n        console.error(\"Error fetching current phase:\", error);\n        return null;\n    }\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAdminUser: () => (/* binding */ getAdminUser),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSiteContent: () => (/* binding */ getSiteContent),\n/* harmony export */   isUserAdmin: () => (/* binding */ isUserAdmin),\n/* harmony export */   signInWithEmail: () => (/* binding */ signInWithEmail),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUpWithEmail: () => (/* binding */ signUpWithEmail),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   updateSiteContent: () => (/* binding */ updateSiteContent)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://fgubaqoftdeefcakejwu.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI\";\n// Validate environment variables\nconsole.log(\"\\uD83D\\uDD0D Supabase Config Check:\");\nconsole.log(\"URL:\", supabaseUrl);\nconsole.log(\"Anon Key exists:\", !!supabaseAnonKey);\nconsole.log(\"Anon Key length:\", supabaseAnonKey?.length || 0);\nif (!supabaseAnonKey || supabaseAnonKey.includes(\"PASTE_YOUR_REAL_ANON_KEY_HERE\")) {\n    console.error(\"❌ SUPABASE CONFIGURATION ERROR:\");\n    console.error(\"Please set NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env file\");\n    console.error(\"Get your anon key from: https://supabase.com/dashboard → Settings → API\");\n} else {\n    console.log(\"✅ Supabase configuration looks good\");\n}\n// Create Supabase client with error handling\nlet supabase;\ntry {\n    if (!supabaseAnonKey) {\n        throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY\");\n    }\n    supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n    console.log(\"✅ Supabase client created successfully\");\n} catch (error) {\n    console.error(\"❌ Supabase connection failed:\", error);\n    // Create a mock client for development\n    supabase = {\n        auth: {\n            signInWithPassword: ()=>Promise.resolve({\n                    data: {\n                        user: null\n                    },\n                    error: {\n                        message: \"Supabase not configured\"\n                    }\n                }),\n            signOut: ()=>Promise.resolve({\n                    error: null\n                }),\n            getUser: ()=>Promise.resolve({\n                    data: {\n                        user: null\n                    },\n                    error: null\n                })\n        },\n        from: ()=>({\n                select: ()=>({\n                        eq: ()=>({\n                                single: ()=>Promise.resolve({\n                                        data: null,\n                                        error: {\n                                            message: \"Supabase not configured\"\n                                        }\n                                    })\n                            })\n                    })\n            })\n    };\n}\n\n// Content management functions (with fallback)\nconst getSiteContent = async (section)=>{\n    try {\n        let query = supabase.from(\"site_content\").select(\"*\");\n        if (section) {\n            query = query.eq(\"section\", section);\n        }\n        const { data, error } = await query.order(\"section\", {\n            ascending: true\n        });\n        if (error) {\n            console.error(\"Error fetching site content:\", error);\n            // Return empty array as fallback\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error(\"Site content table not available:\", error);\n        return [];\n    }\n};\nconst updateSiteContent = async (section, key, value, userEmail)=>{\n    try {\n        const { data, error } = await supabase.from(\"site_content\").upsert({\n            section,\n            key,\n            value,\n            updated_by: userEmail,\n            updated_at: new Date().toISOString()\n        }, {\n            onConflict: \"section,key\"\n        }).select();\n        if (error) {\n            console.error(\"Error updating site content:\", error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Site content table not available:\", error);\n        return null;\n    }\n};\n// Authentication functions\nconst signInWithEmail = async (email, password)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Attempting login for:\", email);\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            console.error(\"❌ Auth error:\", error.message);\n            // For development, allow test credentials\n            if (email === \"<EMAIL>\" && password === \"admin123\") {\n                console.log(\"\\uD83E\\uDDEA Using test credentials - bypassing Supabase auth\");\n                const testUser = {\n                    id: \"test-admin-id\",\n                    email: \"<EMAIL>\",\n                    user_metadata: {\n                        role: \"super_admin\"\n                    },\n                    created_at: new Date().toISOString(),\n                    aud: \"authenticated\",\n                    role: \"authenticated\"\n                };\n                // Store test user in localStorage for persistence\n                localStorage.setItem(\"aureus_test_user\", JSON.stringify(testUser));\n                console.log(\"✅ Test user stored in localStorage\");\n                return {\n                    user: testUser,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error\n            };\n        }\n        console.log(\"✅ Login successful for:\", data.user?.email);\n        return {\n            user: data.user,\n            error: null\n        };\n    } catch (err) {\n        console.error(\"❌ Login exception:\", err);\n        return {\n            user: null,\n            error: {\n                message: \"Login failed\"\n            }\n        };\n    }\n};\nconst signUpWithEmail = async (email, password)=>{\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password\n    });\n    if (error) {\n        console.error(\"Error signing up:\", error);\n        return {\n            user: null,\n            error\n        };\n    }\n    return {\n        user: data.user,\n        error: null\n    };\n};\nconst signOut = async ()=>{\n    try {\n        const { error } = await supabase.auth.signOut();\n        // Always clear test user from localStorage\n        localStorage.removeItem(\"aureus_test_user\");\n        console.log(\"\\uD83E\\uDDEA Test user cleared from localStorage\");\n        if (error) {\n            console.error(\"Error signing out:\", error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error signing out:\", error);\n        // Still clear test user even if Supabase signOut fails\n        localStorage.removeItem(\"aureus_test_user\");\n        return false;\n    }\n};\nconst getCurrentUser = async ()=>{\n    try {\n        // First check for test user in localStorage\n        const testUser = localStorage.getItem(\"aureus_test_user\");\n        if (testUser) {\n            console.log(\"\\uD83E\\uDDEA Using test user from localStorage\");\n            return JSON.parse(testUser);\n        }\n        // Then try Supabase auth\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error) {\n            console.warn(\"Supabase auth error (expected in test mode):\", error.message);\n            return null;\n        }\n        return user;\n    } catch (error) {\n        console.warn(\"Error getting current user (expected in test mode):\", error);\n        return null;\n    }\n};\n// Check if user is admin using existing admin_users table\nconst isUserAdmin = async (email)=>{\n    try {\n        // For development, allow test admin\n        if (email === \"<EMAIL>\") {\n            console.log(\"\\uD83E\\uDDEA Test admin access granted\");\n            return true;\n        }\n        const { data, error } = await supabase.from(\"admin_users\").select(\"*\").eq(\"email\", email).single();\n        if (error) {\n            console.log(\"Admin check error:\", error);\n            // For development, also check known admin emails\n            const knownAdmins = [\n                \"<EMAIL>\",\n                \"<EMAIL>\"\n            ];\n            if (knownAdmins.includes(email)) {\n                console.log(\"\\uD83E\\uDDEA Known admin email, granting access\");\n                return true;\n            }\n            return false;\n        }\n        return !!data && (data.role === \"admin\" || data.role === \"super_admin\");\n    } catch (error) {\n        console.error(\"Error checking admin_users table:\", error);\n        return false;\n    }\n};\n// Get admin user details\nconst getAdminUser = async (email)=>{\n    try {\n        const { data, error } = await supabase.from(\"admin_users\").select(\"*\").eq(\"email\", email).single();\n        if (error) {\n            console.log(\"Admin user fetch error:\", error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error fetching admin user:\", error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateRandomColor: () => (/* binding */ generateRandomColor),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   hexToRgb: () => (/* binding */ hexToRgb),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n// Utility function to merge Tailwind CSS classes\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Format currency values\nfunction formatCurrency(amount, currency = \"USD\", locale = \"en-US\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n}\n// Format numbers with commas\nfunction formatNumber(value, locale = \"en-US\") {\n    return new Intl.NumberFormat(locale).format(value);\n}\n// Format dates\nfunction formatDate(date, options = {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\"\n}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleDateString(\"en-US\", options);\n}\n// Format relative time (e.g., \"2 hours ago\")\nfunction formatRelativeTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"Just now\";\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minute${minutes > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hour${hours > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 2592000) {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} day${days > 1 ? \"s\" : \"\"} ago`;\n    } else {\n        return formatDate(dateObj);\n    }\n}\n// Truncate text with ellipsis\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\n// Generate random ID\nfunction generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n// Debounce function\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Throttle function\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Copy to clipboard\nasync function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        // Fallback for older browsers\n        const textArea = document.createElement(\"textarea\");\n        textArea.value = text;\n        document.body.appendChild(textArea);\n        textArea.focus();\n        textArea.select();\n        try {\n            document.execCommand(\"copy\");\n            document.body.removeChild(textArea);\n            return true;\n        } catch (fallbackError) {\n            document.body.removeChild(textArea);\n            return false;\n        }\n    }\n}\n// Validate email\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Validate phone number\nfunction isValidPhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\n// Calculate percentage\nfunction calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return Math.round(value / total * 100);\n}\n// Sleep function for delays\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Check if device is mobile\nfunction isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n// Get initials from name\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0).toUpperCase()).join(\"\").substring(0, 2);\n}\n// Color utilities\nfunction hexToRgb(hex) {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result ? {\n        r: parseInt(result[1], 16),\n        g: parseInt(result[2], 16),\n        b: parseInt(result[3], 16)\n    } : null;\n}\n// Generate random color\nfunction generateRandomColor() {\n    const colors = [\n        \"#3B82F6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#8B5CF6\",\n        \"#06B6D4\",\n        \"#84CC16\",\n        \"#F97316\",\n        \"#EC4899\",\n        \"#6366F1\"\n    ];\n    return colors[Math.floor(Math.random() * colors.length)];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./aureus-design-system.css":
/*!**********************************!*\
  !*** ./aureus-design-system.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"425cd7b95346\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hdXJldXMtZGVzaWduLXN5c3RlbS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hdXJldXMtYWxsaWFuY2UtaG9sZGluZ3MvLi9hdXJldXMtZGVzaWduLXN5c3RlbS5jc3M/YzE5MiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQyNWNkN2I5NTM0NlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./aureus-design-system.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _aureus_design_system_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../aureus-design-system.css */ \"(rsc)/./aureus-design-system.css\");\n/* harmony import */ var _contexts_SiteContentContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SiteContentContext */ \"(rsc)/./contexts/SiteContentContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toast */ \"(rsc)/./components/ui/Toast.tsx\");\n/* harmony import */ var _components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ErrorBoundary */ \"(rsc)/./components/ui/ErrorBoundary.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Aureus Alliance Holdings\",\n    description: \"Gold placer deposit mining with blockchain-backed ownership\",\n    keywords: \"gold mining, placer deposits, blockchain, share ownership, South Africa\",\n    authors: [\n        {\n            name: \"Aureus Alliance Holdings\"\n        }\n    ],\n    creator: \"Aureus Alliance Holdings\",\n    publisher: \"Aureus Alliance Holdings\",\n    openGraph: {\n        title: \"Aureus Alliance Holdings\",\n        description: \"Gold placer deposit mining with blockchain-backed ownership\",\n        url: \"https://aureus.africa\",\n        siteName: \"Aureus Alliance Holdings\",\n        locale: \"en_US\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorBoundary__WEBPACK_IMPORTED_MODULE_6__.ErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.ToastProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SiteContentContext__WEBPACK_IMPORTED_MODULE_2__.SiteContentProvider, {\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\aureus_africa\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ui/ErrorBoundary.tsx":
/*!*****************************************!*\
  !*** ./components/ui/ErrorBoundary.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AsyncErrorBoundary: () => (/* binding */ e3),
/* harmony export */   ErrorBoundary: () => (/* binding */ e0),
/* harmony export */   useErrorBoundary: () => (/* binding */ e1),
/* harmony export */   withErrorBoundary: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#ErrorBoundary`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#useErrorBoundary`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#withErrorBoundary`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#AsyncErrorBoundary`);


/***/ }),

/***/ "(rsc)/./components/ui/Toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/Toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   toast: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e1),
/* harmony export */   useToastActions: () => (/* binding */ e3)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#useToast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#toast`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#useToastActions`);


/***/ }),

/***/ "(rsc)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\AuthContext.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./contexts/SiteContentContext.tsx":
/*!*****************************************!*\
  !*** ./contexts/SiteContentContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SiteContentProvider: () => (/* binding */ e0),
/* harmony export */   useContent: () => (/* binding */ e2),
/* harmony export */   useSiteContentContext: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#SiteContentProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#useSiteContentContext`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#useContent`);


/***/ }),

/***/ "(rsc)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   ThemeSelector: () => (/* binding */ e3),
/* harmony export */   ThemeToggle: () => (/* binding */ e2),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#useTheme`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeToggle`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeSelector`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/@opentelemetry","vendor-chunks/tailwind-merge","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Caureus_africa&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();