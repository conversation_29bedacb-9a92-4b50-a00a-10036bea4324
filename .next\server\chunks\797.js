exports.id=797,exports.ids=[797],exports.modules={58359:()=>{},93739:()=>{},46845:(e,t,r)=>{Promise.resolve().then(r.bind(r,34834)),Promise.resolve().then(r.bind(r,78998)),Promise.resolve().then(r.bind(r,84979)),Promise.resolve().then(r.bind(r,44804)),Promise.resolve().then(r.bind(r,26125))},8963:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},34834:(e,t,r)=>{"use strict";r.d(t,{ErrorBoundary:()=>n});var s=r(10326),a=r(17577),o=r(16395);class n extends a.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t});let r=new o.gz(`React Error Boundary: ${e.message}`,o.NI.SYSTEM,o.Wk.HIGH,{stack:e.stack,componentStack:t.componentStack,originalError:e});o.$G.logError(r),this.props.onError&&this.props.onError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx(i,{error:this.state.error,errorInfo:this.state.errorInfo,showDetails:this.props.showDetails,onRetry:()=>this.setState({hasError:!1,error:void 0,errorInfo:void 0})}):this.props.children}}function i({error:e,errorInfo:t,showDetails:r=!1,onRetry:a}){let o=()=>{};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center",children:[s.jsx("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900 mb-6",children:s.jsx("svg",{className:"h-8 w-8 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),s.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Something went wrong"}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"We're sorry, but something unexpected happened. Please try again."}),r&&e&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-left",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"Error Details:"}),s.jsx("pre",{className:"text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap break-words",children:e.message}),t&&(0,s.jsxs)("details",{className:"mt-2",children:[s.jsx("summary",{className:"text-xs text-gray-600 dark:text-gray-400 cursor-pointer",children:"Component Stack"}),s.jsx("pre",{className:"text-xs text-gray-500 dark:text-gray-500 mt-1 whitespace-pre-wrap break-words",children:t.componentStack})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[s.jsx("button",{onClick:()=>{a?a():o()},className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Try Again"}),s.jsx("button",{onClick:o,className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Reload Page"})]}),(0,s.jsxs)("p",{className:"mt-6 text-sm text-gray-500 dark:text-gray-400",children:["If this problem persists, please"," ",s.jsx("a",{href:"https://t.me/AureusAllianceBot",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"contact support"})]})]})})}},78998:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>i,x_:()=>d});var s=r(10326),a=r(17577),o=r(77863);let n=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)([]),o=(0,a.useCallback)(e=>{let t=Math.random().toString(36).substring(2,9),s={...e,id:t,duration:e.duration??5e3};r(e=>[...e,s]),s.duration>0&&setTimeout(()=>{i(t)},s.duration)},[]),i=(0,a.useCallback)(e=>{r(t=>t.filter(t=>t.id!==e))},[]),l=(0,a.useCallback)(()=>{r([])},[]);return(0,s.jsxs)(n.Provider,{value:{toasts:t,addToast:o,removeToast:i,clearToasts:l},children:[e,s.jsx(c,{})]})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function c(){let{toasts:e}=l();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full",children:e.map(e=>s.jsx(u,{toast:e},e.id))})}function u({toast:e}){let{removeToast:t}=l(),r={success:{bg:"bg-green-50 border-green-200",icon:"✅",iconColor:"text-green-500",titleColor:"text-green-800",messageColor:"text-green-700"},error:{bg:"bg-red-50 border-red-200",icon:"❌",iconColor:"text-red-500",titleColor:"text-red-800",messageColor:"text-red-700"},warning:{bg:"bg-yellow-50 border-yellow-200",icon:"⚠️",iconColor:"text-yellow-500",titleColor:"text-yellow-800",messageColor:"text-yellow-700"},info:{bg:"bg-blue-50 border-blue-200",icon:"ℹ️",iconColor:"text-blue-500",titleColor:"text-blue-800",messageColor:"text-blue-700"}}[e.type];return s.jsx("div",{className:(0,o.cn)("border rounded-lg p-4 shadow-lg animate-in slide-in-from-right-full duration-300",r.bg),children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:(0,o.cn)("text-lg mr-3",r.iconColor),children:r.icon}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[e.title&&s.jsx("h4",{className:(0,o.cn)("text-sm font-semibold mb-1",r.titleColor),children:e.title}),s.jsx("p",{className:(0,o.cn)("text-sm",r.messageColor),children:e.message}),e.action&&s.jsx("button",{onClick:e.action.onClick,className:(0,o.cn)("mt-2 text-sm font-medium underline hover:no-underline",r.titleColor),children:e.action.label})]}),s.jsx("button",{onClick:()=>t(e.id),className:(0,o.cn)("ml-3 text-lg hover:opacity-70 transition-opacity",r.iconColor),children:"\xd7"})]})})}function d(){let{addToast:e}=l();return{success:(t,r)=>{e({type:"success",message:t,...r})},error:(t,r)=>{e({type:"error",message:t,...r})},warning:(t,r)=>{e({type:"warning",message:t,...r})},info:(t,r)=>{e({type:"info",message:t,...r})}}}},84979:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>l,a:()=>c});var s=r(10326),a=r(17577),o=r(91579);class n{subscribeToTable(e,t,r){if(this.subscribers.has(e)||this.subscribers.set(e,new Set),this.subscribers.get(e).add(t),!this.channels.has(e)){let t=o.OQ.channel(`${e}_changes`).on("postgres_changes",{event:"*",schema:"public",table:e,...r&&{filter:`${r.column}=eq.${r.value}`}},t=>{let r=this.subscribers.get(e);r&&r.forEach(e=>e(t))}).subscribe();this.channels.set(e,t)}return()=>{let r=this.subscribers.get(e);if(r&&(r.delete(t),0===r.size)){let t=this.channels.get(e);t&&(o.OQ.removeChannel(t),this.channels.delete(e),this.subscribers.delete(e))}}}subscribeToUserUpdates(e,t){let r=[];return r.push(this.subscribeToTable("share_purchases",t,{column:"user_id",value:e})),r.push(this.subscribeToTable("crypto_payment_transactions",t,{column:"user_id",value:e})),r.push(this.subscribeToTable("commission_transactions",t,{column:"user_id",value:e})),r.push(this.subscribeToTable("referrals",t,{column:"referrer_user_id",value:e})),r.push(this.subscribeToTable("kyc_information",t,{column:"user_id",value:e})),()=>{r.forEach(e=>e())}}cleanup(){this.channels.forEach(e=>{o.OQ.removeChannel(e)}),this.channels.clear(),this.subscribers.clear()}constructor(){this.channels=new Map,this.subscribers=new Map}}new n,r(16395);let i=(0,a.createContext)(void 0);function l({children:e}){let[t,r]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[c,u]=(0,a.useState)(null),d=async()=>{if(!t?.telegram_profile)return{hasAcceptedTerms:!1,hasSelectedCountry:!1,hasCompletedKYC:!1};try{let{data:e}=await o.OQ.from("terms_acceptance").select("*").eq("user_id",t.telegram_profile.id).single(),r=!!t.telegram_profile.country,{data:s}=await o.OQ.from("kyc_information").select("*").eq("user_id",t.telegram_profile.id).single();return{hasAcceptedTerms:!!e,hasSelectedCountry:r,hasCompletedKYC:!!s}}catch(e){return console.error("Error checking onboarding status:",e),{hasAcceptedTerms:!1,hasSelectedCountry:!1,hasCompletedKYC:!1}}},m=async()=>{try{let{data:{user:e}}=await o.OQ.auth.getUser();if(e){let t=e.user_metadata?.telegram_id;if(t){let{data:s}=await o.OQ.from("telegram_users").select("*").eq("telegram_id",t).single();if(s){let t=await d();r({...e,telegram_profile:s,...t})}else r(e)}else r(e)}else r(null)}catch(e){console.error("Error refreshing user:",e),r(null)}},h=async e=>{try{if(!e.id||!e.first_name)return{success:!1,error:"Invalid Telegram data"};let{data:t}=await o.OQ.from("telegram_users").select("*").eq("telegram_id",e.id.toString()).single();if(t){let{data:t,error:r}=await o.OQ.auth.signInWithPassword({email:`telegram_${e.id}@aureus.africa`,password:`telegram_${e.id}_auth`});if(r){let{data:t,error:r}=await o.OQ.auth.signUp({email:`telegram_${e.id}@aureus.africa`,password:`telegram_${e.id}_auth`,options:{data:{telegram_id:e.id,telegram_username:e.username,telegram_first_name:e.first_name,telegram_last_name:e.last_name}}});if(r)return{success:!1,error:"Authentication failed"}}return await m(),{success:!0}}{let{data:t,error:r}=await o.OQ.from("telegram_users").insert({telegram_id:e.id.toString(),username:e.username||"",first_name:e.first_name,last_name:e.last_name||"",registration_mode:"web"}).select().single();if(r)return{success:!1,error:"Failed to create user profile"};let{data:s,error:a}=await o.OQ.auth.signUp({email:`telegram_${e.id}@aureus.africa`,password:`telegram_${e.id}_auth`,options:{data:{telegram_id:e.id,telegram_username:e.username,telegram_first_name:e.first_name,telegram_last_name:e.last_name}}});if(a)return{success:!1,error:"Authentication setup failed"};return await m(),{success:!0}}}catch(e){return console.error("Telegram sign in error:",e),{success:!1,error:"Sign in failed"}}},g=async()=>{try{await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),r(null),window.location.href="/login"}catch(e){console.error("Sign out error:",e),r(null),window.location.href="/login"}},x=async()=>{try{let e=await fetch("/api/auth/refresh",{method:"POST",credentials:"include"});if(e.ok&&(await e.json()).success)return await m(),!0;return r(null),!1}catch(e){return console.error("Session refresh error:",e),r(null),!1}};return s.jsx(i.Provider,{value:{user:t,loading:n,signInWithTelegram:h,signOut:g,refreshUser:m,refreshSession:x,checkOnboardingStatus:d},children:e})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44804:(e,t,r)=>{"use strict";let s;r.d(t,{SiteContentProvider:()=>m});var a=r(10326),o=r(17577),n=r(52997);let i=(void 0).VITE_SUPABASE_URL||"https://fgubaqoftdeefcakejwu.supabase.co",l=(void 0).VITE_SUPABASE_ANON_KEY;console.log("\uD83D\uDD0D Supabase Config Check:"),console.log("URL:",i),console.log("Anon Key exists:",!!l),console.log("Anon Key length:",l?.length||0),!l||l.includes("PASTE_YOUR_REAL_ANON_KEY_HERE")?(console.error("❌ SUPABASE CONFIGURATION ERROR:"),console.error("Please set VITE_SUPABASE_ANON_KEY in your .env file"),console.error("Get your anon key from: https://supabase.com/dashboard → Settings → API")):console.log("✅ Supabase configuration looks good");try{if(!l)throw Error("Missing VITE_SUPABASE_ANON_KEY");s=(0,n.eI)(i,l),console.log("✅ Supabase client created successfully")}catch(e){console.error("❌ Supabase connection failed:",e),s={auth:{signInWithPassword:()=>Promise.resolve({data:{user:null},error:{message:"Supabase not configured"}}),signOut:()=>Promise.resolve({error:null}),getUser:()=>Promise.resolve({data:{user:null},error:null})},from:()=>({select:()=>({eq:()=>({single:()=>Promise.resolve({data:null,error:{message:"Supabase not configured"}})})})})}}let c=async e=>{try{let t=s.from("site_content").select("*");e&&(t=t.eq("section",e));let{data:r,error:a}=await t.order("section",{ascending:!0});if(a)return console.error("Error fetching site content:",a),[];return r||[]}catch(e){return console.error("Site content table not available:",e),[]}},u=e=>{let[t,r]=(0,o.useState)({}),[s,a]=(0,o.useState)(!0),[n,i]=(0,o.useState)(null),l=async()=>{a(!0),i(null);try{console.log("\uD83D\uDD04 Loading site content...");let e=await c();if(console.log("\uD83D\uDCC4 Site content loaded:",e),e&&Array.isArray(e)){let t={};e.forEach(e=>{if(e&&e.section&&e.key){let r=`${e.section}.${e.key}`;try{t[r]="string"==typeof e.value?JSON.parse(e.value):e.value}catch{t[r]=e.value}}}),r(t)}else r({})}catch(e){console.warn("⚠️ Site content not available, using defaults:",e),i(null),r({})}finally{a(!1)}};return(0,o.useEffect)(()=>{l()},[]),{content:t,loading:s,error:n,getContent:(e,r,s="")=>t[`${e}.${r}`]||s,refreshContent:async()=>{await l()}}},d=(0,o.createContext)(void 0),m=({children:e})=>{let t=u();return a.jsx(d.Provider,{value:t,children:e})}},26125:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(10326),a=r(17577);let o=(0,a.createContext)(void 0);function n({children:e,defaultTheme:t="system",storageKey:r="aureus-theme"}){let[n,i]=(0,a.useState)(t),[l,c]=(0,a.useState)("light");return s.jsx(o.Provider,{value:{theme:n,setTheme:e=>{i(e),localStorage.setItem(r,e)},resolvedTheme:l},children:e})}},16395:(e,t,r)=>{"use strict";r.d(t,{$G:()=>i,NI:()=>s,Wk:()=>a,gz:()=>n});var s,a,o=r(91579);(function(e){e.DATABASE="database",e.VALIDATION="validation",e.AUTHENTICATION="authentication",e.AUTHORIZATION="authorization",e.NETWORK="network",e.BUSINESS_LOGIC="business_logic",e.SYSTEM="system"})(s||(s={})),function(e){e.LOW="low",e.MEDIUM="medium",e.HIGH="high",e.CRITICAL="critical"}(a||(a={}));class n extends Error{constructor(e,t="system",r="medium",s={},a){super(e),this.name="AppError",this.type=t,this.severity=r,this.context=s,this.timestamp=new Date,this.userId=a,Error.captureStackTrace&&Error.captureStackTrace(this,n)}}class i{static async logError(e,t){try{let r={message:e.message,stack:e.stack,type:e instanceof n?e.type:"system",severity:e instanceof n?e.severity:"medium",context:{...e instanceof n?e.context:{},...t},user_id:e instanceof n?e.userId:null,user_agent:e instanceof n?e.userAgent:null,url:e instanceof n?e.url:null,timestamp:new Date().toISOString()};await o.OQ.from("error_logs").insert(r)}catch(t){console.error("Failed to log error to database:",t),console.error("Original error:",e)}}static async logUserAction(e,t,r={},s=!0){try{await o.OQ.from("user_action_logs").insert({user_id:e,action:t,details:r,success:s,timestamp:new Date().toISOString(),user_agent:null,url:null})}catch(e){console.error("Failed to log user action:",e)}}static async logSystemEvent(e,t={},r="low"){try{await o.OQ.from("system_event_logs").insert({event:e,details:t,severity:r,timestamp:new Date().toISOString()})}catch(e){console.error("Failed to log system event:",e)}}}},91579:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>o});var s=r(52997);let a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let o=(0,s.eI)("https://fgubaqoftdeefcakejwu.supabase.co",a,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0}})},77863:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(41135),a=r(31009);function o(...e){return(0,a.m6)((0,s.W)(e))}},73375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>m});var s=r(19510),a=r(77366),o=r.n(a);r(21682);var n=r(68570);let i=(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#SiteContentProvider`);(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#useSiteContentContext`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\SiteContentContext.tsx#useContent`);let l=(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\AuthContext.tsx#AuthProvider`);(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\AuthContext.tsx#useAuth`);let c=(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeProvider`);(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#useTheme`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeToggle`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\contexts\ThemeContext.tsx#ThemeSelector`);let u=(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#ToastProvider`);(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#useToast`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#toast`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\Toast.tsx#useToastActions`);let d=(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#ErrorBoundary`);(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#useErrorBoundary`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#withErrorBoundary`),(0,n.createProxy)(String.raw`C:\xampp\htdocs\aureus_africa\components\ui\ErrorBoundary.tsx#AsyncErrorBoundary`);let m={title:"Aureus Alliance Holdings",description:"Gold placer deposit mining with blockchain-backed ownership",keywords:"gold mining, placer deposits, blockchain, share ownership, South Africa",authors:[{name:"Aureus Alliance Holdings"}],creator:"Aureus Alliance Holdings",publisher:"Aureus Alliance Holdings",openGraph:{title:"Aureus Alliance Holdings",description:"Gold placer deposit mining with blockchain-backed ownership",url:"https://aureus.africa",siteName:"Aureus Alliance Holdings",locale:"en_US",type:"website"}};function h({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:o().className,children:s.jsx(d,{children:s.jsx(c,{children:s.jsx(u,{children:s.jsx(l,{children:s.jsx(i,{children:e})})})})})})})}},21682:()=>{}};