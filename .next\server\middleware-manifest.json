{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|logo.png|images|static).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|logo.png|images|static).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "--NshhhPLN4gvh8aVQb9E", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "iHip80ywo9CgwsMD7Rq5+Xv86lFAOIkOM9cJOq2mQbA=", "__NEXT_PREVIEW_MODE_ID": "8151bd1d970e97a7c3c87252c9c35728", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fd05c66abc4755eeb34879be7ccb61ac8180b7025efd20a65aad1863c4d87225", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eedcbf2c915e23fe18191464d6e5854cbc72356f833b3e744df73c80800ad755"}}}, "functions": {}, "sortedMiddleware": ["/"]}