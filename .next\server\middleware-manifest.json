{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ha2ARA6LBq3vhb8omFiFXNVO3xdvYxvVGBwEIgwC9vw="}}}, "functions": {}, "sortedMiddleware": ["/"]}