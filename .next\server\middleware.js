(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{67:e=>{"use strict";e.exports=require("node:async_hooks")},195:e=>{"use strict";e.exports=require("node:buffer")},402:(e,t,r)=>{"use strict";let i;r.r(t),r.d(t,{default:()=>r7});var n,s,a,o,l,u,c,h,d,f,p,g,m,v,w,y,b,_,k,S,x,T,E,R={};async function P(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(R),r.d(R,{config:()=>r6,middleware:()=>r4});let j=null;function C(){return j||(j=P()),j}function O(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(O(e))},construct(){throw Error(O(e))},apply(r,i,n){if("function"==typeof n[0])return n[0](t);throw Error(O(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),C();class A extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class I extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class $ extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let N={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};function L(e){var t,r,i,n,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=n,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function U(e){let t={},r=[];if(e)for(let[i,n]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...L(n)),t[i]=1===r.length?r[0]:r):t[i]=n;return t}function M(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}({...N,GROUP:{serverOnly:[N.reactServerComponents,N.actionBrowser,N.appMetadataRoute,N.appRouteHandler,N.instrument],clientOnly:[N.serverSideRendering,N.appPagesBrowser],nonClientServerTarget:[N.middleware,N.api],app:[N.reactServerComponents,N.actionBrowser,N.appMetadataRoute,N.appRouteHandler,N.serverSideRendering,N.appPagesBrowser,N.shared,N.instrument]}});let q=Symbol("response"),D=Symbol("passThrough"),B=Symbol("waitUntil");class H{constructor(e){this[B]=[],this[D]=!1}respondWith(e){this[q]||(this[q]=Promise.resolve(e))}passThroughOnException(){this[D]=!0}waitUntil(e){this[B].push(e)}}class F extends H{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new A({page:this.sourcePage})}respondWith(){throw new A({page:this.sourcePage})}}function W(e){return e.replace(/\/$/,"")||"/"}function z(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function V(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:n}=z(e);return""+t+r+i+n}function G(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:n}=z(e);return""+r+t+i+n}function J(e,t){if("string"!=typeof e)return!1;let{pathname:r}=z(e);return r===t||r.startsWith(t+"/")}function K(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let X=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function Y(e,t){return new URL(String(e).replace(X,"localhost"),t&&String(t).replace(X,"localhost"))}let Z=Symbol("NextURLInternal");class Q{constructor(e,t,r){let i,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,n=r||{}):n=r||t||{},this[Z]={url:Y(e,i??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,i,n;let s=function(e,t){var r,i;let{basePath:n,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};n&&J(o.pathname,n)&&(o.pathname=function(e,t){if(!J(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,n),o.basePath=n);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];o.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):K(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):K(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[Z].url.pathname,{nextConfig:this[Z].options.nextConfig,parseData:!0,i18nProvider:this[Z].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[Z].url,this[Z].options.headers);this[Z].domainLocale=this[Z].options.i18nProvider?this[Z].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var i,n;if(t===(null==(i=s.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(n=s.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[Z].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[Z].domainLocale)?void 0:r.defaultLocale)||(null==(n=this[Z].options.nextConfig)?void 0:null==(i=n.i18n)?void 0:i.defaultLocale);this[Z].url.pathname=s.pathname,this[Z].defaultLocale=o,this[Z].basePath=s.basePath??"",this[Z].buildId=s.buildId,this[Z].locale=s.locale??o,this[Z].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let n=e.toLowerCase();return!i&&(J(n,"/api")||J(n,"/"+t.toLowerCase()))?e:V(e,"/"+t)}((e={basePath:this[Z].basePath,buildId:this[Z].buildId,defaultLocale:this[Z].options.forceLocale?void 0:this[Z].defaultLocale,locale:this[Z].locale,pathname:this[Z].url.pathname,trailingSlash:this[Z].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=W(t)),e.buildId&&(t=G(V(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=V(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:G(t,"/"):W(t)}formatSearch(){return this[Z].url.search}get buildId(){return this[Z].buildId}set buildId(e){this[Z].buildId=e}get locale(){return this[Z].locale??""}set locale(e){var t,r;if(!this[Z].locale||!(null==(r=this[Z].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[Z].locale=e}get defaultLocale(){return this[Z].defaultLocale}get domainLocale(){return this[Z].domainLocale}get searchParams(){return this[Z].url.searchParams}get host(){return this[Z].url.host}set host(e){this[Z].url.host=e}get hostname(){return this[Z].url.hostname}set hostname(e){this[Z].url.hostname=e}get port(){return this[Z].url.port}set port(e){this[Z].url.port=e}get protocol(){return this[Z].url.protocol}set protocol(e){this[Z].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[Z].url=Y(e),this.analyze()}get origin(){return this[Z].url.origin}get pathname(){return this[Z].url.pathname}set pathname(e){this[Z].url.pathname=e}get hash(){return this[Z].url.hash}set hash(e){this[Z].url.hash=e}get search(){return this[Z].url.search}set search(e){this[Z].url.search=e}get password(){return this[Z].url.password}set password(e){this[Z].url.password=e}get username(){return this[Z].url.username}set username(e){this[Z].url.username=e}get basePath(){return this[Z].basePath}set basePath(e){this[Z].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new Q(String(this),this[Z].options)}}var ee=r(945);let et=Symbol("internal request");class er extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);M(r),e instanceof Request?super(e,t):super(r,t);let i=new Q(r,{headers:U(this.headers),nextConfig:t.nextConfig});this[et]={cookies:new ee.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[et].cookies}get geo(){return this[et].geo}get ip(){return this[et].ip}get nextUrl(){return this[et].nextUrl}get page(){throw new I}get ua(){throw new $}get url(){return this[et].url}}class ei{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let en=Symbol("internal response"),es=new Set([301,302,303,307,308]);function ea(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,n]of e.request.headers)t.set("x-middleware-request-"+i,n),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class eo extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new ee.ResponseCookies(r),{get(e,i,n){switch(i){case"delete":case"set":return(...n)=>{let s=Reflect.apply(e[i],e,n),a=new Headers(r);return s instanceof ee.ResponseCookies&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,ee.stringifyCookie)(e)).join(",")),ea(t,a),s};default:return ei.get(e,i,n)}}});this[en]={cookies:i,url:t.url?new Q(t.url,{headers:U(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[en].cookies}static json(e,t){let r=Response.json(e,t);return new eo(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!es.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",M(e)),new eo(null,{...i,headers:n,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",M(e)),ea(t,r),new eo(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),ea(e,t),new eo(null,{...e,headers:t})}}function el(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),n=r.protocol+"//"+r.host;return i.protocol+"//"+i.host===n?i.toString().replace(n,""):i.toString()}let eu=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],ec=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eh=["__nextDataReq"];class ed extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ed}}class ef extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return ei.get(t,r,i);let n=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==s)return ei.get(t,s,i)},set(t,r,i,n){if("symbol"==typeof r)return ei.set(t,r,i,n);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);return ei.set(t,a??r,i,n)},has(t,r){if("symbol"==typeof r)return ei.has(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==n&&ei.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return ei.deleteProperty(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===n||ei.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return ed.callable;default:return ei.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new ef(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let ep=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class eg{disable(){throw ep}getStore(){}run(){throw ep}exit(){throw ep}enterWith(){throw ep}}let em=globalThis.AsyncLocalStorage;function ev(){return em?new em:new eg}let ew=ev();class ey extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new ey}}class eb{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ey.callable;default:return ei.get(e,t,r)}}})}}let e_=Symbol.for("next.mutated.cookies");class ek{static wrap(e,t){let r=new ee.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],n=new Set,s=()=>{let e=ew.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>n.has(e.name)),t){let e=[];for(let t of i){let r=new ee.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case e_:return i;case"delete":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{s()}};case"set":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{s()}};default:return ei.get(e,t,r)}}})}}!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(n||(n={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(s||(s={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(a||(a={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(o||(o={})),(l||(l={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(u||(u={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(c||(c={})),(h||(h={})).executeRoute="Router.executeRoute",(d||(d={})).runHandler="Node.runHandler",(f||(f={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(p||(p={})),(g||(g={})).execute="Middleware.execute";let eS=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ex=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:eT,propagation:eE,trace:eR,SpanStatusCode:eP,SpanKind:ej,ROOT_CONTEXT:eC}=i=r(608),eO=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,eA=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eP.ERROR,message:null==t?void 0:t.message})),e.end()},eI=new Map,e$=i.createContextKey("next.rootSpanId"),eN=0,eL=()=>eN++;class eU{getTracerInstance(){return eR.getTracer("next.js","0.0.1")}getContext(){return eT}getActiveScopeSpan(){return eR.getSpan(null==eT?void 0:eT.active())}withPropagatedContext(e,t,r){let i=eT.active();if(eR.getSpanContext(i))return t();let n=eE.extract(i,e,r);return eT.with(n,t)}trace(...e){var t;let[r,i,n]=e,{fn:s,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:n,options:{...i}},o=a.spanName??r;if(!eS.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),u=!1;l?(null==(t=eR.getSpanContext(l))?void 0:t.isRemote)&&(u=!0):(l=(null==eT?void 0:eT.active())??eC,u=!0);let c=eL();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},eT.with(l.setValue(e$,c),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,i=()=>{eI.delete(c),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ex.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};u&&eI.set(c,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>eA(e,t));let t=s(e);if(eO(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eA(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw eA(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,n]=3===e.length?e:[e[0],{},e[1]];return eS.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof n&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>n.apply(this,arguments));{let i=t.getContext().bind(eT.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),i.apply(this,arguments)},n.apply(this,arguments)))}}:n}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?eR.setSpan(eT.active(),e):void 0}getRootSpanAttributes(){let e=eT.active().getValue(e$);return eI.get(e)}}let eM=(()=>{let e=new eU;return()=>e})(),eq="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eq);class eD{constructor(e,t,r,i){var n;let s=e&&function(e,t){let r=ef.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(n=r.get(eq))?void 0:n.value;this.isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eq,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eq,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function eB(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of L(r))i.append("set-cookie",e);for(let e of new ee.ResponseCookies(i).getAll())t.set(e)}}let eH={wrap(e,{req:t,res:r,renderOpts:i},n){let s;function a(e){r&&r.setHeader("Set-Cookie",e)}i&&"previewProps"in i&&(s=i.previewProps);let o={},l={get headers(){return o.headers||(o.headers=function(e){let t=ef.from(e);for(let e of eu)t.delete(e.toString().toLowerCase());return ef.seal(t)}(t.headers)),o.headers},get cookies(){if(!o.cookies){let e=new ee.RequestCookies(ef.from(t.headers));eB(t,e),o.cookies=eb.seal(e)}return o.cookies},get mutableCookies(){if(!o.mutableCookies){let e=function(e,t){let r=new ee.RequestCookies(ef.from(e));return ek.wrap(r,t)}(t.headers,(null==i?void 0:i.onUpdateCookies)||(r?a:void 0));eB(t,e),o.mutableCookies=e}return o.mutableCookies},get draftMode(){return o.draftMode||(o.draftMode=new eD(s,t,this.cookies,this.mutableCookies)),o.draftMode},reactLoadableManifest:(null==i?void 0:i.reactLoadableManifest)||{},assetPrefix:(null==i?void 0:i.assetPrefix)||""};return e.run(l,n,l)}},eF=ev();function eW(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class ez extends er{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new A({page:this.sourcePage})}respondWith(){throw new A({page:this.sourcePage})}waitUntil(){throw new A({page:this.sourcePage})}}let eV={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eG=(e,t)=>eM().withPropagatedContext(e.headers,t,eV),eJ=!1;async function eK(e){let t,i;!function(){if(!eJ&&(eJ=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(177);e(),eG=t(eG)}}(),await C();let n=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let s=new Q(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...s.searchParams.keys()]){let t=s.searchParams.getAll(e);!function(e,t){for(let r of["nxtP","nxtI"])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}(e,r=>{for(let e of(s.searchParams.delete(r),t))s.searchParams.append(r,e);s.searchParams.delete(e)})}let a=s.buildId;s.buildId="";let o=e.request.headers["x-nextjs-data"];o&&"/index"===s.pathname&&(s.pathname="/");let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),u=new Map;if(!n)for(let e of eu){let t=e.toString().toLowerCase();l.get(t)&&(u.set(t,l.get(t)),l.delete(t))}let c=new ez({page:e.page,input:(function(e,t){let r="string"==typeof e,i=r?new URL(e):e;for(let e of ec)i.searchParams.delete(e);if(t)for(let e of eh)i.searchParams.delete(e);return r?i.toString():i})(s,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:l,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});o&&Object.defineProperty(c,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eW()})}));let h=new F({request:c,page:e.page});if((t=await eG(c,()=>"/middleware"===e.page||"/src/middleware"===e.page?eM().trace(g.execute,{spanName:`middleware ${c.method} ${c.nextUrl.pathname}`,attributes:{"http.target":c.nextUrl.pathname,"http.method":c.method}},()=>eH.wrap(eF,{req:c,renderOpts:{onUpdateCookies:e=>{i=e},previewProps:eW()}},()=>e.handler(c,h))):e.handler(c,h)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&i&&t.headers.set("set-cookie",i);let d=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&d&&!n){let r=new Q(d,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===c.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let i=el(String(r),String(s));o&&t.headers.set("x-nextjs-rewrite",i)}let f=null==t?void 0:t.headers.get("Location");if(t&&f&&!n){let r=new Q(f,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===c.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("Location",String(r))),o&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",el(String(r),String(s))))}let p=t||eo.next(),m=p.headers.get("x-middleware-override-headers"),v=[];if(m){for(let[e,t]of u)p.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&p.headers.set("x-middleware-override-headers",m+","+v.join(","))}return{response:p,waitUntil:Promise.all(h[B]),fetchMetrics:c.fetchMetrics}}let eX=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,254)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)};class eY extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class eZ extends eY{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class eQ extends eY{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class e0 extends eY{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(m||(m={}));class e1{constructor(e,{headers:t={},customFetch:r,region:i=m.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=eX(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,n,s,a;return i=this,n=void 0,s=void 0,a=function*(){try{let i;let{headers:n,method:s,body:a}=t,o={},{region:l}=t;l||(l=this.region);let u=new URL(`${this.url}/${e}`);l&&"any"!==l&&(o["x-region"]=l,u.searchParams.set("forceFunctionRegion",l)),a&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",i=a):"string"==typeof a?(o["Content-Type"]="text/plain",i=a):"undefined"!=typeof FormData&&a instanceof FormData?i=a:(o["Content-Type"]="application/json",i=JSON.stringify(a)));let c=yield this.fetch(u.toString(),{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),n),body:i}).catch(e=>{throw new eZ(e)}),h=c.headers.get("x-relay-error");if(h&&"true"===h)throw new eQ(c);if(!c.ok)throw new e0(c);let d=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),error:null,response:c}}catch(e){return{data:null,error:e,response:e instanceof e0||e instanceof eQ?e.context:void 0}}},new(s||(s=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof s?i:new s(function(e){e(i)})).then(r,o)}l((a=a.apply(i,n||[])).next())})}}let{PostgrestClient:e2,PostgrestQueryBuilder:e3,PostgrestFilterBuilder:e4,PostgrestTransformBuilder:e6,PostgrestBuilder:e5,PostgrestError:e8}=r(690),e9=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw Error("`WebSocket` is not supported in this environment")}();!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(v||(v={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(w||(w={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(y||(y={})),(b||(b={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(_||(_={}));class e7{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),n=t.getUint8(2),s=this.HEADER_LENGTH+2,a=r.decode(e.slice(s,s+i));s+=i;let o=r.decode(e.slice(s,s+n));return s+=n,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(s,e.byteLength)))}}}class te{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(k||(k={}));let tt=(e,t,r={})=>{var i;let n=null!==(i=r.skipTypes)&&void 0!==i?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=tr(i,e,t,n),r),{})},tr=(e,t,r,i)=>{let n=t.find(t=>t.name===e),s=null==n?void 0:n.type,a=r[e];return s&&!i.includes(s)?ti(s,a):tn(a)},ti=(e,t)=>{if("_"===e.charAt(0))return tl(t,e.slice(1,e.length));switch(e){case k.bool:return ts(t);case k.float4:case k.float8:case k.int2:case k.int4:case k.int8:case k.numeric:case k.oid:return ta(t);case k.json:case k.jsonb:return to(t);case k.timestamp:return tu(t);case k.abstime:case k.date:case k.daterange:case k.int4range:case k.int8range:case k.money:case k.reltime:case k.text:case k.time:case k.timestamptz:case k.timetz:case k.tsrange:case k.tstzrange:default:return tn(t)}},tn=e=>e,ts=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},ta=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},to=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},tl=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i;let n=e.slice(1,r);try{i=JSON.parse("["+n+"]")}catch(e){i=n?n.split(","):[]}return i.map(e=>ti(t,e))}return e},tu=e=>"string"==typeof e?e.replace(" ","T"):e,tc=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class th{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(S||(S={}));class td{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=td.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=td.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=td.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let n=this.cloneDeep(e),s=this.transformState(t),a={},o={};return this.map(n,(e,t)=>{s[e]||(o[e]=t)}),this.map(s,(e,t)=>{let r=n[e];if(r){let i=t.map(e=>e.presence_ref),n=r.map(e=>e.presence_ref),s=t.filter(e=>0>n.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));s.length>0&&(a[e]=s),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(n,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:n,leaves:s}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(n,(t,i)=>{var n;let s=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(i),s.length>0){let r=e[t].map(e=>e.presence_ref),i=s.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,s,i)}),this.map(s,(t,r)=>{let n=e[t];if(!n)return;let s=r.map(e=>e.presence_ref);n=n.filter(e=>0>s.indexOf(e.presence_ref)),e[t]=n,i(t,n,r),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(x||(x={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(T||(T={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(E||(E={}));class tf{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=w.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new th(this,y.join,this.params,this.timeout),this.rejoinTimer=new te(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=w.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=w.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=w.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=w.errored,this.rejoinTimer.scheduleTimeout())}),this._on(y.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new td(this),this.broadcastEndpointURL=tc(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==w.closed){let{config:{broadcast:n,presence:s,private:a}}=this.params;this._onError(t=>null==e?void 0:e(E.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(E.CLOSED));let o={},l={broadcast:n,presence:s,postgres_changes:null!==(i=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==i?i:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(E.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,n=null!==(r=null==i?void 0:i.length)&&void 0!==r?r:0,s=[];for(let r=0;r<n;r++){let n=i[r],{filter:{event:a,schema:o,table:l,filter:u}}=n,c=t&&t[r];if(c&&c.event===a&&c.schema===o&&c.table===l&&c.filter===u)s.push(Object.assign(Object.assign({},n),{id:c.id}));else{this.unsubscribe(),this.state=w.errored,null==e||e(E.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=s,e&&e(E.SUBSCRIBED);return}}).receive("error",t=>{this.state=w.errored,null==e||e(E.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(E.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,n,s;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(s=null===(n=null===(i=this.params)||void 0===i?void 0:i.config)||void 0===n?void 0:n.broadcast)||void 0===s?void 0:s.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:n,payload:s}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:s,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(i=e.body)||void 0===i?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=w.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(y.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{(r=new th(this,y.leave,{},e)).receive("ok",()=>{t(),i("ok")}).receive("timeout",()=>{t(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let i=new AbortController,n=setTimeout(()=>i.abort(),r),s=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(n),s}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new th(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,n;let s=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:u}=y;if(r&&[a,o,l,u].indexOf(s)>=0&&r!==this._joinRef())return;let c=this._onMessage(s,t,r);if(t&&!c)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(s)?null===(i=this.bindings.postgres_changes)||void 0===i||i.filter(e=>{var t,r,i;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(i=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===i?void 0:i.toLocaleLowerCase())===s}).map(e=>e.callback(c,r)):null===(n=this.bindings[s])||void 0===n||n.filter(e=>{var r,i,n,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(s))return e.type.toLocaleLowerCase()===s;if("id"in e){let s=e.id,a=null===(r=e.filter)||void 0===r?void 0:r.event;return s&&(null===(i=t.ids)||void 0===i?void 0:i.includes(s))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{let r=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof c&&"ids"in c){let e=c.data,{schema:t,table:r,commit_timestamp:i,type:n,errors:s}=e;c=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:n,new:{},old:{},errors:s}),this._getPayloadRecords(e))}e.callback(c,r)})}_isClosed(){return this.state===w.closed}_isJoined(){return this.state===w.joined}_isJoining(){return this.state===w.joining}_isLeaving(){return this.state===w.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),n={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(n):this.bindings[i]=[n],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null===(i=e.type)||void 0===i?void 0:i.toLocaleLowerCase())===r&&tf.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(y.close,{},e)}_onError(e){this._on(y.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=w.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=tt(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=tt(e.columns,e.old_record)),t}}let tp=()=>{},tg=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class tm{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=tp,this.ref=0,this.logger=tp,this.conn=null,this.sendBuffer=[],this.serializer=new e7,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,254)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},this.endPoint=`${e}/${b.websocket}`,this.httpEndpoint=tc(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let n=null===(i=null==t?void 0:t.params)||void 0===i?void 0:i.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new te(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=e9),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case v.connecting:return _.Connecting;case v.open:return _.Open;case v.closing:return _.Closing;default:return _.Closed}}isConnected(){return this.connectionState()===_.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===r);if(i)return i;{let r=new tf(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:i,ref:n}=e,s=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${n})`,i),this.isConnected()?s():this.sendBuffer.push(s)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:"realtime-js/2.11.15"}),e.joinedOnce&&e._isJoined()&&e._push(y.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:n}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${n&&"("+n+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(y.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([tg],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class tv extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function tw(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class ty extends tv{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class tb extends tv{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let t_=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,254)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},tk=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,254))).Response:Response}),tS=e=>{if(Array.isArray(e))return e.map(e=>tS(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=tS(r)}),t};var tx=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let tT=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tE=(e,t,r)=>tx(void 0,void 0,void 0,function*(){e instanceof(yield tk())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new ty(tT(r),e.status||500))}).catch(e=>{t(new tb(tT(e),e))}):t(new tb(tT(e),e))}),tR=(e,t,r,i)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(n.body=JSON.stringify(i)),Object.assign(Object.assign({},n),r))};function tP(e,t,r,i,n,s){return tx(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,tR(t,i,n,s)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>tE(e,o,i))})})}function tj(e,t,r,i){return tx(this,void 0,void 0,function*(){return tP(e,"GET",t,r,i)})}function tC(e,t,r,i,n){return tx(this,void 0,void 0,function*(){return tP(e,"POST",t,i,n,r)})}function tO(e,t,r,i,n){return tx(this,void 0,void 0,function*(){return tP(e,"DELETE",t,i,n,r)})}var tA=r(195).Buffer,tI=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let t$={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},tN={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class tL{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=t_(i)}uploadOrUpdate(e,t,r,i){return tI(this,void 0,void 0,function*(){try{let n;let s=Object.assign(Object.assign({},tN),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(s.upsert)}),o=s.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((n=new FormData).append("cacheControl",s.cacheControl),o&&n.append("metadata",this.encodeMetadata(o)),n.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((n=r).append("cacheControl",s.cacheControl),o&&n.append("metadata",this.encodeMetadata(o))):(n=r,a["cache-control"]=`max-age=${s.cacheControl}`,a["content-type"]=s.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:n,headers:a},(null==s?void 0:s.duplex)?{duplex:s.duplex}:{})),h=yield c.json();if(c.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}upload(e,t,r){return tI(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return tI(this,void 0,void 0,function*(){let n=this._removeEmptyFolders(e),s=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${s}`);a.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:tN.upsert},i),s=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,s["cache-control"]=`max-age=${t.cacheControl}`,s["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:s}),l=yield o.json();if(o.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return tI(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let n=yield tC(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),s=new URL(this.url+n.url),a=s.searchParams.get("token");if(!a)throw new tv("No token returned by API");return{data:{signedUrl:s.toString(),path:e,token:a},error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}update(e,t,r){return tI(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return tI(this,void 0,void 0,function*(){try{return{data:yield tC(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}copy(e,t,r){return tI(this,void 0,void 0,function*(){try{return{data:{path:(yield tC(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return tI(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),n=yield tC(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n={signedUrl:encodeURI(`${this.url}${n.signedURL}${s}`)},error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return tI(this,void 0,void 0,function*(){try{let i=yield tC(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}download(e,t){return tI(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield tj(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}info(e){return tI(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield tj(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:tS(e),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}exists(e){return tI(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return tx(this,void 0,void 0,function*(){return tP(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(tw(e)&&e instanceof tb){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&i.push(n);let s=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${s?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return tI(this,void 0,void 0,function*(){try{return{data:yield tO(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}list(e,t,r){return tI(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},t$),t),{prefix:e||""});return{data:yield tC(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==tA?tA.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let tU={"X-Client-Info":"storage-js/2.7.1"};var tM=function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class tq{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},tU),t),this.fetch=t_(r)}listBuckets(){return tM(this,void 0,void 0,function*(){try{return{data:yield tj(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}getBucket(e){return tM(this,void 0,void 0,function*(){try{return{data:yield tj(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return tM(this,void 0,void 0,function*(){try{return{data:yield tC(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return tM(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,n){return tx(this,void 0,void 0,function*(){return tP(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}emptyBucket(e){return tM(this,void 0,void 0,function*(){try{return{data:yield tC(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}deleteBucket(e){return tM(this,void 0,void 0,function*(){try{return{data:yield tO(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(tw(e))return{data:null,error:e};throw e}})}}class tD extends tq{constructor(e,t={},r){super(e,t,r)}from(e){return new tL(this.url,this.headers,e,this.fetch)}}let tB="";"undefined"!=typeof Deno?tB="deno":"undefined"!=typeof document?tB="web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?tB="react-native":tB="node";let tH={headers:{"X-Client-Info":`supabase-js-${tB}/2.50.3`}},tF={schema:"public"},tW={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},tz={};var tV=r(254);let tG=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=tV.default:t=fetch,(...e)=>t(...e)},tJ=()=>"undefined"==typeof Headers?tV.Headers:Headers,tK=(e,t,r)=>{let i=tG(r),n=tJ();return(r,s)=>(function(e,t,r,i){return new(r||(r=Promise))(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!==(a=yield t())&&void 0!==a?a:e,l=new n(null==s?void 0:s.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},s),{headers:l}))})},tX="2.70.0",tY={"X-Client-Info":`gotrue-js/${tX}`},tZ="X-Supabase-Api-Version",tQ={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},t0=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class t1 extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function t2(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class t3 extends t1{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class t4 extends t1{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class t6 extends t1{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class t5 extends t6{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class t8 extends t6{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class t9 extends t6{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class t7 extends t6{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class re extends t6{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rt extends t6{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function rr(e){return t2(e)&&"AuthRetryableFetchError"===e.name}class ri extends t6{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class rn extends t6{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let rs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),ra=" 	\n\r=".split(""),ro=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<ra.length;t+=1)e[ra[t].charCodeAt(0)]=-2;for(let t=0;t<rs.length;t+=1)e[rs[t].charCodeAt(0)]=t;return e})();function rl(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(rs[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(rs[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function ru(e,t,r){let i=ro[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function rc(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},s=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)ru(e.charCodeAt(t),n,s);return t.join("")}let rh=()=>"undefined"!=typeof window&&"undefined"!=typeof document,rd={tested:!1,writable:!1},rf=()=>{if(!rh())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(rd.tested)return rd.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),rd.tested=!0,rd.writable=!0}catch(e){rd.tested=!0,rd.writable=!1}return rd.writable},rp=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,254)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},rg=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,rm=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},rv=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},rw=async(e,t)=>{await e.removeItem(t)};class ry{constructor(){this.promise=new ry.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function rb(e){let t=e.split(".");if(3!==t.length)throw new rn("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!t0.test(t[e]))throw new rn("JWT not in base64url format");return{header:JSON.parse(rc(t[0])),payload:JSON.parse(rc(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)ru(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function r_(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function rk(e){return("0"+e.toString(16)).substr(-2)}async function rS(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function rx(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await rS(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function rT(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,rk).join("")}(),n=i;r&&(n+="/PASSWORD_RECOVERY"),await rm(e,`${t}-code-verifier`,n);let s=await rx(i),a=i===s?"plain":"s256";return[s,a]}ry.promiseConstructor=Promise;let rE=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,rR=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function rP(e){if(!rR.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var rj=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r};let rC=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),rO=[502,503,504];async function rA(e){var t;let r,i;if(!rg(e))throw new rt(rC(e),0);if(rO.includes(e.status))throw new rt(rC(e),e.status);try{r=await e.json()}catch(e){throw new t4(rC(e),e)}let n=function(e){let t=e.headers.get(tZ);if(!t||!t.match(rE))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(n&&n.getTime()>=tQ["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new ri(rC(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new t5}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new ri(rC(r),e.status,r.weak_password.reasons);throw new t3(rC(r),e.status||500,i)}let rI=(e,t,r,i)=>{let n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(i),Object.assign(Object.assign({},n),r))};async function r$(e,t,r,i){var n;let s=Object.assign({},null==i?void 0:i.headers);s[tZ]||(s[tZ]=tQ["2024-01-01"].name),(null==i?void 0:i.jwt)&&(s.Authorization=`Bearer ${i.jwt}`);let a=null!==(n=null==i?void 0:i.query)&&void 0!==n?n:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await rN(e,t,r+o,{headers:s,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function rN(e,t,r,i,n,s){let a;let o=rI(t,i,n,s);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new rt(rC(e),0)}if(a.ok||await rA(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await rA(e)}}function rL(e){var t,r;let i=null;return e.access_token&&e.refresh_token&&e.expires_in&&(i=Object.assign({},e),!e.expires_at)&&(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)),{data:{session:i,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function rU(e){let t=rL(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function rM(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function rq(e){return{data:e,error:null}}function rD(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:n,verification_type:s}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:n,verification_type:s},user:Object.assign({},rj(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function rB(e){return e}let rH=["global","local","others"];var rF=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r};class rW{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=rp(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=rH[0]){if(0>rH.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${rH.join(", ")}`);try{return await r$(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(t2(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await r$(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:rM})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=rF(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await r$(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:rD,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(t2(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await r$(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:rM})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,n,s,a,o;try{let l={nextPage:null,lastPage:0,total:0},u=await r$(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(n=null===(i=null==e?void 0:e.perPage)||void 0===i?void 0:i.toString())&&void 0!==n?n:""},xform:rB});if(u.error)throw u.error;let c=await u.json(),h=null!==(s=u.headers.get("x-total-count"))&&void 0!==s?s:0,d=null!==(o=null===(a=u.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(t2(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){rP(e);try{return await r$(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:rM})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){rP(e);try{return await r$(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:rM})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){rP(e);try{return await r$(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:rM})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){rP(e.userId);try{let{data:t,error:r}=await r$(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(t2(e))return{data:null,error:e};throw e}}async _deleteFactor(e){rP(e.userId),rP(e.id);try{return{data:await r$(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(t2(e))return{data:null,error:e};throw e}}}let rz={getItem:e=>rf()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{rf()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{rf()&&globalThis.localStorage.removeItem(e)}};function rV(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let rG={debug:!!(globalThis&&rf()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class rJ extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class rK extends rJ{}async function rX(e,t,r){rG.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),rG.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){rG.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{rG.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(0===t)throw rG.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new rK(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(rG.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let rY={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:tY,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function rZ(e,t,r){return await r()}class rQ{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=rQ.nextInstanceID,rQ.nextInstanceID+=1,this.instanceID>0&&rh()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},rY),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new rW({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=rp(i.fetch),this.lock=i.lock||rZ,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:rh()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=rX:this.lock=rZ,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:rf()?this.storage=rz:(this.memoryStorage={},this.storage=rV(this.memoryStorage)):(this.memoryStorage={},this.storage=rV(this.memoryStorage)),rh()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${tX}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),rh()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:n}=await this._getSessionFromURL(t,r);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),t2(n)&&"AuthImplicitGrantRedirectError"===n.name){let t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}let{session:s,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",s,"redirect type",a),await this._saveSession(s),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",s):await this._notifyAllSubscribers("SIGNED_IN",s)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(t2(e))return{error:e};return{error:new t4("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:n,error:s}=await r$(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken}},xform:rL});if(s||!n)return{data:{user:null,session:null},error:s};let a=n.session,o=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let n;if("email"in e){let{email:r,password:i,options:s}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await rT(this.storage,this.storageKey)),n=await r$(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==s?void 0:s.emailRedirectTo,body:{email:r,password:i,data:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:a,code_challenge_method:o},xform:rL})}else if("phone"in e){let{phone:t,password:s,options:a}=e;n=await r$(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:s,data:null!==(r=null==a?void 0:a.data)&&void 0!==r?r:{},channel:null!==(i=null==a?void 0:a.channel)&&void 0!==i?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:rL})}else throw new t9("You must provide either an email or phone number and a password");let{data:s,error:a}=n;if(a||!s)return{data:{user:null,session:null},error:a};let o=s.session,l=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:n}=e;t=await r$(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:rU})}else if("phone"in e){let{phone:r,password:i,options:n}=e;t=await r$(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:rU})}else throw new t9("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new t8};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,i,n,s,a,o,l,u,c,h,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let h;let{chain:d,wallet:g,statement:m,options:v}=e;if(rh()){if("object"==typeof g)h=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))h=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}}else{if("object"!=typeof g||!(null==v?void 0:v.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");h=g}let w=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in h&&h.signIn){let e;let t=await h.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:w.host,uri:w.href}),m?{statement:m}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in h)||"function"!=typeof h.signMessage||!("publicKey"in h)||"object"!=typeof h||!h.publicKey||!("toBase58"in h.publicKey)||"function"!=typeof h.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${w.host} wants you to sign in with your Solana account:`,h.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${w.href}`,`Issued At: ${null!==(i=null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==i?i:new Date().toISOString()}`,...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(c=null===(u=null==v?void 0:v.signInWithSolana)||void 0===u?void 0:u.resources)||void 0===c?void 0:c.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await h.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await r$(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>rl(e,r,i)),rl(null,r,i),t.join("")}(p)},(null===(h=e.options)||void 0===h?void 0:h.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:rL});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new t8};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await rv(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:n}=await r$(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:rL});if(await rw(this.storage,`${this.storageKey}-code-verifier`),n)throw n;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new t8};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:n}}catch(e){if(t2(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:n,nonce:s}=e,{data:a,error:o}=await r$(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:n,nonce:s,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:rL});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new t8};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,n,s;try{if("email"in e){let{email:i,options:n}=e,s=null,a=null;"pkce"===this.flowType&&([s,a]=await rT(this.storage,this.storageKey));let{error:o}=await r$(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(r=null==n?void 0:n.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:s,code_challenge_method:a},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await r$(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(i=null==r?void 0:r.data)&&void 0!==i?i:{},create_user:null===(n=null==r?void 0:r.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(s=null==r?void 0:r.channel)&&void 0!==s?s:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new t9("You must provide either an email or phone number.")}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,n;"options"in e&&(i=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:s,error:a}=await r$(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:i,xform:rL});if(a)throw a;if(!s)throw Error("An error occurred on token verification.");let o=s.session,l=s.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let n=null,s=null;return"pkce"===this.flowType&&([n,s]=await rT(this.storage,this.storageKey)),await r$(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:s}),headers:this.headers,xform:rq})}catch(e){if(t2(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new t5;let{error:i}=await r$(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:n}=e,{error:s}=await r$(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){let{phone:r,type:i,options:n}=e,{data:s,error:a}=await r$(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:a}}throw new t9("You must provide either an email or phone number and a type")}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await rv(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:n}=await this._callRefreshToken(e.refresh_token);if(n)return{data:{session:null},error:n};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await r$(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:rM});return await this._useSession(async e=>{var t,r,i;let{data:n,error:s}=e;if(s)throw s;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await r$(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(i=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0,xform:rM}):{data:{user:null},error:new t5}})}catch(e){if(t2(e))return t2(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await rw(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:n}=r;if(n)throw n;if(!i.session)throw new t5;let s=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await rT(this.storage,this.storageKey));let{data:l,error:u}=await r$(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:s.access_token,xform:rM});if(u)throw u;return s.user=l.user,await this._saveSession(s),await this._notifyAllSubscribers("USER_UPDATED",s),{data:{user:s.user},error:null}})}catch(e){if(t2(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new t5;let t=Date.now()/1e3,r=t,i=!0,n=null,{payload:s}=rb(e.access_token);if(s.exp&&(i=(r=s.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};n=t}else{let{data:i,error:s}=await this._getUser(e.access_token);if(s)throw s;n={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(e){if(t2(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:n}=t;if(n)throw n;e=null!==(r=i.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new t5;let{session:i,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(t2(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!rh())throw new t7("No browser detected.");if(e.error||e.error_description||e.error_code)throw new t7(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new re("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new t7("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new re("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:n,refresh_token:s,expires_in:a,expires_at:o,token_type:l}=e;if(!n||!a||!s||!l)throw new t7("No session defined in URL");let u=Math.round(Date.now()/1e3),c=parseInt(a),h=u+c;o&&(h=parseInt(o));let d=h-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${c}s`);let f=h-c;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,h,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,h,u);let{data:p,error:g}=await this._getUser(n);if(g)throw g;let m={provider_token:r,provider_refresh_token:i,access_token:n,expires_in:c,expires_at:h,refresh_token:s,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(t2(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await rv(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:n}=t;if(n)return{error:n};let s=null===(r=i.session)||void 0===r?void 0:r.access_token;if(s){let{error:t}=await this.admin.signOut(s,e);if(t&&!(t2(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await rw(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:n}=t;if(n)throw n;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null===(i=this.stateChangeEmitters.get(e))||void 0===i?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await rT(this.storage,this.storageKey,!0));try{return await r$(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(t2(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(t2(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,n,s,a;let{data:o,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(i=e.options)||void 0===i?void 0:i.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await r$(this.fetch,"GET",u,{headers:this.headers,jwt:null!==(a=null===(s=o.session)||void 0===s?void 0:s.access_token)&&void 0!==a?a:void 0})});if(i)throw i;return!rh()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(t2(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:n,error:s}=t;if(s)throw s;return await r$(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(i=null===(r=n.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0})})}catch(e){if(t2(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let n=Date.now();return await (r=async r=>(r>0&&await r_(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await r$(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:rL})),i=(e,t)=>t&&rr(t)&&Date.now()+200*Math.pow(2,e)-n<3e4,new Promise((e,t)=>{(async()=>{for(let n=0;n<1/0;n++)try{let t=await r(n);if(!i(n,null,t)){e(t);return}}catch(e){if(!i(n,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),t2(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),rh()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await rv(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!==(e=r.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),rr(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new t5;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new ry;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new t5;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),t2(e)){let r={session:null,error:e};return rr(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],n=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(n),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await rm(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await rw(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&rh()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof rJ)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!rh()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await rT(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;return n?{data:null,error:n}:await r$(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(t2(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:n,error:s}=t;if(s)return{data:null,error:s};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await r$(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(r=null==n?void 0:n.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(i=null==o?void 0:o.totp)||void 0===i?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(t2(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;if(n)return{data:null,error:n};let{data:s,error:a}=await r$(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+s.expires_in},s)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",s),{data:s,error:a})})}catch(e){if(t2(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:n}=t;return n?{data:null,error:n}:await r$(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(t2(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:n}=e;if(n)return{data:null,error:n};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:s}=rb(i.access_token),a=null;s.aal&&(a=s.aal);let o=a;return(null!==(r=null===(t=i.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:s.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:n}=await r$(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!i.keys||0===i.keys.length)throw new rn("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new rn("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:n,signature:s,raw:{header:a,payload:o}}=rb(r);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(n.exp),!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:n,header:i,signature:s},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),u=await this.fetchJwk(i.kid,t),c=await crypto.subtle.importKey("jwk",u,l,!0,["verify"]);if(!await crypto.subtle.verify(l,c,s,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${a}.${o}`)))throw new rn("Invalid JWT signature");return{data:{claims:n,header:i,signature:s},error:null}}catch(e){if(t2(e))return{data:null,error:e};throw e}}}rQ.nextInstanceID=0;let r0=rQ;class r1 extends r0{constructor(e){super(e)}}class r2{constructor(e,t,r){var i,n,s;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,i;let{db:n,auth:s,realtime:a,global:o}=e,{db:l,auth:u,realtime:c,global:h}=t,d={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},h),o),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(i=null==o?void 0:o.headers)&&void 0!==i?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(n,s){function a(e){try{l(i.next(e))}catch(e){s(e)}}function o(e){try{l(i.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?n(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:tF,realtime:tz,auth:Object.assign(Object.assign({},tW),{storageKey:o}),global:tH});this.storageKey=null!==(i=l.auth.storageKey)&&void 0!==i?i:"",this.headers=null!==(n=l.global.headers)&&void 0!==n?n:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(s=l.auth)&&void 0!==s?s:{},this.headers,l.global.fetch),this.fetch=tK(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new e2(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new e1(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new tD(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,n,s;return r=this,i=void 0,n=void 0,s=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(n||(n=Promise))(function(e,t){function a(e){try{l(s.next(e))}catch(e){t(e)}}function o(e){try{l(s.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(a,o)}l((s=s.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:n,flowType:s,lock:a,debug:o},l,u){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new r1({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:s,lock:a,debug:o,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new tm(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let r3=(e,t,r)=>new r2(e,t,r);async function r4(e){let t=eo.next(),r=r3("https://fgubaqoftdeefcakejwu.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDkyMTAsImV4cCI6MjA2Njg4NTIxMH0.ZdCtKWveoWqxufQ59OXGf2EXoCBjUhWe8spDvYASySI"),i=e.cookies.get("sb-access-token")?.value,n=e.cookies.get("sb-refresh-token")?.value,s=null;if(i)try{let{data:{user:e}}=await r.auth.getUser(i);e&&(s={user:e,access_token:i})}catch(e){if(n)try{let{data:{session:e}}=await r.auth.refreshSession({refresh_token:n});e&&(s=e,t.cookies.set("sb-access-token",e.access_token,{path:"/",maxAge:604800,sameSite:"lax",secure:!0}),e.refresh_token&&t.cookies.set("sb-refresh-token",e.refresh_token,{path:"/",maxAge:2592e3,sameSite:"lax",secure:!0}))}catch(e){t.cookies.delete("sb-access-token"),t.cookies.delete("sb-refresh-token")}}let{pathname:a}=e.nextUrl;if(["/","/login","/api/auth/telegram-callback","/api/health","/_next","/favicon.ico","/logo.png","/images","/static"].some(e=>a===e||a.startsWith(e)))return t;if(!s){let t=new URL("/login",e.url);return t.searchParams.set("redirectTo",a),eo.redirect(t)}let{data:o}=await r.from("telegram_users").select("*").eq("telegram_id",s.user.user_metadata?.telegram_id).single();if((a.startsWith("/dashboard")||a.startsWith("/onboarding"))&&o){let{data:t}=await r.from("terms_acceptance").select("*").eq("user_id",o.id).single();if(a.startsWith("/dashboard")&&!t)return eo.redirect(new URL("/onboarding/terms",e.url));if(a.startsWith("/dashboard")&&!o.country)return eo.redirect(new URL("/onboarding/country",e.url));if("/onboarding/terms"===a&&t)return o.country?eo.redirect(new URL("/dashboard",e.url)):eo.redirect(new URL("/onboarding/country",e.url));if("/onboarding/country"===a&&o.country)return eo.redirect(new URL("/dashboard",e.url))}if(a.startsWith("/admin")){let{data:t}=await r.from("admin_users").select("*").eq("telegram_id",s.user.user_metadata?.telegram_id).eq("is_active",!0).single();if(!t)return eo.redirect(new URL("/dashboard",e.url))}return t}r(340),"undefined"==typeof URLPattern||URLPattern;let r6={matcher:["/((?!api|_next/static|_next/image|favicon.ico|logo.png|images|static).*)"]},r5={...R},r8=r5.middleware||r5.default,r9="/middleware";if("function"!=typeof r8)throw Error(`The Middleware "${r9}" must export a \`middleware\` or a \`default\` function`);function r7(e){return eK({...e,page:r9,handler:r8})}},608:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DiagConsoleLogger:()=>$,DiagLogLevel:()=>i,INVALID_SPANID:()=>eh,INVALID_SPAN_CONTEXT:()=>ef,INVALID_TRACEID:()=>ed,ProxyTracer:()=>eO,ProxyTracerProvider:()=>eI,ROOT_CONTEXT:()=>A,SamplingDecision:()=>a,SpanKind:()=>o,SpanStatusCode:()=>l,TraceFlags:()=>s,ValueType:()=>n,baggageEntryMetadataFromString:()=>C,context:()=>eD,createContextKey:()=>O,createNoopMeter:()=>ee,createTraceState:()=>eq,default:()=>e2,defaultTextMapGetter:()=>et,defaultTextMapSetter:()=>er,diag:()=>eB,isSpanContextValid:()=>eE,isValidSpanId:()=>eT,isValidTraceId:()=>ex,metrics:()=>eW,propagation:()=>eQ,trace:()=>e1});var i,n,s,a,o,l,u="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof r.g?r.g:{},c="1.9.0",h=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,d=function(e){var t=new Set([e]),r=new Set,i=e.match(h);if(!i)return function(){return!1};var n={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=n.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;var i=e.match(h);if(!i)return s(e);var a={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};return null!=a.prerelease||n.major!==a.major?s(e):0===n.major?n.minor===a.minor&&n.patch<=a.patch?(t.add(e),!0):s(e):n.minor<=a.minor?(t.add(e),!0):s(e)}}(c),f=Symbol.for("opentelemetry.js.api."+c.split(".")[0]);function p(e,t,r,i){void 0===i&&(i=!1);var n,s=u[f]=null!==(n=u[f])&&void 0!==n?n:{version:c};if(!i&&s[e]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+e);return r.error(a.stack||a.message),!1}if(s.version!==c){var a=Error("@opentelemetry/api: Registration of version v"+s.version+" for "+e+" does not match previously registered API v"+c);return r.error(a.stack||a.message),!1}return s[e]=t,r.debug("@opentelemetry/api: Registered a global for "+e+" v"+c+"."),!0}function g(e){var t,r,i=null===(t=u[f])||void 0===t?void 0:t.version;if(i&&d(i))return null===(r=u[f])||void 0===r?void 0:r[e]}function m(e,t){t.debug("@opentelemetry/api: Unregistering a global for "+e+" v"+c+".");var r=u[f];r&&delete r[e]}var v=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a},w=function(e,t,r){if(r||2==arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},y=function(){function e(e){this._namespace=e.namespace||"DiagComponentLogger"}return e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("debug",this._namespace,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("error",this._namespace,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("info",this._namespace,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("warn",this._namespace,e)},e.prototype.verbose=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return b("verbose",this._namespace,e)},e}();function b(e,t,r){var i=g("diag");if(i)return r.unshift(t),i[e].apply(i,w([],v(r),!1))}!function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(i||(i={}));var _=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a},k=function(e,t,r){if(r||2==arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},S=function(){function e(){function e(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var i=g("diag");if(i)return i[e].apply(i,k([],_(t),!1))}}var t=this;t.setLogger=function(e,r){if(void 0===r&&(r={logLevel:i.INFO}),e===t){var n,s,a,o=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=o.stack)&&void 0!==n?n:o.message),!1}"number"==typeof r&&(r={logLevel:r});var l=g("diag"),u=function(e,t){function r(r,i){var n=t[r];return"function"==typeof n&&e>=i?n.bind(t):function(){}}return e<i.NONE?e=i.NONE:e>i.ALL&&(e=i.ALL),t=t||{},{error:r("error",i.ERROR),warn:r("warn",i.WARN),info:r("info",i.INFO),debug:r("debug",i.DEBUG),verbose:r("verbose",i.VERBOSE)}}(null!==(s=r.logLevel)&&void 0!==s?s:i.INFO,e);if(l&&!r.suppressOverrideMessage){var c=null!==(a=Error().stack)&&void 0!==a?a:"<failed to generate stacktrace>";l.warn("Current logger will be overwritten from "+c),u.warn("Current logger will overwrite one already registered from "+c)}return p("diag",u,t,!0)},t.disable=function(){m("diag",t)},t.createComponentLogger=function(e){return new y(e)},t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}return e.instance=function(){return this._instance||(this._instance=new e),this._instance},e}(),x=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a},T=function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},E=function(){function e(e){this._entries=e?new Map(e):new Map}return e.prototype.getEntry=function(e){var t=this._entries.get(e);if(t)return Object.assign({},t)},e.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(e){var t=x(e,2);return[t[0],t[1]]})},e.prototype.setEntry=function(t,r){var i=new e(this._entries);return i._entries.set(t,r),i},e.prototype.removeEntry=function(t){var r=new e(this._entries);return r._entries.delete(t),r},e.prototype.removeEntries=function(){for(var t,r,i=[],n=0;n<arguments.length;n++)i[n]=arguments[n];var s=new e(this._entries);try{for(var a=T(i),o=a.next();!o.done;o=a.next()){var l=o.value;s._entries.delete(l)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return s},e.prototype.clear=function(){return new e},e}(),R=Symbol("BaggageEntryMetadata"),P=S.instance();function j(e){return void 0===e&&(e={}),new E(new Map(Object.entries(e)))}function C(e){return"string"!=typeof e&&(P.error("Cannot create baggage metadata from unknown type: "+typeof e),e=""),{__TYPE__:R,toString:function(){return e}}}function O(e){return Symbol.for(e)}var A=new function e(t){var r=this;r._currentContext=t?new Map(t):new Map,r.getValue=function(e){return r._currentContext.get(e)},r.setValue=function(t,i){var n=new e(r._currentContext);return n._currentContext.set(t,i),n},r.deleteValue=function(t){var i=new e(r._currentContext);return i._currentContext.delete(t),i}},I=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}],$=function(){for(var e=0;e<I.length;e++)this[I[e].n]=function(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(console){var i=console[e];if("function"!=typeof i&&(i=console.log),"function"==typeof i)return i.apply(console,t)}}}(I[e].c)},N=function(){var e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(t,r)};return function(t,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}}(),L=function(){function e(){}return e.prototype.createGauge=function(e,t){return J},e.prototype.createHistogram=function(e,t){return K},e.prototype.createCounter=function(e,t){return G},e.prototype.createUpDownCounter=function(e,t){return X},e.prototype.createObservableGauge=function(e,t){return Z},e.prototype.createObservableCounter=function(e,t){return Y},e.prototype.createObservableUpDownCounter=function(e,t){return Q},e.prototype.addBatchObservableCallback=function(e,t){},e.prototype.removeBatchObservableCallback=function(e){},e}(),U=function(){},M=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t.prototype.add=function(e,t){},t}(U),q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t.prototype.add=function(e,t){},t}(U),D=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t.prototype.record=function(e,t){},t}(U),B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t.prototype.record=function(e,t){},t}(U),H=function(){function e(){}return e.prototype.addCallback=function(e){},e.prototype.removeCallback=function(e){},e}(),F=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t}(H),W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t}(H),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return N(t,e),t}(H),V=new L,G=new M,J=new D,K=new B,X=new q,Y=new F,Z=new W,Q=new z;function ee(){return V}!function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(n||(n={}));var et={get:function(e,t){if(null!=e)return e[t]},keys:function(e){return null==e?[]:Object.keys(e)}},er={set:function(e,t,r){null!=e&&(e[t]=r)}},ei=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a},en=function(e,t,r){if(r||2==arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},es=function(){function e(){}return e.prototype.active=function(){return A},e.prototype.with=function(e,t,r){for(var i=[],n=3;n<arguments.length;n++)i[n-3]=arguments[n];return t.call.apply(t,en([r],ei(i),!1))},e.prototype.bind=function(e,t){return t},e.prototype.enable=function(){return this},e.prototype.disable=function(){return this},e}(),ea=function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var i,n,s=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(i=s.next()).done;)a.push(i.value)}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return a},eo=function(e,t,r){if(r||2==arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))},el="context",eu=new es,ec=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalContextManager=function(e){return p(el,e,S.instance())},e.prototype.active=function(){return this._getContextManager().active()},e.prototype.with=function(e,t,r){for(var i,n=[],s=3;s<arguments.length;s++)n[s-3]=arguments[s];return(i=this._getContextManager()).with.apply(i,eo([e,t,r],ea(n),!1))},e.prototype.bind=function(e,t){return this._getContextManager().bind(e,t)},e.prototype._getContextManager=function(){return g(el)||eu},e.prototype.disable=function(){this._getContextManager().disable(),m(el,S.instance())},e}();!function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(s||(s={}));var eh="0000000000000000",ed="00000000000000000000000000000000",ef={traceId:ed,spanId:eh,traceFlags:s.NONE},ep=function(){function e(e){void 0===e&&(e=ef),this._spanContext=e}return e.prototype.spanContext=function(){return this._spanContext},e.prototype.setAttribute=function(e,t){return this},e.prototype.setAttributes=function(e){return this},e.prototype.addEvent=function(e,t){return this},e.prototype.addLink=function(e){return this},e.prototype.addLinks=function(e){return this},e.prototype.setStatus=function(e){return this},e.prototype.updateName=function(e){return this},e.prototype.end=function(e){},e.prototype.isRecording=function(){return!1},e.prototype.recordException=function(e,t){},e}(),eg=O("OpenTelemetry Context Key SPAN");function em(e){return e.getValue(eg)||void 0}function ev(){return em(ec.getInstance().active())}function ew(e,t){return e.setValue(eg,t)}function ey(e){return e.deleteValue(eg)}function eb(e,t){return ew(e,new ep(t))}function e_(e){var t;return null===(t=em(e))||void 0===t?void 0:t.spanContext()}var ek=/^([0-9a-f]{32})$/i,eS=/^[0-9a-f]{16}$/i;function ex(e){return ek.test(e)&&e!==ed}function eT(e){return eS.test(e)&&e!==eh}function eE(e){return ex(e.traceId)&&eT(e.spanId)}function eR(e){return new ep(e)}var eP=ec.getInstance(),ej=function(){function e(){}return e.prototype.startSpan=function(e,t,r){if(void 0===r&&(r=eP.active()),null==t?void 0:t.root)return new ep;var i=r&&e_(r);return"object"==typeof i&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&eE(i)?new ep(i):new ep},e.prototype.startActiveSpan=function(e,t,r,i){if(!(arguments.length<2)){2==arguments.length?a=t:3==arguments.length?(n=t,a=r):(n=t,s=r,a=i);var n,s,a,o=null!=s?s:eP.active(),l=this.startSpan(e,n,o),u=ew(o,l);return eP.with(u,a,void 0,l)}},e}(),eC=new ej,eO=function(){function e(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}return e.prototype.startSpan=function(e,t,r){return this._getTracer().startSpan(e,t,r)},e.prototype.startActiveSpan=function(e,t,r,i){var n=this._getTracer();return Reflect.apply(n.startActiveSpan,n,arguments)},e.prototype._getTracer=function(){if(this._delegate)return this._delegate;var e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):eC},e}(),eA=new(function(){function e(){}return e.prototype.getTracer=function(e,t,r){return new ej},e}()),eI=function(){function e(){}return e.prototype.getTracer=function(e,t,r){var i;return null!==(i=this.getDelegateTracer(e,t,r))&&void 0!==i?i:new eO(this,e,t,r)},e.prototype.getDelegate=function(){var e;return null!==(e=this._delegate)&&void 0!==e?e:eA},e.prototype.setDelegate=function(e){this._delegate=e},e.prototype.getDelegateTracer=function(e,t,r){var i;return null===(i=this._delegate)||void 0===i?void 0:i.getTracer(e,t,r)},e}();!function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(a||(a={})),function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(o||(o={})),function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(l||(l={}));var e$="[_0-9a-z-*/]",eN=RegExp("^(?:[a-z]"+e$+"{0,255}|"+("[a-z0-9]"+e$)+"{0,240}@[a-z]"+e$+"{0,13})$"),eL=/^[ -~]{0,255}[!-~]$/,eU=/,|=/,eM=function(){function e(e){this._internalState=new Map,e&&this._parse(e)}return e.prototype.set=function(e,t){var r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r},e.prototype.unset=function(e){var t=this._clone();return t._internalState.delete(e),t},e.prototype.get=function(e){return this._internalState.get(e)},e.prototype.serialize=function(){var e=this;return this._keys().reduce(function(t,r){return t.push(r+"="+e.get(r)),t},[]).join(",")},e.prototype._parse=function(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce(function(e,t){var r=t.trim(),i=r.indexOf("=");if(-1!==i){var n=r.slice(0,i),s=r.slice(i+1,t.length);eN.test(n)&&eL.test(s)&&!eU.test(s)&&e.set(n,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},e.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},e.prototype._clone=function(){var t=new e;return t._internalState=new Map(this._internalState),t},e}();function eq(e){return new eM(e)}var eD=ec.getInstance(),eB=S.instance(),eH=new(function(){function e(){}return e.prototype.getMeter=function(e,t,r){return V},e}()),eF="metrics",eW=(function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalMeterProvider=function(e){return p(eF,e,S.instance())},e.prototype.getMeterProvider=function(){return g(eF)||eH},e.prototype.getMeter=function(e,t,r){return this.getMeterProvider().getMeter(e,t,r)},e.prototype.disable=function(){m(eF,S.instance())},e})().getInstance(),ez=function(){function e(){}return e.prototype.inject=function(e,t){},e.prototype.extract=function(e,t){return e},e.prototype.fields=function(){return[]},e}(),eV=O("OpenTelemetry Baggage Key");function eG(e){return e.getValue(eV)||void 0}function eJ(){return eG(ec.getInstance().active())}function eK(e,t){return e.setValue(eV,t)}function eX(e){return e.deleteValue(eV)}var eY="propagation",eZ=new ez,eQ=(function(){function e(){this.createBaggage=j,this.getBaggage=eG,this.getActiveBaggage=eJ,this.setBaggage=eK,this.deleteBaggage=eX}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalPropagator=function(e){return p(eY,e,S.instance())},e.prototype.inject=function(e,t,r){return void 0===r&&(r=er),this._getGlobalPropagator().inject(e,t,r)},e.prototype.extract=function(e,t,r){return void 0===r&&(r=et),this._getGlobalPropagator().extract(e,t,r)},e.prototype.fields=function(){return this._getGlobalPropagator().fields()},e.prototype.disable=function(){m(eY,S.instance())},e.prototype._getGlobalPropagator=function(){return g(eY)||eZ},e})().getInstance(),e0="trace",e1=(function(){function e(){this._proxyTracerProvider=new eI,this.wrapSpanContext=eR,this.isSpanContextValid=eE,this.deleteSpan=ey,this.getSpan=em,this.getActiveSpan=ev,this.getSpanContext=e_,this.setSpan=ew,this.setSpanContext=eb}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.prototype.setGlobalTracerProvider=function(e){var t=p(e0,this._proxyTracerProvider,S.instance());return t&&this._proxyTracerProvider.setDelegate(e),t},e.prototype.getTracerProvider=function(){return g(e0)||this._proxyTracerProvider},e.prototype.getTracer=function(e,t){return this.getTracerProvider().getTracer(e,t)},e.prototype.disable=function(){m(e0,S.instance()),this._proxyTracerProvider=new eI},e})().getInstance();let e2={context:eD,diag:eB,metrics:eW,propagation:eQ,trace:e1}},254:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>s,fetch:()=>n});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let n=i.fetch,s=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},22:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(254)),s=i(r(335));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=n.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let n=null,a=null,o=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),s=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");i&&s&&s.length>1&&(o=parseInt(s[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(n={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,u="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(a=[],n=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(i=null==n?void 0:n.details)||void 0===i?void 0:i.includes("0 rows"))&&(n=null,l=200,u="OK"),n&&this.shouldThrowOnError)throw new s.default(n)}return{error:n,data:a,count:o,status:l,statusText:u}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(i=null==e?void 0:e.code)&&void 0!==i?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},191:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(210)),s=i(r(401)),a=r(253);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new n.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:n}={}){let a,o;let l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let u=Object.assign({},this.headers);return n&&(u.Prefer=`count=${n}`),new s.default({method:a,url:l,headers:u,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},335:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},401:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(251));class s extends n.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let n="";"plain"===i?n="pl":"phrase"===i?n="ph":"websearch"===i&&(n="w");let s=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${n}fts${s}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=s},210:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(401));class s{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",s),r&&(this.headers.Prefer=`count=${r}`),new n.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new n.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new n.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new n.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=s},251:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let n=i(r(22));class s extends n.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:n=i}={}){let s=n?`${n}.order`:"order",a=this.url.searchParams.get(s);return this.url.searchParams.set(s,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let n=void 0===i?"offset":`${i}.offset`,s=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(s,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:n=!1,format:s="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${s}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=s},253:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(348);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},690:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let n=i(r(191));t.PostgrestClient=n.default;let s=i(r(210));t.PostgrestQueryBuilder=s.default;let a=i(r(401));t.PostgrestFilterBuilder=a.default;let o=i(r(251));t.PostgrestTransformBuilder=o.default;let l=i(r(22));t.PostgrestBuilder=l.default;let u=i(r(335));t.PostgrestError=u.default,t.default={PostgrestClient:n.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:u.default}},348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},945:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,n],...s]=o(e),{domain:a,expires:l,httponly:h,maxage:d,path:f,samesite:p,secure:g,partitioned:m,priority:v}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(n),domain:a,...l&&{expires:new Date(l)},...h&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:f,...p&&{sameSite:u.includes(t=(t=p).toLowerCase())?t:void 0},...g&&{secure:!0},...v&&{priority:c.includes(r=(r=v).toLowerCase())?r:void 0},...m&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(s,{RequestCookies:()=>h,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of i(s))n.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var u=["strict","lax","none"],c=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=n,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},133:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},s=t.split(i),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),h=l.substr(++u,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return n},t.serialize=function(e,t,i){var s=i||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},340:(e,t,r)=>{var i;(()=>{var n={226:function(n,s){!function(a,o){"use strict";var l="function",u="undefined",c="object",h="string",d="major",f="model",p="name",g="type",m="vendor",v="version",w="architecture",y="console",b="mobile",_="tablet",k="smarttv",S="wearable",x="embedded",T="Amazon",E="Apple",R="ASUS",P="BlackBerry",j="Browser",C="Chrome",O="Firefox",A="Google",I="Huawei",$="Microsoft",N="Motorola",L="Opera",U="Samsung",M="Sharp",q="Sony",D="Xiaomi",B="Zebra",H="Facebook",F="Chromium OS",W="Mac OS",z=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},V=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},G=function(e,t){return typeof e===h&&-1!==J(t).indexOf(J(e))},J=function(e){return e.toLowerCase()},K=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},X=function(e,t){for(var r,i,n,s,a,u,h=0;h<t.length&&!a;){var d=t[h],f=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(n=0;n<f.length;n++)u=a[++i],typeof(s=f[n])===c&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,u):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=u?u.replace(s[1],s[2]):void 0:this[s[0]]=u?s[1].call(this,u,s[2]):void 0:4===s.length&&(this[s[0]]=u?s[3].call(this,u.replace(s[1],s[2])):void 0):this[s]=u||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(G(t[r][i],e))return"?"===r?o:r}else if(G(t[r],e))return"?"===r?o:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[v,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[v,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,v],[/opios[\/ ]+([\w\.]+)/i],[v,[p,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[v,[p,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,v],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[v,[p,"UC"+j]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[v,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[v,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[v,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[v,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[v,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+j],v],[/\bfocus\/([\w\.]+)/i],[v,[p,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[v,[p,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[v,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[v,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[v,[p,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[v,[p,"MIUI "+j]],[/fxios\/([-\w\.]+)/i],[v,[p,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+j]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+j],v],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],v],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,v],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,H],v],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,v],[/\bgsa\/([\w\.]+) .*safari\//i],[v,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[v,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[v,[p,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,C+" WebView"],v],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[v,[p,"Android "+j]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,v],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[v,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[v,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[v,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,v],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],v],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[v,[p,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,v],[/(cobalt)\/([\w\.]+)/i],[p,[v,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[w,"amd64"]],[/(ia32(?=;))/i],[[w,J]],[/((?:i[346]|x)86)[;\)]/i],[[w,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[w,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[w,"armhf"]],[/windows (ce|mobile); ppc;/i],[[w,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[w,/ower/,"",J]],[/(sun4\w)[;\)]/i],[[w,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[w,J]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[m,U],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[m,U],[g,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[m,E],[g,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[m,E],[g,_]],[/(macintosh);/i],[f,[m,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[m,M],[g,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[m,I],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[m,I],[g,b]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[m,D],[g,b]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[m,D],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[m,"OPPO"],[g,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[m,"Vivo"],[g,b]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[m,"Realme"],[g,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[m,N],[g,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[m,N],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[m,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[m,"LG"],[g,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[m,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[m,"Nokia"],[g,b]],[/(pixel c)\b/i],[f,[m,A],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[m,A],[g,b]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[m,q],[g,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[m,q],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[m,"OnePlus"],[g,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[m,T],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[m,T],[g,b]],[/(playbook);[-\w\),; ]+(rim)/i],[f,m,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[m,P],[g,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[m,R],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[m,R],[g,b]],[/(nexus 9)/i],[f,[m,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[f,/_/g," "],[g,b]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[m,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[m,"Meizu"],[g,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,f,[g,b]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,f,[g,_]],[/(surface duo)/i],[f,[m,$],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[m,"Fairphone"],[g,b]],[/(u304aa)/i],[f,[m,"AT&T"],[g,b]],[/\bsie-(\w*)/i],[f,[m,"Siemens"],[g,b]],[/\b(rct\w+) b/i],[f,[m,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[f,[m,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[f,[m,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[m,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[f,[m,"NuVision"],[g,_]],[/\b(k88) b/i],[f,[m,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[f,[m,"ZTE"],[g,b]],[/\b(gen\d{3}) b.+49h/i],[f,[m,"Swiss"],[g,b]],[/\b(zur\d{3}) b/i],[f,[m,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[f,[m,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],f,[g,_]],[/\b(ns-?\w{0,9}) b/i],[f,[m,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[m,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],f,[g,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],f,[g,b]],[/\b(ph-1) /i],[f,[m,"Essential"],[g,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[m,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[f,[m,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[f,[m,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[f,[m,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[m,f,[g,b]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[m,$],[g,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[m,B],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[m,B],[g,b]],[/smart-tv.+(samsung)/i],[m,[g,k]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[m,U],[g,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,k]],[/(apple) ?tv/i],[m,[f,E+" TV"],[g,k]],[/crkey/i],[[f,C+"cast"],[m,A],[g,k]],[/droid.+aft(\w)( bui|\))/i],[f,[m,T],[g,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[m,M],[g,k]],[/(bravia[\w ]+)( bui|\))/i],[f,[m,q],[g,k]],[/(mitv-\w{5}) bui/i],[f,[m,D],[g,k]],[/Hbbtv.*(technisat) (.*);/i],[m,f,[g,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,K],[f,K],[g,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,f,[g,y]],[/droid.+; (shield) bui/i],[f,[m,"Nvidia"],[g,y]],[/(playstation [345portablevi]+)/i],[f,[m,q],[g,y]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[m,$],[g,y]],[/((pebble))app/i],[m,f,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[m,E],[g,S]],[/droid.+; (glass) \d/i],[f,[m,A],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[f,[m,B],[g,S]],[/(quest( 2| pro)?)/i],[f,[m,H],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,x]],[/(aeobc)\b/i],[f,[m,T],[g,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,b]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[v,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[v,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,v],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[v,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,v],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[v,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[v,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[v,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,W],[v,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[v,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,v],[/\(bb(10);/i],[v,[p,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[v,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[v,[p,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[v,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[v,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[v,[p,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,F],v],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,v],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],v],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,v]]},ee=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),n=r&&r.userAgentData?r.userAgentData:o,s=t?z(Q,t):Q,y=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[p]=o,t[v]=o,X.call(t,i,s.browser),t[d]=typeof(e=t[v])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,y&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[w]=o,X.call(e,i,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[f]=o,e[g]=o,X.call(e,i,s.device),y&&!e[g]&&n&&n.mobile&&(e[g]=b),y&&"Macintosh"==e[f]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[p]=o,e[v]=o,X.call(e,i,s.engine),e},this.getOS=function(){var e={};return e[p]=o,e[v]=o,X.call(e,i,s.os),y&&!e[p]&&n&&"Unknown"!=n.platform&&(e[p]=n.platform.replace(/chrome os/i,F).replace(/macos/i,W)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?K(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=V([p,v,d]),ee.CPU=V([w]),ee.DEVICE=V([f,m,g,y,b,k,_,S,x]),ee.ENGINE=ee.OS=V([p,v]),typeof s!==u?(n.exports&&(s=n.exports=ee),s.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},i=!0;try{n[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete s[e]}return r.exports}a.ab="//";var o=a(226);e.exports=o})()},488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let i=new(r(67)).AsyncLocalStorage;function n(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=n(e,t);return s?i.run(s,r):r()}function a(e,t){return i.getStore()||(e&&t?n(e,t):void 0)}},375:(e,t,r)=>{"use strict";var i=r(195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let n=r(488),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:n,headers:s,body:a,cache:o,credentials:l,integrity:u,mode:c,redirect:h,referrer:d,referrerPolicy:f}=t;return{testData:e,api:"fetch",request:{url:r,method:n,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:u,mode:c,redirect:h,referrer:d,referrerPolicy:f}}}async function o(e,t){let r=(0,n.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,u=await a(o,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(u),next:{internal:!0}});if(!c.ok)throw Error(`Proxy request failed: ${c.status}`);let h=await c.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:n}=e.response;return new Response(n?i.from(n,"base64"):null,{status:t,headers:new Headers(r)})}(h)}function l(e){return r.g.fetch=function(t,r){var i;return(null==r?void 0:null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},177:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let i=r(488),n=r(375);function s(){return(0,n.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,n.reader,()=>e(t,r))}}},e=>{var t=e(e.s=402);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map