# Aureus Africa Web Dashboard - Development Setup Guide

## 🚀 Quick Start

### Prerequisites
- **Node.js**: Version 18.0 or higher
- **npm**: Version 9.0 or higher (or yarn/pnpm)
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Required VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

## 📦 Installation

### 1. Clone Repository
```bash
git clone <repository-url>
cd aureus_africa
```

### 2. Install Dependencies
```bash
npm install
# or
yarn install
# or
pnpm install
```

### 3. Environment Setup
Create `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://fgubaqoftdeefcakejwu.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

### 4. Database Setup
The application uses Supabase as the backend. The database schema is already configured and includes:

- User authentication tables
- Share purchase and payment tracking
- Referral and commission systems
- KYC and compliance data

**Note**: No additional database setup required - the schema is managed by the Telegram bot system.

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

The application will be available at `http://localhost:3000`

### Production Build
```bash
npm run build
npm start
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
npm run lint:fix
```

## 🏗️ Project Structure Overview

```
aureus_africa/
├── app/                     # Next.js 14 App Router
│   ├── (auth)/             # Authentication routes
│   ├── dashboard/          # Protected dashboard
│   ├── onboarding/         # User onboarding
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Home page
├── components/             # React components
│   ├── ui/                 # Reusable UI components
│   ├── auth/               # Authentication components
│   └── onboarding/         # Onboarding components
├── contexts/               # React contexts
├── lib/                    # Utility libraries
├── hooks/                  # Custom React hooks
├── public/                 # Static assets
└── types/                  # TypeScript type definitions
```

## 🔧 Development Tools

### Available Scripts
```json
{
  "dev": "next dev",
  "build": "next build",
  "start": "next start",
  "lint": "next lint",
  "type-check": "tsc --noEmit",
  "analyze": "ANALYZE=true npm run build"
}
```

### Code Quality Tools
- **ESLint**: Code linting with Next.js rules
- **Prettier**: Code formatting
- **TypeScript**: Type checking
- **Husky**: Git hooks for quality gates

## 🎨 Styling System

### Tailwind CSS Configuration
The project uses a custom Tailwind CSS configuration with:
- Custom color palette matching brand guidelines
- Responsive breakpoints
- Custom component classes
- Dark mode support

### Design System Components
Located in `components/ui/`:
- **Button**: Multiple variants and sizes
- **Card**: Flexible card layouts
- **Input**: Form input components
- **Modal**: Dialog and modal system
- **Toast**: Notification system
- **Progress**: Loading and progress indicators

## 🔐 Authentication Flow

### Development Authentication
1. The app uses Telegram OAuth for authentication
2. For development, you can use the test user system
3. Authentication state is managed via React Context
4. Protected routes automatically redirect to login

### Testing Authentication
```typescript
// Use test credentials for development
const testUser = {
  telegram_id: "123456789",
  first_name: "Test",
  last_name: "User",
  username: "testuser"
}
```

## 📊 Database Integration

### Supabase Client
The application uses two Supabase clients:
- **Public Client**: For client-side operations
- **Service Client**: For server-side operations

### Key Tables
- `telegram_users`: User profiles
- `share_purchases`: Investment records
- `crypto_payment_transactions`: Payment history
- `referrals`: Referral system
- `commission_balances`: Commission tracking

### Real-time Features
The app includes real-time synchronization:
```typescript
// Example: Subscribe to user updates
const { data, loading, error } = useRealTimeSync('telegram_users', null, {
  column: 'telegram_id',
  value: user.telegram_id
})
```

## 🧪 Testing Strategy

### Testing Framework (To be implemented)
- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: Cypress
- **E2E Tests**: Playwright
- **API Tests**: Supertest

### Test Commands (Future)
```bash
npm run test          # Unit tests
npm run test:e2e      # End-to-end tests
npm run test:coverage # Coverage report
```

## 🚀 Deployment

### Vercel Deployment (Recommended)
1. Connect repository to Vercel
2. Configure environment variables
3. Deploy automatically on push to main

### Environment Variables for Production
```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_key
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🔍 Debugging

### Development Tools
- **React Developer Tools**: Browser extension
- **Supabase Dashboard**: Database inspection
- **Network Tab**: API request monitoring
- **Console Logging**: Comprehensive error logging

### Common Issues
1. **Supabase Connection**: Check environment variables
2. **Authentication**: Verify Telegram OAuth setup
3. **Styling**: Ensure Tailwind CSS is properly configured
4. **Type Errors**: Run `npm run type-check`

## 📚 Key Dependencies

### Core Dependencies
```json
{
  "next": "^14.0.0",
  "react": "^19.1.0",
  "typescript": "^5.0.0",
  "@supabase/supabase-js": "^2.50.3",
  "tailwindcss": "^3.4.0"
}
```

### UI Dependencies
```json
{
  "clsx": "^2.0.0",
  "tailwind-merge": "^2.2.0",
  "@heroicons/react": "^2.0.18"
}
```

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `main`
2. Make changes following coding standards
3. Test thoroughly
4. Submit pull request
5. Code review and merge

### Coding Standards
- Use TypeScript for all new code
- Follow ESLint configuration
- Use Prettier for formatting
- Write meaningful commit messages
- Add JSDoc comments for complex functions

## 📞 Support

### Getting Help
- **Documentation**: Check this guide and code comments
- **Issues**: Create GitHub issues for bugs
- **Discussions**: Use GitHub discussions for questions
- **Team Chat**: Internal team communication channels

### Useful Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

This setup guide provides everything needed to start developing on the Aureus Africa Web Dashboard project.
