# Share Purchase System - Complete Implementation

## 💰 System Overview

The Aureus Africa Share Purchase System provides a comprehensive platform for users to purchase gold mining shares through multiple payment methods. The system handles share calculations, payment processing, commission tracking, and portfolio management with full integration to the existing Telegram bot infrastructure.

## 🏗️ Architecture Components

### Core Components
1. **Share Calculation Engine** - Precise share and pricing calculations
2. **Payment Processing** - Multi-currency payment handling
3. **Investment Phases** - Dynamic pricing and availability management
4. **Package System** - Predefined investment packages with ROI tiers
5. **Commission System** - Referral commission calculations and tracking
6. **Portfolio Management** - User share tracking and history

### Purchase Flow
```
Amount Selection → Package Selection → Payment Method → 
Payment Processing → Share Allocation → Portfolio Update
```

## 🔧 Implementation Details

### 1. Share Calculation Engine (`lib/share-calculations.ts`)
**Features:**
- Precise share calculations with validation
- Package information and ROI projections
- Phase availability validation
- Purchase limit enforcement
- Commission calculations for referrals
- Currency conversion utilities

**Key Functions:**
- `calculateShares()` - Core share calculation logic
- `validatePhaseAvailability()` - Phase capacity validation
- `validatePurchaseLimits()` - User purchase limit checks
- `getPackageInfo()` - Package tier determination
- `calculateCommission()` - Referral commission calculation

### 2. Purchase API (`app/api/shares/purchase/route.ts`)
**Features:**
- Secure purchase transaction creation
- Real-time validation and error handling
- Payment method integration
- Commission tracking for referrals
- Database transaction management
- Comprehensive error logging

**Endpoints:**
- `POST /api/shares/purchase` - Create new share purchase
- `GET /api/shares/purchase` - Get user portfolio and phase info

### 3. Purchase Summary Component (`components/purchase/SharePurchaseSummary.tsx`)
**Features:**
- Real-time purchase calculation display
- ROI projections and package information
- Payment method details and processing times
- Warning and error message display
- Interactive confirmation interface
- Mobile-responsive design

### 4. Testing Framework (`components/purchase/SharePurchaseTest.tsx`)
**Features:**
- Comprehensive system validation
- Edge case testing
- API endpoint verification
- Calculation accuracy testing
- Payment method validation
- Performance benchmarking

## 📊 Investment Package System

### Package Tiers
```typescript
const packages = [
  { name: 'Shovel', amount: $25-74, shares: 25, roi: 12% },
  { name: 'Miner', amount: $75-249, shares: 75, roi: 15% },
  { name: 'Excavator', amount: $250-499, shares: 250, roi: 18% },
  { name: 'Crusher', amount: $500-749, shares: 500, roi: 20% },
  { name: 'Refinery', amount: $750-999, shares: 750, roi: 22% },
  { name: 'Aureus', amount: $1000-2499, shares: 1000, roi: 25% },
  { name: 'Titan', amount: $2500-4999, shares: 2500, roi: 28% },
  { name: 'Empire', amount: $5000-10000, shares: 5000, roi: 30% }
]
```

### Package Benefits
- **Progressive ROI** - Higher investment amounts receive better returns
- **Clear Tiers** - Defined investment levels with specific benefits
- **Flexible Amounts** - Custom packages for amounts outside predefined ranges
- **ROI Projections** - 5-year return calculations for each package

## 💳 Payment Processing

### Supported Payment Methods
1. **USDT Cryptocurrency**
   - Networks: Ethereum, BSC, Polygon, TRON
   - Processing: 10 minutes - 2 hours
   - Fees: $0 (network fees paid by user)
   - Global availability

2. **ZAR Bank Transfer** (South Africa only)
   - Processing: 1-3 business days
   - Fees: $0
   - Local bank account required

### Payment Flow
```
Payment Method Selection → Amount Validation → 
Payment Instructions → Proof Upload → Admin Verification → 
Share Allocation → Portfolio Update
```

## 🔄 Investment Phase Management

### Phase Structure
```sql
investment_phases (
  id, phase_number, phase_name, price_per_share,
  shares_available, shares_sold, is_active,
  start_date, end_date, created_at, updated_at
)
```

### Phase Features
- **Dynamic Pricing** - Price per share increases with each phase
- **Limited Availability** - Fixed number of shares per phase
- **Automatic Progression** - Phases advance when sold out
- **Real-time Tracking** - Live availability updates

### Current Phase System
- **Phase 1**: $1.00 per share (1,000,000 shares)
- **Phase 2**: $1.25 per share (500,000 shares)
- **Phase 3**: $1.50 per share (300,000 shares)
- **Future Phases**: Progressive price increases

## 💰 Commission System

### Referral Commission Structure
- **Commission Rate**: 15% of purchase amount
- **USDT Commission**: 15% of USD amount
- **Share Commission**: 15% of shares purchased
- **Payment Trigger**: When referred user's payment is approved
- **Withdrawal**: Available immediately after approval

### Commission Calculation
```typescript
const commission = {
  usdtAmount: purchaseAmount * 0.15,
  shareAmount: sharesPurchased * 0.15,
  rate: 15.0
}
```

## 🔒 Security Features

### Transaction Security
- **Input Validation** - Comprehensive form and amount validation
- **SQL Injection Prevention** - Parameterized queries
- **XSS Protection** - Content sanitization
- **CSRF Protection** - Built-in Next.js protection
- **Rate Limiting** - Purchase frequency limits

### Financial Security
- **Amount Limits** - Minimum $25, Maximum $10,000 per transaction
- **Phase Validation** - Ensure sufficient shares available
- **Double-spend Prevention** - Transaction uniqueness validation
- **Audit Trail** - Complete transaction logging

## 📈 Portfolio Management

### User Portfolio Features
- **Total Shares** - Cumulative share ownership
- **Total Invested** - Sum of all investments
- **Purchase History** - Detailed transaction records
- **ROI Tracking** - Performance monitoring
- **Commission Earnings** - Referral income tracking

### Portfolio Display
```typescript
interface UserPortfolio {
  totalShares: number
  totalInvested: number
  averagePricePerShare: number
  currentValue: number
  unrealizedGains: number
  commissionEarned: number
  purchases: SharePurchase[]
}
```

## 🧮 Calculation Engine

### Share Calculation Logic
```typescript
function calculateShares(amount: number, sharePrice: number) {
  const exactShares = amount / sharePrice
  const sharesAmount = Math.floor(exactShares) // Whole shares only
  const totalCost = sharesAmount * sharePrice
  const remainingAmount = amount - totalCost // Refund amount
  
  return {
    sharesAmount,
    totalCost,
    remainingAmount,
    isValid: sharesAmount > 0
  }
}
```

### Validation Rules
- **Minimum Investment**: $25 USD
- **Maximum Investment**: $10,000 USD per transaction
- **Whole Shares Only**: Fractional shares not supported
- **Phase Availability**: Must have sufficient shares in current phase
- **User Limits**: Total investment tracking and limits

## 🔄 Database Integration

### Core Tables
```sql
-- Share purchases
share_purchases (
  id, user_id, package_name, shares_purchased,
  total_amount, payment_method, status,
  created_at, updated_at
)

-- Payment transactions
crypto_payment_transactions (
  id, user_id, amount, currency, network,
  sender_wallet, receiver_wallet, transaction_hash,
  screenshot_url, status, admin_notes
)

-- Investment phases
investment_phases (
  id, phase_number, phase_name, price_per_share,
  shares_available, shares_sold, is_active
)

-- Commission tracking
commission_transactions (
  id, referrer_id, referred_id, share_purchase_amount,
  usdt_commission, share_commission, status
)
```

### Data Relationships
```
telegram_users (1) → (M) share_purchases
share_purchases (1) → (1) crypto_payment_transactions
investment_phases (1) → (M) share_purchases
referrals (1) → (M) commission_transactions
```

## 🧪 Testing Framework

### SharePurchaseTest Component
**Test Coverage:**
- User authentication verification
- Current phase information retrieval
- Share calculation accuracy (multiple amounts)
- Package information validation
- Phase availability checking
- Purchase limits validation
- Commission calculation verification
- Edge case handling
- Payment method availability
- API endpoint connectivity

**Test Categories:**
- **Calculation Tests** - Mathematical accuracy
- **Validation Tests** - Business rule enforcement
- **API Tests** - Endpoint functionality
- **Edge Case Tests** - Boundary condition handling
- **Integration Tests** - End-to-end flow validation

## 📱 User Experience

### Purchase Flow UX
1. **Amount Selection** - Clear input with real-time validation
2. **Package Display** - Visual package tiers with benefits
3. **Payment Method** - Available options based on country
4. **Summary Review** - Comprehensive purchase details
5. **Payment Instructions** - Clear step-by-step guidance
6. **Confirmation** - Success feedback and next steps

### Mobile Optimization
- **Responsive Design** - Works on all device sizes
- **Touch-friendly** - Large buttons and easy navigation
- **Fast Loading** - Optimized for mobile networks
- **Offline Support** - Cached data for poor connections

## 🚀 Performance Features

### Optimization Strategies
- **Real-time Calculations** - Instant feedback on amount changes
- **Caching** - Phase and package information caching
- **Lazy Loading** - Component-based loading
- **Bundle Optimization** - Minimal JavaScript payload
- **Database Indexing** - Optimized query performance

### Scalability Features
- **Connection Pooling** - Efficient database connections
- **Rate Limiting** - Prevent system overload
- **Error Recovery** - Graceful failure handling
- **Monitoring** - Performance and error tracking

## ✅ System Status

### Completed Features
- ✅ Complete share calculation engine
- ✅ Multi-currency payment processing
- ✅ Investment phase management
- ✅ Package tier system with ROI calculations
- ✅ Commission tracking and calculations
- ✅ Portfolio management and history
- ✅ Comprehensive validation and error handling
- ✅ Mobile-responsive user interface
- ✅ Security measures and audit trails
- ✅ Testing framework and validation
- ✅ API endpoints for all operations
- ✅ Database integration and relationships

### Integration Points
- ✅ **Authentication System** - Seamless user authentication
- ✅ **Telegram Bot** - Data synchronization
- ✅ **Payment Processing** - Multi-method support
- ✅ **Commission System** - Referral tracking
- ✅ **Admin Dashboard** - Payment approval workflow

## 📚 Usage Examples

### Calculate Shares
```typescript
import { calculateShares } from '@/lib/share-calculations'

const result = calculateShares(1000, 1.25)
// Returns: { sharesAmount: 800, totalCost: 1000, remainingAmount: 0 }
```

### Create Purchase
```typescript
const response = await fetch('/api/shares/purchase', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    amount: 1000,
    paymentMethod: 'USDT',
    network: 'BSC'
  })
})
```

### Get Portfolio
```typescript
const response = await fetch('/api/shares/purchase')
const data = await response.json()
// Returns user portfolio and current phase info
```

The share purchase system is **COMPLETE** and ready for production use with comprehensive calculation accuracy, security measures, and user experience optimization.
