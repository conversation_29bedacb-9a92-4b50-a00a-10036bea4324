import { createClient } from '@supabase/supabase-js';
import { createHmac } from 'crypto';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN;

if (!supabaseServiceKey || !telegramBotToken) {
  throw new Error('Missing required environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

// Verify Telegram authentication data
function verifyTelegramAuth(data: TelegramUser): boolean {
  const { hash, ...authData } = data;
  
  // Create data-check-string
  const dataCheckString = Object.keys(authData)
    .sort()
    .map(key => `${key}=${authData[key as keyof typeof authData]}`)
    .join('\n');
  
  // Create secret key
  const secretKey = createHmac('sha256', 'WebAppData').update(telegramBotToken).digest();
  
  // Calculate hash
  const calculatedHash = createHmac('sha256', secretKey).update(dataCheckString).digest('hex');
  
  return calculatedHash === hash;
}

export default async function handler(req: any, res: any) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const telegramData: TelegramUser = req.body;

    // Verify Telegram authentication
    if (!verifyTelegramAuth(telegramData)) {
      return res.status(401).json({ 
        success: false, 
        error: 'Invalid Telegram authentication' 
      });
    }

    // Check if auth data is not too old (5 minutes)
    const authAge = Date.now() / 1000 - telegramData.auth_date;
    if (authAge > 300) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication data is too old' 
      });
    }

    // Find existing user in telegram_users table
    const { data: existingTelegramUser, error: findError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramData.id)
      .single();

    if (findError && findError.code !== 'PGRST116') {
      console.error('Error finding telegram user:', findError);
      return res.status(500).json({ 
        success: false, 
        error: 'Database error' 
      });
    }

    let telegramUser = existingTelegramUser;

    // If user doesn't exist, create new telegram user record
    if (!existingTelegramUser) {
      const { data: newTelegramUser, error: createError } = await supabase
        .from('telegram_users')
        .insert({
          telegram_id: telegramData.id,
          username: telegramData.username,
          first_name: telegramData.first_name,
          last_name: telegramData.last_name,
          is_registered: false,
          registration_step: 'start',
          registration_mode: 'login'
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating telegram user:', createError);
        return res.status(500).json({ 
          success: false, 
          error: 'Failed to create user record' 
        });
      }

      telegramUser = newTelegramUser;
    } else {
      // Update existing user info
      const { error: updateError } = await supabase
        .from('telegram_users')
        .update({
          username: telegramData.username,
          first_name: telegramData.first_name,
          last_name: telegramData.last_name,
          updated_at: new Date().toISOString()
        })
        .eq('telegram_id', telegramData.id);

      if (updateError) {
        console.error('Error updating telegram user:', updateError);
      }
    }

    // Create or get Supabase auth user
    const email = `telegram_${telegramData.id}@aureus.africa`;
    const password = `telegram_${telegramData.id}_auth`;

    // Try to sign in first
    let authUser;
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (signInError) {
      // If sign in fails, create new auth user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            telegram_id: telegramData.id,
            telegram_username: telegramData.username,
            telegram_first_name: telegramData.first_name,
            telegram_last_name: telegramData.last_name
          }
        }
      });

      if (signUpError) {
        console.error('Error creating auth user:', signUpError);
        return res.status(500).json({ 
          success: false, 
          error: 'Authentication setup failed' 
        });
      }

      authUser = signUpData.user;
    } else {
      authUser = signInData.user;
    }

    // Return success with user data
    return res.status(200).json({
      success: true,
      user: {
        id: telegramUser.id,
        telegram_id: telegramUser.telegram_id,
        username: telegramUser.username,
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name,
        is_registered: telegramUser.is_registered,
        registration_step: telegramUser.registration_step
      },
      redirect_url: telegramUser.is_registered ? '/dashboard' : '/onboarding'
    });

  } catch (error) {
    console.error('Telegram auth error:', error);
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
}
