import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    // Get the access token from the request
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (accessToken) {
      // Sign out from Supabase
      await supabase.auth.signOut()
    }

    // Create response and clear cookies
    const response = NextResponse.json({ success: true })
    
    // Clear authentication cookies
    response.cookies.set('sb-access-token', '', {
      path: '/',
      expires: new Date(0),
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })
    
    response.cookies.set('sb-refresh-token', '', {
      path: '/',
      expires: new Date(0),
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })

    return response
  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Handle GET request for logout (redirect-based logout)
  try {
    const response = NextResponse.redirect(new URL('/login', request.url))
    
    // Clear authentication cookies
    response.cookies.set('sb-access-token', '', {
      path: '/',
      expires: new Date(0),
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })
    
    response.cookies.set('sb-refresh-token', '', {
      path: '/',
      expires: new Date(0),
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })

    return response
  } catch (error) {
    console.error('Logout redirect error:', error)
    return NextResponse.redirect(new URL('/login?error=logout_failed', request.url))
  }
}
