import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    const refreshToken = request.cookies.get('sb-refresh-token')?.value

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'No refresh token provided' },
        { status: 401 }
      )
    }

    // Refresh the session
    const { data: { session }, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken
    })

    if (error || !session) {
      // Clear invalid cookies
      const response = NextResponse.json(
        { error: 'Failed to refresh session' },
        { status: 401 }
      )
      
      response.cookies.set('sb-access-token', '', {
        path: '/',
        expires: new Date(0),
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
      })
      
      response.cookies.set('sb-refresh-token', '', {
        path: '/',
        expires: new Date(0),
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
      })

      return response
    }

    // Create response with new tokens
    const response = NextResponse.json({
      success: true,
      user: session.user
    })

    // Set new access token
    response.cookies.set('sb-access-token', session.access_token, {
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })

    // Set new refresh token if provided
    if (session.refresh_token) {
      response.cookies.set('sb-refresh-token', session.refresh_token, {
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
      })
    }

    return response
  } catch (error) {
    console.error('Session refresh error:', error)
    return NextResponse.json(
      { error: 'Session refresh failed' },
      { status: 500 }
    )
  }
}
