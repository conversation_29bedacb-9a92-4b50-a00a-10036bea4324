import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createHash, createHmac } from 'crypto'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const telegramBotToken = process.env.TELEGRAM_BOT_TOKEN!

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Verify Telegram authentication data
function verifyTelegramAuth(data: any): boolean {
  if (!data) return false
  
  const { hash, ...userData } = data
  
  // Create data check string
  const dataCheckArr = Object.keys(userData)
    .sort()
    .map(key => `${key}=${userData[key]}`)
  
  const dataCheckString = dataCheckArr.join('\n')
  
  // Create secret key
  const secretKey = createHash('sha256')
    .update(telegramBotToken)
    .digest()
  
  // Calculate hash
  const calculatedHash = createHmac('sha256', secretKey)
    .update(dataCheckString)
    .digest('hex')
  
  return calculatedHash === hash
}

// Transform Telegram user data
function transformTelegramUser(data: any) {
  return {
    id: data.id.toString(),
    username: data.username || '',
    firstName: data.first_name || '',
    lastName: data.last_name || '',
    photoUrl: data.photo_url || '',
    authDate: data.auth_date
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get Telegram auth data from URL
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries())
    
    // Verify Telegram authentication
    if (!verifyTelegramAuth(params)) {
      return NextResponse.redirect(new URL('/login?error=Invalid+authentication', request.url))
    }
    
    // Transform Telegram data
    const telegramUser = transformTelegramUser(params)
    
    // Find or create user in database
    let { data: existingUser, error: findError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramUser.id)
      .single()
    
    if (findError && findError.code !== 'PGRST116') {
      console.error('Error finding user:', findError)
      return NextResponse.redirect(new URL('/login?error=Database+error', request.url))
    }
    
    // Create Supabase auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: `telegram_${telegramUser.id}@aureus.africa`,
      password: `telegram_${telegramUser.id}_${Date.now()}`,
      email_confirm: true,
      user_metadata: {
        telegram_id: telegramUser.id,
        telegram_username: telegramUser.username,
        telegram_first_name: telegramUser.firstName,
        telegram_last_name: telegramUser.lastName
      }
    })
    
    if (authError) {
      console.error('Error creating auth user:', authError)
      return NextResponse.redirect(new URL('/login?error=Auth+error', request.url))
    }
    
    // Create session
    const { data: session, error: sessionError } = await supabase.auth.admin.createSession({
      user_id: authUser.user.id,
      expires_in: 60 * 60 * 24 * 7 // 7 days
    })
    
    if (sessionError) {
      console.error('Error creating session:', sessionError)
      return NextResponse.redirect(new URL('/login?error=Session+error', request.url))
    }
    
    // Set cookies
    const response = NextResponse.redirect(new URL('/dashboard', request.url))
    
    response.cookies.set('sb-access-token', session.access_token, {
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })
    
    response.cookies.set('sb-refresh-token', session.refresh_token, {
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })
    
    return response
  } catch (error) {
    console.error('Telegram auth error:', error)
    return NextResponse.redirect(new URL('/login?error=Server+error', request.url))
  }
}
