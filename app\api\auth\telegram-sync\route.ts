import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!
);

// Helper function to verify JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
  } catch {
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get auth token from cookie
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    const { telegram_username, telegram_id } = await request.json();

    if (!telegram_username && !telegram_id) {
      return NextResponse.json(
        { error: 'Telegram username or ID is required' },
        { status: 400 }
      );
    }

    // Find Telegram user in database
    let telegramUser = null;
    
    if (telegram_id) {
      const { data } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegram_id)
        .single();
      telegramUser = data;
    } else if (telegram_username) {
      const { data } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('username', telegram_username.replace('@', ''))
        .single();
      telegramUser = data;
    }

    if (!telegramUser) {
      return NextResponse.json(
        { error: 'Telegram account not found. Please make sure you have used the bot first.' },
        { status: 404 }
      );
    }

    // Check if Telegram account is already linked to another user
    if (telegramUser.user_id && telegramUser.user_id !== decoded.userId) {
      return NextResponse.json(
        { error: 'This Telegram account is already linked to another user' },
        { status: 409 }
      );
    }

    // Link Telegram account to current user
    const { error: updateError } = await supabase
      .from('telegram_users')
      .update({
        user_id: decoded.userId,
        updated_at: new Date().toISOString()
      })
      .eq('id', telegramUser.id);

    if (updateError) {
      console.error('Telegram sync error:', updateError);
      return NextResponse.json(
        { error: 'Failed to link Telegram account' },
        { status: 500 }
      );
    }

    // Get updated user data with Telegram info
    const { data: user } = await supabase
      .from('users')
      .select('*')
      .eq('id', decoded.userId)
      .single();

    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      full_name: user.full_name,
      phone: user.phone,
      is_verified: user.is_verified,
      created_at: user.created_at,
      telegram_linked: true,
      telegram_info: {
        telegram_id: telegramUser.telegram_id,
        telegram_username: telegramUser.username,
        first_name: telegramUser.first_name,
        last_name: telegramUser.last_name
      }
    };

    return NextResponse.json({
      success: true,
      message: 'Telegram account linked successfully',
      user: userData
    });

  } catch (error) {
    console.error('Telegram sync error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Get auth token from cookie
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify token
    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Unlink Telegram account
    const { error: updateError } = await supabase
      .from('telegram_users')
      .update({
        user_id: null,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', decoded.userId);

    if (updateError) {
      console.error('Telegram unlink error:', updateError);
      return NextResponse.json(
        { error: 'Failed to unlink Telegram account' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Telegram account unlinked successfully'
    });

  } catch (error) {
    console.error('Telegram unlink error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
