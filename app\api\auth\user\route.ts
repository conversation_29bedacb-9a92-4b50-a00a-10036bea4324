import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'

export async function GET(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Get Telegram user profile
    const telegramId = user.user_metadata?.telegram_id
    let telegramProfile = null

    if (telegramId) {
      const { data: telegramUser, error: telegramError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('telegram_id', telegramId)
        .single()

      if (!telegramError && telegramUser) {
        telegramProfile = telegramUser
      }
    }

    // Check onboarding status
    let hasAcceptedTerms = false
    let hasSelectedCountry = false
    let hasCompletedKYC = false

    if (telegramProfile) {
      // Check terms acceptance
      const { data: termsAcceptance } = await supabase
        .from('terms_acceptance')
        .select('*')
        .eq('user_id', telegramProfile.id)
        .single()

      hasAcceptedTerms = !!termsAcceptance

      // Check country selection
      hasSelectedCountry = !!telegramProfile.country

      // Check KYC completion
      const { data: kycInfo } = await supabase
        .from('kyc_information')
        .select('verification_status')
        .eq('user_id', telegramProfile.id)
        .single()

      hasCompletedKYC = kycInfo?.verification_status === 'approved'
    }

    // Return user data with profile and onboarding status
    return NextResponse.json({
      user: {
        ...user,
        telegram_profile: telegramProfile,
        hasAcceptedTerms,
        hasSelectedCountry,
        hasCompletedKYC
      }
    })
  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { error: 'Failed to get user data' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { country, phone, address } = body

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Update telegram user profile
    const { data: updatedUser, error: updateError } = await supabase
      .from('telegram_users')
      .update({
        country,
        phone,
        address,
        updated_at: new Date().toISOString()
      })
      .eq('telegram_id', telegramId)
      .select()
      .single()

    if (updateError) {
      console.error('Update user error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update user profile' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      user: updatedUser
    })
  } catch (error) {
    console.error('Update user error:', error)
    return NextResponse.json(
      { error: 'Failed to update user profile' },
      { status: 500 }
    )
  }
}
