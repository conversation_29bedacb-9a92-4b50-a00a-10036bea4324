import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'
import { DataValidationService } from '@/lib/data-sync'

export async function POST(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Run comprehensive data validation
    const userValidation = await DataValidationService.validateUserData(userId)
    const businessValidation = await DataValidationService.validateBusinessLogic()

    // Check database consistency
    const consistencyIssues = []

    // Check for orphaned records
    const { data: orphanedShares } = await supabase
      .from('share_purchases')
      .select('id, user_id')
      .eq('user_id', userId)
      .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

    if (orphanedShares && orphanedShares.length > 0) {
      consistencyIssues.push({
        type: 'orphaned_shares',
        count: orphanedShares.length,
        description: 'Share purchases without valid user references'
      })
    }

    // Check for negative balances
    const { data: negativeBalance } = await supabase
      .from('commission_balances')
      .select('user_id, usdt_balance')
      .eq('user_id', userId)
      .lt('usdt_balance', 0)

    if (negativeBalance && negativeBalance.length > 0) {
      consistencyIssues.push({
        type: 'negative_balance',
        count: negativeBalance.length,
        description: 'Negative commission balance detected'
      })
    }

    // Check for duplicate referrals
    const { data: duplicateReferrals } = await supabase
      .from('referrals')
      .select('referred_id, count(*)')
      .eq('referred_id', userId)
      .group('referred_id')
      .having('count(*)', 'gt', 1)

    if (duplicateReferrals && duplicateReferrals.length > 0) {
      consistencyIssues.push({
        type: 'duplicate_referrals',
        count: duplicateReferrals.length,
        description: 'User has multiple referrers'
      })
    }

    // Check payment consistency
    const { data: userPayments } = await supabase
      .from('crypto_payment_transactions')
      .select('*')
      .eq('user_id', userId)

    const { data: userShares } = await supabase
      .from('share_purchases')
      .select('*')
      .eq('user_id', userId)

    const paymentTotal = userPayments?.reduce((sum, p) => sum + parseFloat(p.amount || 0), 0) || 0
    const shareTotal = userShares?.reduce((sum, s) => sum + parseFloat(s.total_amount || 0), 0) || 0

    if (Math.abs(paymentTotal - shareTotal) > 0.01) { // Allow for small rounding differences
      consistencyIssues.push({
        type: 'payment_share_mismatch',
        paymentTotal,
        shareTotal,
        difference: paymentTotal - shareTotal,
        description: 'Payment total does not match share purchase total'
      })
    }

    // Check for stale pending payments
    const stalePendingThreshold = new Date()
    stalePendingThreshold.setDate(stalePendingThreshold.getDate() - 7) // 7 days ago

    const { data: stalePending } = await supabase
      .from('crypto_payment_transactions')
      .select('id, created_at')
      .eq('user_id', userId)
      .eq('status', 'pending')
      .lt('created_at', stalePendingThreshold.toISOString())

    if (stalePending && stalePending.length > 0) {
      consistencyIssues.push({
        type: 'stale_pending_payments',
        count: stalePending.length,
        description: 'Payments pending for more than 7 days'
      })
    }

    // Calculate overall consistency score
    const totalIssues = userValidation.errors.length + 
                       userValidation.warnings.length + 
                       businessValidation.errors.length + 
                       businessValidation.warnings.length + 
                       consistencyIssues.length

    const isConsistent = totalIssues === 0
    const consistencyScore = Math.max(0, 100 - (totalIssues * 10)) // Deduct 10 points per issue

    return NextResponse.json({
      success: true,
      isConsistent,
      consistencyScore,
      userValidation,
      businessValidation,
      consistencyIssues,
      summary: {
        totalIssues,
        criticalErrors: userValidation.errors.length + businessValidation.errors.length,
        warnings: userValidation.warnings.length + businessValidation.warnings.length,
        consistencyIssues: consistencyIssues.length
      },
      recommendations: generateRecommendations(userValidation, businessValidation, consistencyIssues),
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Data validation error:', error)
    return NextResponse.json(
      { error: 'Failed to validate data synchronization' },
      { status: 500 }
    )
  }
}

function generateRecommendations(
  userValidation: any,
  businessValidation: any,
  consistencyIssues: any[]
): string[] {
  const recommendations: string[] = []

  // User validation recommendations
  if (userValidation.errors.length > 0) {
    recommendations.push('Review and fix user data errors to ensure proper synchronization')
  }

  if (userValidation.warnings.length > 0) {
    recommendations.push('Address user data warnings to improve data quality')
  }

  // Business logic recommendations
  if (businessValidation.errors.length > 0) {
    recommendations.push('Fix business logic errors to maintain system integrity')
  }

  // Consistency issue recommendations
  consistencyIssues.forEach(issue => {
    switch (issue.type) {
      case 'orphaned_shares':
        recommendations.push('Clean up orphaned share purchase records')
        break
      case 'negative_balance':
        recommendations.push('Investigate and correct negative commission balances')
        break
      case 'duplicate_referrals':
        recommendations.push('Resolve duplicate referral relationships')
        break
      case 'payment_share_mismatch':
        recommendations.push('Reconcile payment and share purchase totals')
        break
      case 'stale_pending_payments':
        recommendations.push('Review and process stale pending payments')
        break
    }
  })

  // General recommendations
  if (recommendations.length === 0) {
    recommendations.push('Data synchronization is working correctly')
    recommendations.push('Continue monitoring for any future inconsistencies')
  } else {
    recommendations.push('Run validation tests regularly to catch issues early')
    recommendations.push('Consider implementing automated data cleanup processes')
  }

  return recommendations
}
