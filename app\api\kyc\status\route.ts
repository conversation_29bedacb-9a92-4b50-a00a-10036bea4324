import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'

export async function GET(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    // Get KYC information
    const { data: kycInfo, error: kycError } = await supabase
      .from('kyc_information')
      .select('*')
      .eq('user_id', telegramUser.id)
      .single()

    // Get KYC documents
    const { data: documents, error: docsError } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('user_id', telegramUser.id)
      .order('uploaded_at', { ascending: false })

    if (docsError && docsError.code !== 'PGRST116') {
      return NextResponse.json(
        { error: 'Failed to get KYC documents' },
        { status: 500 }
      )
    }

    // Calculate completion status
    const requiredDocuments = ['identity_document', 'proof_of_address', 'selfie_with_id']
    const uploadedDocuments = documents?.map(doc => doc.document_type) || []
    const approvedDocuments = documents?.filter(doc => doc.status === 'approved').map(doc => doc.document_type) || []

    const completionStatus = {
      hasKycInfo: !!kycInfo,
      documentsUploaded: requiredDocuments.filter(type => uploadedDocuments.includes(type)).length,
      documentsApproved: requiredDocuments.filter(type => approvedDocuments.includes(type)).length,
      totalRequired: requiredDocuments.length,
      isComplete: kycInfo?.kyc_status === 'approved',
      status: kycInfo?.kyc_status || 'incomplete',
      completionPercentage: kycInfo?.kyc_status === 'approved' 
        ? 100 
        : Math.round(((uploadedDocuments.length / requiredDocuments.length) * 75) + (kycInfo ? 25 : 0))
    }

    return NextResponse.json({
      success: true,
      kycInfo: kycInfo || null,
      documents: documents || [],
      completionStatus,
      requiredDocuments: [
        {
          type: 'identity_document',
          name: 'Identity Document',
          description: 'National ID, Passport, or Driver\'s License',
          required: true,
          uploaded: uploadedDocuments.includes('identity_document'),
          approved: approvedDocuments.includes('identity_document')
        },
        {
          type: 'proof_of_address',
          name: 'Proof of Address',
          description: 'Utility bill, bank statement, or lease agreement (not older than 3 months)',
          required: true,
          uploaded: uploadedDocuments.includes('proof_of_address'),
          approved: approvedDocuments.includes('proof_of_address')
        },
        {
          type: 'selfie_with_id',
          name: 'Selfie with ID',
          description: 'Clear photo of yourself holding your ID document',
          required: true,
          uploaded: uploadedDocuments.includes('selfie_with_id'),
          approved: approvedDocuments.includes('selfie_with_id')
        }
      ]
    })

  } catch (error) {
    console.error('Get KYC status error:', error)
    return NextResponse.json(
      { error: 'Failed to get KYC status' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, documentId, adminNotes } = body

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'delete_document':
        // Delete document
        const { error: deleteError } = await supabase
          .from('kyc_documents')
          .delete()
          .eq('id', documentId)
          .eq('user_id', telegramUser.id) // Ensure user can only delete their own documents

        if (deleteError) {
          throw deleteError
        }

        return NextResponse.json({
          success: true,
          message: 'Document deleted successfully'
        })

      case 'resubmit_kyc':
        // Reset KYC status to allow resubmission
        const { error: resetError } = await supabase
          .from('kyc_information')
          .update({
            kyc_status: 'pending',
            updated_at: new Date().toISOString()
          })
          .eq('user_id', telegramUser.id)

        if (resetError) {
          throw resetError
        }

        // Reset all document statuses to pending
        const { error: resetDocsError } = await supabase
          .from('kyc_documents')
          .update({
            status: 'pending',
            admin_notes: null,
            reviewed_at: null
          })
          .eq('user_id', telegramUser.id)

        if (resetDocsError) {
          console.warn('Error resetting document statuses:', resetDocsError)
        }

        return NextResponse.json({
          success: true,
          message: 'KYC resubmitted for review'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('KYC action error:', error)
    return NextResponse.json(
      { error: 'Failed to process KYC action' },
      { status: 500 }
    )
  }
}
