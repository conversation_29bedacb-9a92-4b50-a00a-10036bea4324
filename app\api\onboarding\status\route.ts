import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'
import { calculateOnboardingStatus } from '@/lib/onboarding-validation'

export async function GET(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    // Check terms acceptance
    const { data: termsAcceptance } = await supabase
      .from('terms_acceptance')
      .select('*')
      .eq('user_id', telegramUser.id)
      .single()

    const hasAcceptedTerms = !!termsAcceptance

    // Check country selection
    const hasSelectedCountry = !!telegramUser.country

    // Check KYC completion
    const { data: kycInfo } = await supabase
      .from('kyc_information')
      .select('*')
      .eq('user_id', telegramUser.id)
      .single()

    const hasCompletedKYC = !!kycInfo
    const kycStatus = kycInfo?.kyc_status || 'incomplete'

    // Calculate comprehensive onboarding status
    const onboardingStatus = calculateOnboardingStatus(
      hasAcceptedTerms,
      hasSelectedCountry,
      hasCompletedKYC,
      kycStatus
    )

    return NextResponse.json({
      success: true,
      status: onboardingStatus,
      details: {
        termsAcceptance: termsAcceptance ? {
          accepted_at: termsAcceptance.accepted_at,
          version: termsAcceptance.version
        } : null,
        country: telegramUser.country,
        kycInfo: kycInfo ? {
          status: kycInfo.kyc_status,
          submitted_at: kycInfo.created_at,
          verified_at: kycInfo.verified_at
        } : null
      }
    })
  } catch (error) {
    console.error('Get onboarding status error:', error)
    return NextResponse.json(
      { error: 'Failed to get onboarding status' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { step, data } = body

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    let result

    switch (step) {
      case 'terms':
        // Accept terms
        const { error: termsError } = await supabase
          .from('terms_acceptance')
          .insert({
            user_id: telegramUser.id,
            terms_type: 'general',
            version: data.version || '1.0',
            accepted_at: new Date().toISOString()
          })

        if (termsError) {
          throw new Error(termsError.message)
        }

        result = { step: 'terms', completed: true }
        break

      case 'country':
        // Update country
        const { error: countryError } = await supabase
          .from('telegram_users')
          .update({ 
            country: data.country,
            updated_at: new Date().toISOString()
          })
          .eq('id', telegramUser.id)

        if (countryError) {
          throw new Error(countryError.message)
        }

        result = { step: 'country', completed: true, country: data.country }
        break

      case 'kyc':
        // Submit KYC information
        const { error: kycError } = await supabase
          .from('kyc_information')
          .insert({
            user_id: telegramUser.id,
            first_name: data.firstName,
            last_name: data.lastName,
            id_type: data.idType,
            id_number_encrypted: data.idNumber, // Should be encrypted in production
            id_number_hash: data.idNumber, // Should be hashed in production
            phone_number: data.phoneNumber,
            email_address: data.emailAddress,
            street_address: data.address,
            city: data.city,
            postal_code: data.postalCode,
            country_code: telegramUser.country || 'ZA',
            country_name: telegramUser.country || 'South Africa',
            data_consent_given: data.acceptedPrivacy,
            privacy_policy_accepted: data.acceptedPrivacy,
            kyc_status: 'pending',
            created_by_telegram_id: telegramUser.telegram_id
          })

        if (kycError) {
          throw new Error(kycError.message)
        }

        result = { step: 'kyc', completed: true, status: 'pending' }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid onboarding step' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      result
    })
  } catch (error) {
    console.error('Update onboarding step error:', error)
    return NextResponse.json(
      { error: 'Failed to update onboarding step' },
      { status: 500 }
    )
  }
}
