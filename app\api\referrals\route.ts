import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'

export async function GET(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    // Get referrals made by this user
    const { data: referrals, error: referralsError } = await supabase
      .from('referrals')
      .select(`
        id,
        referred_id,
        referral_code,
        commission_rate,
        total_commission,
        status,
        created_at,
        telegram_users!referrals_referred_id_fkey (
          id,
          telegram_id,
          username,
          first_name,
          last_name,
          country,
          created_at
        )
      `)
      .eq('referrer_id', telegramUser.id)
      .order('created_at', { ascending: false })

    if (referralsError) {
      return NextResponse.json(
        { error: 'Failed to get referrals' },
        { status: 500 }
      )
    }

    // Get commission balance
    const { data: commissionBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', telegramUser.id)
      .single()

    // Get commission transactions
    const { data: commissionTransactions, error: transactionsError } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('referrer_id', telegramUser.id)
      .order('created_at', { ascending: false })
      .limit(10)

    // Calculate referral statistics
    const totalReferrals = referrals?.length || 0
    const activeReferrals = referrals?.filter(r => r.status === 'active').length || 0
    
    // Get investment data for referred users to calculate commission earned
    const referredUserIds = referrals?.map(r => r.referred_id) || []
    let totalInvestmentVolume = 0
    let totalCommissionEarned = 0

    if (referredUserIds.length > 0) {
      const { data: investments } = await supabase
        .from('share_purchases')
        .select('user_id, total_amount')
        .in('user_id', referredUserIds)
        .eq('status', 'active')

      if (investments) {
        totalInvestmentVolume = investments.reduce((sum, inv) => sum + parseFloat(inv.total_amount), 0)
        totalCommissionEarned = totalInvestmentVolume * 0.15 // 15% commission rate
      }
    }

    const averageInvestmentPerReferral = totalReferrals > 0 ? totalInvestmentVolume / totalReferrals : 0
    const conversionRate = totalReferrals > 0 ? (activeReferrals / totalReferrals) * 100 : 0

    // Format referral data
    const formattedReferrals = referrals?.map(referral => ({
      id: referral.id,
      firstName: referral.telegram_users?.first_name || '',
      lastName: referral.telegram_users?.last_name || '',
      username: referral.telegram_users?.username || '',
      country: referral.telegram_users?.country || '',
      joinedDate: referral.created_at,
      status: referral.status,
      commissionRate: referral.commission_rate,
      totalCommission: referral.total_commission || 0
    })) || []

    return NextResponse.json({
      success: true,
      referralCode: telegramId.toString(),
      statistics: {
        totalReferrals,
        activeReferrals,
        totalCommissionEarned,
        totalInvestmentVolume,
        averageInvestmentPerReferral,
        conversionRate
      },
      commissionBalance: commissionBalance || {
        usdt_balance: 0,
        share_balance: 0,
        total_earned: 0,
        total_withdrawn: 0,
        pending_withdrawals: 0
      },
      referrals: formattedReferrals,
      recentTransactions: commissionTransactions || []
    })

  } catch (error) {
    console.error('Get referrals error:', error)
    return NextResponse.json(
      { error: 'Failed to get referral data' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, amount, network, walletAddress } = body

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'withdraw_commission':
        // Validate withdrawal amount
        if (!amount || amount < 25) {
          return NextResponse.json(
            { error: 'Minimum withdrawal amount is $25' },
            { status: 400 }
          )
        }

        if (!walletAddress || !network) {
          return NextResponse.json(
            { error: 'Wallet address and network are required' },
            { status: 400 }
          )
        }

        // Check commission balance
        const { data: balance } = await supabase
          .from('commission_balances')
          .select('usdt_balance')
          .eq('user_id', telegramUser.id)
          .single()

        if (!balance || balance.usdt_balance < amount) {
          return NextResponse.json(
            { error: 'Insufficient commission balance' },
            { status: 400 }
          )
        }

        // Create withdrawal request
        const { error: withdrawalError } = await supabase
          .from('commission_withdrawals')
          .insert({
            user_id: telegramUser.id,
            amount: amount,
            wallet_address: walletAddress,
            network: network,
            status: 'pending',
            created_at: new Date().toISOString()
          })

        if (withdrawalError) {
          return NextResponse.json(
            { error: 'Failed to create withdrawal request' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Withdrawal request submitted successfully'
        })

      case 'convert_to_shares':
        // Validate conversion amount
        if (!amount || amount <= 0) {
          return NextResponse.json(
            { error: 'Invalid conversion amount' },
            { status: 400 }
          )
        }

        // Check commission balance
        const { data: conversionBalance } = await supabase
          .from('commission_balances')
          .select('usdt_balance')
          .eq('user_id', telegramUser.id)
          .single()

        if (!conversionBalance || conversionBalance.usdt_balance < amount) {
          return NextResponse.json(
            { error: 'Insufficient commission balance' },
            { status: 400 }
          )
        }

        // Get current share price
        const { data: currentPhase } = await supabase
          .from('investment_phases')
          .select('price_per_share')
          .eq('is_active', true)
          .single()

        const sharePrice = currentPhase?.price_per_share || 1
        const sharesAmount = Math.floor(amount / sharePrice)

        // Create share purchase record
        const { error: shareError } = await supabase
          .from('share_purchases')
          .insert({
            user_id: telegramUser.id,
            package_name: 'Commission Conversion',
            shares_purchased: sharesAmount,
            total_amount: amount,
            payment_method: 'Commission Conversion',
            status: 'active',
            created_at: new Date().toISOString()
          })

        if (shareError) {
          return NextResponse.json(
            { error: 'Failed to convert commission to shares' },
            { status: 500 }
          )
        }

        // Update commission balance
        const { error: balanceUpdateError } = await supabase
          .from('commission_balances')
          .update({
            usdt_balance: conversionBalance.usdt_balance - amount,
            share_balance: (conversionBalance.share_balance || 0) + sharesAmount,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', telegramUser.id)

        if (balanceUpdateError) {
          console.error('Failed to update commission balance:', balanceUpdateError)
        }

        return NextResponse.json({
          success: true,
          message: `Successfully converted $${amount} to ${sharesAmount} shares`,
          sharesReceived: sharesAmount
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Referral action error:', error)
    return NextResponse.json(
      { error: 'Failed to process referral action' },
      { status: 500 }
    )
  }
}
