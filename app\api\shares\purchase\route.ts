import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase-client'
import { 
  calculateShares, 
  validatePhaseAvailability, 
  validatePurchaseLimits,
  getPackageInfo,
  calculateCommission
} from '@/lib/share-calculations'

export async function POST(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, paymentMethod, network, senderWallet, transactionHash, screenshotUrl } = body

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    // Get current active phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single()

    if (phaseError || !currentPhase) {
      return NextResponse.json(
        { error: 'No active investment phase found' },
        { status: 400 }
      )
    }

    // Validate purchase amount
    const amountNum = parseFloat(amount)
    if (isNaN(amountNum) || amountNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid purchase amount' },
        { status: 400 }
      )
    }

    // Calculate shares
    const shareCalculation = calculateShares(amountNum, currentPhase.price_per_share)
    
    if (!shareCalculation.isValid) {
      return NextResponse.json(
        { error: 'Invalid share calculation', details: shareCalculation.errors },
        { status: 400 }
      )
    }

    // Validate phase availability
    const phaseValidation = validatePhaseAvailability(shareCalculation.sharesAmount, currentPhase)
    
    if (!phaseValidation.isValid) {
      return NextResponse.json(
        { error: 'Phase validation failed', details: phaseValidation.errors },
        { status: 400 }
      )
    }

    // Get user's existing purchases for limit validation
    const { data: existingPurchases } = await supabase
      .from('share_purchases')
      .select('total_amount')
      .eq('user_id', telegramUser.id)

    const totalExistingAmount = existingPurchases?.reduce((sum, purchase) => 
      sum + parseFloat(purchase.total_amount), 0) || 0

    // Validate purchase limits
    const limitsValidation = validatePurchaseLimits(amountNum, telegramUser.id, totalExistingAmount)
    
    if (!limitsValidation.isValid) {
      return NextResponse.json(
        { error: 'Purchase limits validation failed', details: limitsValidation.errors },
        { status: 400 }
      )
    }

    // Get package information
    const packageInfo = getPackageInfo(amountNum)

    // Create payment transaction
    const paymentData = {
      user_id: telegramUser.id,
      amount: shareCalculation.totalCost,
      currency: paymentMethod === 'ZAR' ? 'ZAR' : 'USDT',
      network: paymentMethod === 'ZAR' ? 'BANK_TRANSFER' : network,
      sender_wallet: senderWallet || '',
      receiver_wallet: '', // Will be set based on network
      transaction_hash: transactionHash || null,
      screenshot_url: screenshotUrl || null,
      status: 'pending',
      admin_notes: `Package: ${packageInfo.name}, Shares: ${shareCalculation.sharesAmount}`
    }

    // Set receiver wallet based on network
    if (paymentMethod === 'ZAR') {
      paymentData.receiver_wallet = 'Aureus Alliance Holdings Bank Account'
    } else {
      // Get company wallet for the network
      const { data: companyWallet } = await supabase
        .from('company_wallets')
        .select('wallet_address')
        .eq('network', network)
        .eq('is_active', true)
        .single()

      if (companyWallet) {
        paymentData.receiver_wallet = companyWallet.wallet_address
      }
    }

    const { data: payment, error: paymentError } = await supabase
      .from('crypto_payment_transactions')
      .insert(paymentData)
      .select()
      .single()

    if (paymentError) {
      return NextResponse.json(
        { error: 'Failed to create payment transaction', details: paymentError.message },
        { status: 500 }
      )
    }

    // Calculate commission for referrer (if any)
    let commissionData = null
    const { data: referral } = await supabase
      .from('referrals')
      .select('referrer_id')
      .eq('referred_id', telegramUser.id)
      .single()

    if (referral) {
      const commission = calculateCommission(shareCalculation.totalCost, shareCalculation.sharesAmount)
      commissionData = {
        referrer_id: referral.referrer_id,
        referred_id: telegramUser.id,
        share_purchase_amount: shareCalculation.totalCost,
        usdt_commission: commission.usdtCommission,
        share_commission: commission.shareCommission,
        commission_rate: 15.0,
        status: 'pending', // Will be approved when payment is approved
        payment_date: new Date().toISOString()
      }
    }

    return NextResponse.json({
      success: true,
      payment: {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        network: payment.network,
        status: payment.status
      },
      shares: {
        amount: shareCalculation.sharesAmount,
        pricePerShare: currentPhase.price_per_share,
        totalCost: shareCalculation.totalCost,
        package: packageInfo.name
      },
      commission: commissionData,
      phase: {
        name: currentPhase.phase_name,
        number: currentPhase.phase_number
      },
      warnings: [
        ...shareCalculation.warnings,
        ...phaseValidation.warnings,
        ...limitsValidation.warnings
      ]
    })

  } catch (error) {
    console.error('Share purchase error:', error)
    return NextResponse.json(
      { error: 'Failed to process share purchase' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const accessToken = request.cookies.get('sb-access-token')?.value

    if (!accessToken) {
      return NextResponse.json(
        { error: 'No access token provided' },
        { status: 401 }
      )
    }

    // Get user from Supabase
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken)

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    const telegramId = user.user_metadata?.telegram_id

    if (!telegramId) {
      return NextResponse.json(
        { error: 'No Telegram ID found' },
        { status: 400 }
      )
    }

    // Get Telegram user profile
    const { data: telegramUser, error: telegramError } = await supabase
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', telegramId)
      .single()

    if (telegramError || !telegramUser) {
      return NextResponse.json(
        { error: 'Telegram user profile not found' },
        { status: 404 }
      )
    }

    // Get current active phase
    const { data: currentPhase, error: phaseError } = await supabase
      .from('investment_phases')
      .select('*')
      .eq('is_active', true)
      .single()

    if (phaseError || !currentPhase) {
      return NextResponse.json(
        { error: 'No active investment phase found' },
        { status: 400 }
      )
    }

    // Get user's share purchases
    const { data: sharePurchases, error: sharesError } = await supabase
      .from('share_purchases')
      .select('*')
      .eq('user_id', telegramUser.id)
      .order('created_at', { ascending: false })

    if (sharesError) {
      return NextResponse.json(
        { error: 'Failed to get share purchases' },
        { status: 500 }
      )
    }

    // Calculate totals
    const totalShares = sharePurchases?.reduce((sum, purchase) => 
      sum + (purchase.shares_purchased || 0), 0) || 0
    
    const totalInvested = sharePurchases?.reduce((sum, purchase) => 
      sum + parseFloat(purchase.total_amount || 0), 0) || 0

    return NextResponse.json({
      success: true,
      currentPhase: {
        name: currentPhase.phase_name,
        number: currentPhase.phase_number,
        pricePerShare: currentPhase.price_per_share,
        sharesAvailable: currentPhase.shares_available,
        sharesSold: currentPhase.shares_sold,
        remainingShares: currentPhase.shares_available - currentPhase.shares_sold
      },
      userShares: {
        totalShares,
        totalInvested,
        purchases: sharePurchases || []
      }
    })

  } catch (error) {
    console.error('Get share purchase info error:', error)
    return NextResponse.json(
      { error: 'Failed to get share purchase information' },
      { status: 500 }
    )
  }
}
