'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import DocumentUpload from '@/components/kyc/DocumentUpload'
import KYCStatusTracker from '@/components/kyc/KYCStatusTracker'
import DocumentList from '@/components/kyc/DocumentList'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useToastActions } from '@/components/ui/Toast'

interface KYCDocument {
  id: string
  document_type: string
  file_name: string
  file_url: string
  status: 'pending' | 'approved' | 'rejected'
  uploaded_at: string
  reviewed_at?: string
  admin_notes?: string
}

interface KYCInfo {
  id: string
  kyc_status: 'pending' | 'approved' | 'rejected' | 'incomplete'
  first_name: string
  last_name: string
  id_type: string
  phone_number: string
  email_address: string
  street_address: string
  city: string
  postal_code: string
  country_code: string
  created_at: string
  verified_at?: string
}

export default function KYCDashboard() {
  const { user } = useAuth()
  const { success, error } = useToastActions()
  const [kycInfo, setKycInfo] = useState<KYCInfo | null>(null)
  const [documents, setDocuments] = useState<KYCDocument[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    if (user?.telegram_profile) {
      loadKYCData()
    }
  }, [user])

  const loadKYCData = async () => {
    try {
      setLoading(true)

      // Load KYC information
      const { data: kycData, error: kycError } = await supabase
        .from('kyc_information')
        .select('*')
        .eq('user_id', user?.telegram_profile?.id)
        .single()

      if (kycError && kycError.code !== 'PGRST116') {
        throw kycError
      }

      setKycInfo(kycData)

      // Load KYC documents
      const { data: docsData, error: docsError } = await supabase
        .from('kyc_documents')
        .select('*')
        .eq('user_id', user?.telegram_profile?.id)
        .order('uploaded_at', { ascending: false })

      if (docsError && docsError.code !== 'PGRST116') {
        throw docsError
      }

      setDocuments(docsData || [])
    } catch (err) {
      console.error('Error loading KYC data:', err)
      error('Failed to load KYC information')
    } finally {
      setLoading(false)
    }
  }

  const handleDocumentUpload = async (documentType: string, file: File) => {
    try {
      setUploading(true)

      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop()
      const fileName = `kyc_${user?.telegram_profile?.telegram_id}_${documentType}_${Date.now()}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('kyc-documents')
        .upload(fileName, file)

      if (uploadError) {
        throw uploadError
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('kyc-documents')
        .getPublicUrl(fileName)

      // Save document record
      const { error: docError } = await supabase
        .from('kyc_documents')
        .insert({
          user_id: user?.telegram_profile?.id,
          document_type: documentType,
          file_name: file.name,
          file_url: publicUrl,
          file_path: uploadData.path,
          file_size: file.size,
          mime_type: file.type,
          status: 'pending',
          uploaded_by_telegram_id: user?.telegram_profile?.telegram_id
        })

      if (docError) {
        throw docError
      }

      success('Document uploaded successfully')
      await loadKYCData()
    } catch (err) {
      console.error('Error uploading document:', err)
      error('Failed to upload document')
    } finally {
      setUploading(false)
    }
  }

  const handleDeleteDocument = async (documentId: string, filePath: string) => {
    try {
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('kyc-documents')
        .remove([filePath])

      if (storageError) {
        console.warn('Storage deletion error:', storageError)
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('kyc_documents')
        .delete()
        .eq('id', documentId)

      if (dbError) {
        throw dbError
      }

      success('Document deleted successfully')
      await loadKYCData()
    } catch (err) {
      console.error('Error deleting document:', err)
      error('Failed to delete document')
    }
  }

  const getKYCStatusMessage = () => {
    if (!kycInfo) {
      return {
        status: 'incomplete',
        message: 'KYC verification not started',
        color: 'text-gray-600'
      }
    }

    switch (kycInfo.kyc_status) {
      case 'pending':
        return {
          status: 'pending',
          message: 'KYC verification under review',
          color: 'text-yellow-600'
        }
      case 'approved':
        return {
          status: 'approved',
          message: 'KYC verification approved',
          color: 'text-green-600'
        }
      case 'rejected':
        return {
          status: 'rejected',
          message: 'KYC verification rejected - please resubmit',
          color: 'text-red-600'
        }
      default:
        return {
          status: 'incomplete',
          message: 'KYC verification incomplete',
          color: 'text-gray-600'
        }
    }
  }

  const requiredDocuments = [
    {
      type: 'identity_document',
      name: 'Identity Document',
      description: 'National ID, Passport, or Driver\'s License',
      required: true
    },
    {
      type: 'proof_of_address',
      name: 'Proof of Address',
      description: 'Utility bill, bank statement, or lease agreement (not older than 3 months)',
      required: true
    },
    {
      type: 'selfie_with_id',
      name: 'Selfie with ID',
      description: 'Clear photo of yourself holding your ID document',
      required: true
    }
  ]

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  const statusInfo = getKYCStatusMessage()

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">KYC Verification</h1>
        <p className="text-gray-600">
          Complete your Know Your Customer verification to access all platform features
        </p>
      </div>

      {/* KYC Status */}
      <Card className="mb-8">
        <div className="p-6">
          <KYCStatusTracker 
            kycInfo={kycInfo}
            documents={documents}
            requiredDocuments={requiredDocuments}
          />
        </div>
      </Card>

      {/* Document Upload Section */}
      {(!kycInfo || kycInfo.kyc_status !== 'approved') && (
        <Card className="mb-8">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Upload Documents</h2>
            <DocumentUpload
              requiredDocuments={requiredDocuments}
              existingDocuments={documents}
              onUpload={handleDocumentUpload}
              uploading={uploading}
            />
          </div>
        </Card>
      )}

      {/* Document List */}
      {documents.length > 0 && (
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Uploaded Documents</h2>
            <DocumentList
              documents={documents}
              onDelete={handleDeleteDocument}
              canDelete={kycInfo?.kyc_status !== 'approved'}
            />
          </div>
        </Card>
      )}

      {/* Help Section */}
      <Card className="mt-8">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Need Help?</h2>
          <div className="space-y-4 text-sm text-gray-600">
            <p>
              <strong>Document Requirements:</strong> All documents must be clear, legible, and in color. 
              Accepted formats: JPG, PNG, PDF (max 10MB per file).
            </p>
            <p>
              <strong>Processing Time:</strong> KYC verification typically takes 1-3 business days. 
              You'll receive a notification once your documents are reviewed.
            </p>
            <p>
              <strong>Support:</strong> If you have questions or need assistance, contact our support team at{' '}
              <a 
                href="https://t.me/AureusAllianceBot" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                @AureusAllianceBot
              </a>
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}
