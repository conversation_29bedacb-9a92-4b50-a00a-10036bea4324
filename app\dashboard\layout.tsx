'use client'

import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, signOut } = useAuth()

  const handleLogout = async () => {
    await signOut()
  }

  return (
    <ProtectedRoute>
      <div className="dashboard-layout min-h-screen bg-gray-50">
        <header className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white shadow-lg">
          <div className="container mx-auto px-4 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <h1 className="text-2xl font-bold text-yellow-400">Aureus Alliance</h1>
                <span className="text-sm text-gray-300">Dashboard</span>
              </div>

              <div className="flex items-center space-x-4">
                {user?.telegram_profile && (
                  <div className="text-sm">
                    <span className="text-gray-300">Welcome, </span>
                    <span className="text-yellow-400 font-medium">
                      {user.telegram_profile.first_name}
                    </span>
                  </div>
                )}

                <button
                  onClick={handleLogout}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          {children}
        </main>
      </div>
    </ProtectedRoute>
  )
}
