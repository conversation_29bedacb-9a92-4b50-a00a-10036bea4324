'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getUserShares, getUserPayments, getCommissionBalance, getCurrentPhase } from '@/lib/supabase-client'
import DashboardStats from '@/components/dashboard/DashboardStats'
import QuickActions from '@/components/dashboard/QuickActions'
import RecentActivity from '@/components/dashboard/RecentActivity'
import PortfolioOverview from '@/components/dashboard/PortfolioOverview'
import InvestmentPhaseInfo from '@/components/dashboard/InvestmentPhaseInfo'
import { Card } from '@/components/ui/Card'

export default function Dashboard() {
  const { user } = useAuth()
  const [userShares, setUserShares] = useState<any[]>([])
  const [userPayments, setUserPayments] = useState<any[]>([])
  const [commissionBalance, setCommissionBalance] = useState<any>(null)
  const [currentPhase, setCurrentPhase] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUserData = async () => {
      if (!user?.telegram_profile?.id) return

      try {
        setLoading(true)

        // Fetch user shares
        const shares = await getUserShares(user.telegram_profile.id)
        setUserShares(shares)

        // Fetch user payments
        const payments = await getUserPayments(user.telegram_profile.id)
        setUserPayments(payments)

        // Fetch commission balance
        const commission = await getCommissionBalance(user.telegram_profile.id)
        setCommissionBalance(commission)

        // Fetch current phase
        const phase = await getCurrentPhase()
        setCurrentPhase(phase)
      } catch (error) {
        console.error('Error loading user data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadUserData()
  }, [user])

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-64 bg-gray-200 rounded mb-4"></div>
      </div>
    )
  }

  // Calculate total shares
  const totalShares = userShares.reduce((total, share) => total + (share.shares_purchased || 0), 0)

  // Calculate pending payments
  const pendingPayments = userPayments.filter(payment => payment.status === 'pending').length

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          Welcome back, {user?.telegram_profile?.first_name || 'Investor'}! 👋
        </h1>
        <p className="text-gray-600">
          Track your gold mining investments and grow your portfolio
        </p>
      </div>

      {/* Dashboard Stats */}
      <DashboardStats
        userShares={userShares}
        commissionBalance={commissionBalance}
        currentPhase={currentPhase}
        userPayments={userPayments}
        className="mb-8"
      />

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* Left Column - Portfolio and Actions */}
        <div className="lg:col-span-2 space-y-8">
          <PortfolioOverview
            userShares={userShares}
            currentPhase={currentPhase}
            commissionBalance={commissionBalance}
          />

          <RecentActivity
            userShares={userShares}
            userPayments={userPayments}
          />
        </div>

        {/* Right Column - Quick Actions and Phase Info */}
        <div className="space-y-8">
          <QuickActions
            pendingPayments={pendingPayments}
            hasShares={totalShares > 0}
            hasKYC={user?.telegram_profile?.kyc_status === 'approved'}
          />

          <InvestmentPhaseInfo currentPhase={currentPhase} />
        </div>
      </div>
    </div>
  )
}
