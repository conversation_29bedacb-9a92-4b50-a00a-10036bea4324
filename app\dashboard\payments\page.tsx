'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { getUserPayments } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

interface PaymentTransaction {
  id: string
  amount: number
  currency: string
  network: string
  sender_wallet: string
  receiver_wallet: string
  transaction_hash?: string
  screenshot_url?: string
  status: string
  admin_notes?: string
  created_at: string
  updated_at: string
}

export default function PaymentsPage() {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const [payments, setPayments] = useState<PaymentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)

  useEffect(() => {
    // Check if we just submitted a payment
    const status = searchParams.get('status')
    if (status === 'submitted') {
      setShowSuccessMessage(true)
      // Hide message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000)
    }
  }, [searchParams])

  useEffect(() => {
    const loadPayments = async () => {
      if (!user?.telegram_profile?.id) return
      
      try {
        const userPayments = await getUserPayments(user.telegram_profile.id)
        setPayments(userPayments)
      } catch (error) {
        console.error('Error loading payments:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadPayments()
  }, [user])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'rejected':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return '✅'
      case 'pending':
        return '⏳'
      case 'rejected':
        return '❌'
      default:
        return '❓'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-xl p-6 mb-8 text-white">
          <h1 className="text-3xl font-bold mb-2">Payment History</h1>
          <p className="text-blue-100">Track your share purchase payments and their status</p>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="mb-6 p-4 bg-green-100 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <span className="text-green-600 text-xl mr-2">✅</span>
              <div>
                <h3 className="font-medium text-green-800">Payment Submitted Successfully!</h3>
                <p className="text-green-700 text-sm">Your payment has been submitted for admin review. You'll be notified once it's processed.</p>
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-6 flex flex-wrap gap-4">
          <a
            href="/dashboard/purchase"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Make New Purchase
          </a>
          <a
            href="/dashboard"
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Back to Dashboard
          </a>
        </div>

        {/* Payments List */}
        {payments.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-gray-400 text-6xl mb-4">💳</div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">No Payments Yet</h2>
            <p className="text-gray-600 mb-4">You haven't made any share purchase payments yet.</p>
            <a
              href="/dashboard/purchase"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              Make Your First Purchase
            </a>
          </div>
        ) : (
          <div className="space-y-4">
            {payments.map((payment) => (
              <div key={payment.id} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="text-2xl mr-2">
                        {payment.currency === 'USDT' ? '₮' : payment.currency === 'ZAR' ? 'R' : '$'}
                      </span>
                      <h3 className="text-xl font-semibold text-gray-800">
                        {payment.currency} {payment.amount.toFixed(2)}
                      </h3>
                      <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {getStatusIcon(payment.status)} {payment.status.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Network:</span> {payment.network}
                      </div>
                      <div>
                        <span className="font-medium">Date:</span> {formatDate(payment.created_at)}
                      </div>
                      {payment.transaction_hash && (
                        <div className="md:col-span-2">
                          <span className="font-medium">Transaction Hash:</span>
                          <span className="font-mono text-xs ml-1 break-all">{payment.transaction_hash}</span>
                        </div>
                      )}
                      {payment.admin_notes && (
                        <div className="md:col-span-2">
                          <span className="font-medium">Notes:</span> {payment.admin_notes}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-2">
                    {payment.screenshot_url && (
                      <a
                        href={payment.screenshot_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium transition-colors duration-200 text-center"
                      >
                        View Proof
                      </a>
                    )}
                    
                    {payment.status === 'pending' && (
                      <div className="bg-yellow-50 text-yellow-700 px-3 py-2 rounded text-sm text-center">
                        Awaiting Review
                      </div>
                    )}
                    
                    {payment.status === 'approved' && (
                      <div className="bg-green-50 text-green-700 px-3 py-2 rounded text-sm text-center">
                        Payment Approved
                      </div>
                    )}
                    
                    {payment.status === 'rejected' && (
                      <div className="bg-red-50 text-red-700 px-3 py-2 rounded text-sm text-center">
                        Payment Rejected
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Payment Status Information */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Payment Status Guide</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-start">
              <span className="text-yellow-500 text-lg mr-2">⏳</span>
              <div>
                <h4 className="font-medium text-gray-800">Pending</h4>
                <p className="text-gray-600">Payment submitted and awaiting admin review</p>
              </div>
            </div>
            <div className="flex items-start">
              <span className="text-green-500 text-lg mr-2">✅</span>
              <div>
                <h4 className="font-medium text-gray-800">Approved</h4>
                <p className="text-gray-600">Payment verified and shares allocated to your account</p>
              </div>
            </div>
            <div className="flex items-start">
              <span className="text-red-500 text-lg mr-2">❌</span>
              <div>
                <h4 className="font-medium text-gray-800">Rejected</h4>
                <p className="text-gray-600">Payment could not be verified - contact support</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
