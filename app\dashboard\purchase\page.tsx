'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getCurrentPhase, supabase } from '@/lib/supabase-client'
import { getAvailablePaymentMethods, getCountryName } from '@/lib/country-utils'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

interface InvestmentPhase {
  id: number
  phase_number: number
  price_per_share: number
  shares_available: number
  shares_sold: number
  is_active: boolean
  start_date: string
  end_date?: string
}

export default function SharePurchasePage() {
  const { user } = useAuth()
  const [currentPhase, setCurrentPhase] = useState<InvestmentPhase | null>(null)
  const [loading, setLoading] = useState(true)
  const [purchaseAmount, setPurchaseAmount] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [calculatedShares, setCalculatedShares] = useState(0)

  useEffect(() => {
    const loadPhaseData = async () => {
      try {
        const phase = await getCurrentPhase()
        setCurrentPhase(phase)
        
        // Set default payment method based on country
        if (user?.telegram_profile?.country) {
          const availableMethods = getAvailablePaymentMethods(user.telegram_profile.country)
          if (availableMethods.length > 0) {
            setSelectedPaymentMethod(availableMethods[0])
          }
        }
      } catch (error) {
        console.error('Error loading phase data:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadPhaseData()
  }, [user])

  useEffect(() => {
    // Calculate shares when amount changes
    if (purchaseAmount && currentPhase) {
      const amount = parseFloat(purchaseAmount)
      if (!isNaN(amount) && amount > 0) {
        const shares = Math.floor(amount / currentPhase.price_per_share)
        setCalculatedShares(shares)
      } else {
        setCalculatedShares(0)
      }
    } else {
      setCalculatedShares(0)
    }
  }, [purchaseAmount, currentPhase])

  const handleProceedToPayment = () => {
    if (!purchaseAmount || !selectedPaymentMethod || calculatedShares === 0) {
      return
    }
    
    // Redirect to payment form based on method
    if (selectedPaymentMethod === 'USDT') {
      window.location.href = `/dashboard/purchase/usdt?amount=${purchaseAmount}&shares=${calculatedShares}`
    } else if (selectedPaymentMethod === 'ZAR') {
      window.location.href = `/dashboard/purchase/zar?amount=${purchaseAmount}&shares=${calculatedShares}`
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!currentPhase) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">No Active Phase</h1>
          <p className="text-gray-600">There is currently no active investment phase available.</p>
        </div>
      </ProtectedRoute>
    )
  }

  const availablePaymentMethods = user?.telegram_profile?.country 
    ? getAvailablePaymentMethods(user.telegram_profile.country)
    : ['USDT']

  const remainingShares = currentPhase.shares_available - currentPhase.shares_sold
  const phaseProgress = (currentPhase.shares_sold / currentPhase.shares_available) * 100

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-4xl mx-auto">
        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-xl p-6 mb-8 text-white">
          <h1 className="text-3xl font-bold mb-2">Purchase Gold Mining Shares</h1>
          <p className="text-yellow-100">Invest in Aureus Alliance Holdings gold placer deposit mining operations</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Phase Information */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Current Investment Phase</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Phase Number:</span>
                <span className="font-semibold text-2xl text-blue-600">#{currentPhase.phase_number}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Price per Share:</span>
                <span className="font-semibold text-2xl text-green-600">${currentPhase.price_per_share.toFixed(2)}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Available Shares:</span>
                <span className="font-semibold text-lg">{remainingShares.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Shares Sold:</span>
                <span className="font-semibold text-lg">{currentPhase.shares_sold.toLocaleString()}</span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Phase Progress</span>
                <span>{phaseProgress.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${phaseProgress}%` }}
                ></div>
              </div>
            </div>

            {/* Phase Transition Warning */}
            {phaseProgress > 90 && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 text-sm">
                  ⚠️ This phase is nearly complete. Price will increase in the next phase.
                </p>
              </div>
            )}
          </div>

          {/* Purchase Form */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Purchase Shares</h2>
            
            <div className="space-y-4">
              {/* Country Display */}
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                <p className="text-sm text-blue-700">
                  <span className="font-medium">Your Country:</span> {getCountryName(user?.telegram_profile?.country || '')}
                </p>
              </div>

              {/* Purchase Amount */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Purchase Amount (USD)
                </label>
                <input
                  type="number"
                  value={purchaseAmount}
                  onChange={(e) => setPurchaseAmount(e.target.value)}
                  placeholder="Enter amount in USD"
                  min="1"
                  step="0.01"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Payment Method Selection */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Payment Method
                </label>
                <select
                  value={selectedPaymentMethod}
                  onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select payment method</option>
                  {availablePaymentMethods.map(method => (
                    <option key={method} value={method}>
                      {method === 'USDT' ? 'USDT (Cryptocurrency)' : 'ZAR (Bank Transfer)'}
                    </option>
                  ))}
                </select>
              </div>

              {/* Calculation Display */}
              {calculatedShares > 0 && (
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h3 className="font-medium text-green-800 mb-2">Purchase Summary</h3>
                  <div className="space-y-1 text-sm text-green-700">
                    <div className="flex justify-between">
                      <span>Amount:</span>
                      <span>${parseFloat(purchaseAmount).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Price per Share:</span>
                      <span>${currentPhase.price_per_share.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>Shares to Receive:</span>
                      <span>{calculatedShares.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Proceed Button */}
              <button
                onClick={handleProceedToPayment}
                disabled={!purchaseAmount || !selectedPaymentMethod || calculatedShares === 0}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors duration-200 ${
                  !purchaseAmount || !selectedPaymentMethod || calculatedShares === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                Proceed to Payment
              </button>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">Important Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Share Ownership</h4>
              <p>Shares represent ownership in Aureus Alliance Holdings (Pty) Ltd, a South African registered company.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Dividend Payments</h4>
              <p>Shareholders receive dividends based on mining operations performance and company profits.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Payment Processing</h4>
              <p>All payments require admin approval. You will be notified once your payment is processed.</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Share Certificates</h4>
              <p>Digital share certificates will be generated after KYC completion and payment approval.</p>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
