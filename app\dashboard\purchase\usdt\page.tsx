'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import { getRecommendedUSDTNetwork } from '@/lib/country-utils'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

const USDT_NETWORKS = [
  { code: 'ETH', name: 'Ethereum (ETH)', fee: 'High fees, secure' },
  { code: 'BSC', name: 'Binance Smart Chain (BSC)', fee: 'Low fees, fast' },
  { code: 'POLYGON', name: 'Polygon (MATIC)', fee: 'Very low fees, fast' },
  { code: 'TRON', name: 'TRON (TRX)', fee: 'Low fees, popular in Asia' }
]

const COMPANY_WALLETS = {
  ETH: '******************************************',
  BSC: '******************************************',
  POLYGON: '******************************************',
  TRON: 'TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7'
}

export default function USDTPaymentPage() {
  const { user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [amount, setAmount] = useState('')
  const [shares, setShares] = useState('')
  const [selectedNetwork, setSelectedNetwork] = useState('')
  const [senderWallet, setSenderWallet] = useState('')
  const [transactionHash, setTransactionHash] = useState('')
  const [screenshot, setScreenshot] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Get parameters from URL
    const amountParam = searchParams.get('amount')
    const sharesParam = searchParams.get('shares')
    
    if (amountParam) setAmount(amountParam)
    if (sharesParam) setShares(sharesParam)
    
    // Set recommended network based on country
    if (user?.telegram_profile?.country) {
      const recommendedNetwork = getRecommendedUSDTNetwork(user.telegram_profile.country)
      setSelectedNetwork(recommendedNetwork)
    }
  }, [searchParams, user])

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type and size
      if (!file.type.startsWith('image/')) {
        setError('Please upload an image file')
        return
      }
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setError('File size must be less than 5MB')
        return
      }
      setScreenshot(file)
      setError(null)
    }
  }

  const uploadScreenshot = async (file: File): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${user?.telegram_profile?.telegram_id}_${Date.now()}.${fileExt}`
      
      const { data, error } = await supabase.storage
        .from('payment-proofs')
        .upload(fileName, file)
      
      if (error) {
        console.error('Upload error:', error)
        return null
      }
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('payment-proofs')
        .getPublicUrl(fileName)
      
      return publicUrl
    } catch (error) {
      console.error('Upload error:', error)
      return null
    }
  }

  const handleSubmitPayment = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }
    
    if (!senderWallet || !selectedNetwork) {
      setError('Please fill in all required fields')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      let screenshotUrl = null
      
      // Upload screenshot if provided
      if (screenshot) {
        screenshotUrl = await uploadScreenshot(screenshot)
        if (!screenshotUrl) {
          throw new Error('Failed to upload screenshot')
        }
      }
      
      // Create payment transaction
      const { error: paymentError } = await supabase
        .from('crypto_payment_transactions')
        .insert({
          user_id: user.telegram_profile.id,
          amount: parseFloat(amount),
          currency: 'USDT',
          network: selectedNetwork,
          sender_wallet: senderWallet,
          receiver_wallet: COMPANY_WALLETS[selectedNetwork as keyof typeof COMPANY_WALLETS],
          transaction_hash: transactionHash || null,
          screenshot_url: screenshotUrl,
          status: 'pending'
        })
      
      if (paymentError) {
        throw new Error(paymentError.message)
      }
      
      // Redirect to payment status page
      router.push('/dashboard/payments?status=submitted')
    } catch (err) {
      console.error('Payment submission error:', err)
      setError('Failed to submit payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const companyWallet = selectedNetwork ? COMPANY_WALLETS[selectedNetwork as keyof typeof COMPANY_WALLETS] : ''

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">USDT Payment</h1>
            <p className="text-gray-600">Complete your share purchase using USDT cryptocurrency</p>
          </div>

          {/* Purchase Summary */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 border border-blue-100">
            <h2 className="font-semibold text-blue-800 mb-2">Purchase Summary</h2>
            <div className="space-y-1 text-sm text-blue-700">
              <div className="flex justify-between">
                <span>Amount:</span>
                <span>${parseFloat(amount || '0').toFixed(2)} USD</span>
              </div>
              <div className="flex justify-between">
                <span>Shares:</span>
                <span>{parseInt(shares || '0').toLocaleString()}</span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmitPayment} className="space-y-6">
            {/* Network Selection */}
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                USDT Network *
              </label>
              <select
                value={selectedNetwork}
                onChange={(e) => setSelectedNetwork(e.target.value)}
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select network</option>
                {USDT_NETWORKS.map(network => (
                  <option key={network.code} value={network.code}>
                    {network.name} - {network.fee}
                  </option>
                ))}
              </select>
            </div>

            {/* Company Wallet Display */}
            {selectedNetwork && (
              <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                <h3 className="font-medium text-yellow-800 mb-2">Send USDT to this address:</h3>
                <div className="bg-white p-3 rounded border font-mono text-sm break-all">
                  {companyWallet}
                </div>
                <p className="text-yellow-700 text-sm mt-2">
                  ⚠️ Only send USDT on {USDT_NETWORKS.find(n => n.code === selectedNetwork)?.name} network to this address
                </p>
              </div>
            )}

            {/* Sender Wallet */}
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Your Wallet Address *
              </label>
              <input
                type="text"
                value={senderWallet}
                onChange={(e) => setSenderWallet(e.target.value)}
                placeholder="Enter the wallet address you're sending from"
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                This helps us verify the transaction
              </p>
            </div>

            {/* Transaction Hash */}
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Transaction Hash (Optional)
              </label>
              <input
                type="text"
                value={transactionHash}
                onChange={(e) => setTransactionHash(e.target.value)}
                placeholder="Enter transaction hash if available"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                You can add this later if not available now
              </p>
            </div>

            {/* Screenshot Upload */}
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Payment Screenshot (Optional)
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Upload a screenshot of your transaction for faster processing
              </p>
            </div>

            {error && (
              <div className="p-3 bg-red-100 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-between">
              <button
                type="button"
                onClick={() => router.push('/dashboard/purchase')}
                className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={loading || !selectedNetwork || !senderWallet}
                className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
                  loading || !selectedNetwork || !senderWallet
                    ? 'bg-blue-300 text-white cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {loading ? 'Submitting...' : 'Submit Payment'}
              </button>
            </div>
          </form>

          {/* Instructions */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-2">Payment Instructions</h3>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Select the USDT network you want to use</li>
              <li>Send exactly ${amount} worth of USDT to the displayed address</li>
              <li>Enter your wallet address and transaction details</li>
              <li>Submit this form for admin review</li>
              <li>You'll be notified once your payment is approved</li>
            </ol>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
