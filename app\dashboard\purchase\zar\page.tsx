'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

// Company bank details for ZAR payments
const COMPANY_BANK_DETAILS = {
  bankName: 'First National Bank (FNB)',
  accountName: 'AUREUS ALLIANCE HOLDINGS (PTY) LTD',
  accountNumber: '***********',
  branchCode: '250655',
  accountType: 'Business Current Account',
  reference: 'SHARE_PURCHASE'
}

export default function ZARPaymentPage() {
  const { user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [amount, setAmount] = useState('')
  const [shares, setShares] = useState('')
  const [zarAmount, setZarAmount] = useState('')
  const [senderName, setSenderName] = useState('')
  const [senderAccount, setSenderAccount] = useState('')
  const [senderBank, setSenderBank] = useState('')
  const [referenceNumber, setReferenceNumber] = useState('')
  const [proofFile, setProofFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Get parameters from URL
    const amountParam = searchParams.get('amount')
    const sharesParam = searchParams.get('shares')
    
    if (amountParam) {
      setAmount(amountParam)
      // Convert USD to ZAR (approximate rate - in production, use real-time rates)
      const usdAmount = parseFloat(amountParam)
      const zarRate = 18.5 // Approximate USD to ZAR rate
      setZarAmount((usdAmount * zarRate).toFixed(2))
    }
    if (sharesParam) setShares(sharesParam)
    
    // Pre-fill sender name from user profile
    if (user?.telegram_profile) {
      const fullName = `${user.telegram_profile.first_name} ${user.telegram_profile.last_name || ''}`.trim()
      setSenderName(fullName)
    }
  }, [searchParams, user])

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type and size
      if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
        setError('Please upload an image or PDF file')
        return
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        setError('File size must be less than 10MB')
        return
      }
      setProofFile(file)
      setError(null)
    }
  }

  const uploadProofFile = async (file: File): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `zar_${user?.telegram_profile?.telegram_id}_${Date.now()}.${fileExt}`
      
      const { data, error } = await supabase.storage
        .from('payment-proofs')
        .upload(fileName, file)
      
      if (error) {
        console.error('Upload error:', error)
        return null
      }
      
      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('payment-proofs')
        .getPublicUrl(fileName)
      
      return publicUrl
    } catch (error) {
      console.error('Upload error:', error)
      return null
    }
  }

  const handleSubmitPayment = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }
    
    if (!senderName || !senderAccount || !senderBank || !proofFile) {
      setError('Please fill in all required fields and upload proof of payment')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // Upload proof file
      const proofUrl = await uploadProofFile(proofFile)
      if (!proofUrl) {
        throw new Error('Failed to upload proof of payment')
      }
      
      // Create payment transaction using crypto_payment_transactions table
      // with network = 'BANK_TRANSFER' and currency = 'ZAR'
      const { error: paymentError } = await supabase
        .from('crypto_payment_transactions')
        .insert({
          user_id: user.telegram_profile.id,
          amount: parseFloat(amount), // USD amount
          currency: 'ZAR',
          network: 'BANK_TRANSFER',
          sender_wallet: `${senderName} - ${senderAccount} (${senderBank})`, // Store sender details
          receiver_wallet: `${COMPANY_BANK_DETAILS.accountName} - ${COMPANY_BANK_DETAILS.accountNumber}`,
          transaction_hash: referenceNumber || null,
          screenshot_url: proofUrl,
          status: 'pending',
          admin_notes: `ZAR Amount: R${zarAmount}, Reference: ${referenceNumber || 'N/A'}`
        })
      
      if (paymentError) {
        throw new Error(paymentError.message)
      }
      
      // Redirect to payment status page
      router.push('/dashboard/payments?status=submitted')
    } catch (err) {
      console.error('Payment submission error:', err)
      setError('Failed to submit payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">ZAR Bank Transfer</h1>
            <p className="text-gray-600">Complete your share purchase using South African Rand bank transfer</p>
          </div>

          {/* Purchase Summary */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6 border border-blue-100">
            <h2 className="font-semibold text-blue-800 mb-2">Purchase Summary</h2>
            <div className="space-y-1 text-sm text-blue-700">
              <div className="flex justify-between">
                <span>USD Amount:</span>
                <span>${parseFloat(amount || '0').toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>ZAR Amount (approx):</span>
                <span>R{parseFloat(zarAmount || '0').toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Shares:</span>
                <span>{parseInt(shares || '0').toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Bank Details */}
          <div className="bg-green-50 rounded-lg p-4 mb-6 border border-green-200">
            <h3 className="font-medium text-green-800 mb-3">Company Bank Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-green-700">Bank:</span>
                <span className="font-medium">{COMPANY_BANK_DETAILS.bankName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Account Name:</span>
                <span className="font-medium">{COMPANY_BANK_DETAILS.accountName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Account Number:</span>
                <span className="font-medium font-mono">{COMPANY_BANK_DETAILS.accountNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Branch Code:</span>
                <span className="font-medium font-mono">{COMPANY_BANK_DETAILS.branchCode}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Account Type:</span>
                <span className="font-medium">{COMPANY_BANK_DETAILS.accountType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Reference:</span>
                <span className="font-medium">{user?.telegram_profile?.telegram_id || 'SHARE_PURCHASE'}</span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmitPayment} className="space-y-6">
            {/* Sender Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Your Full Name *
                </label>
                <input
                  type="text"
                  value={senderName}
                  onChange={(e) => setSenderName(e.target.value)}
                  placeholder="As it appears on your bank account"
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Your Account Number *
                </label>
                <input
                  type="text"
                  value={senderAccount}
                  onChange={(e) => setSenderAccount(e.target.value)}
                  placeholder="Your bank account number"
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Your Bank Name *
              </label>
              <input
                type="text"
                value={senderBank}
                onChange={(e) => setSenderBank(e.target.value)}
                placeholder="e.g., Standard Bank, ABSA, FNB, Nedbank"
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Reference Number (Optional)
              </label>
              <input
                type="text"
                value={referenceNumber}
                onChange={(e) => setReferenceNumber(e.target.value)}
                placeholder="Transaction reference from your bank"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Proof Upload */}
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                Proof of Payment *
              </label>
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={handleFileUpload}
                required
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Upload a screenshot or PDF of your bank transfer confirmation
              </p>
            </div>

            {error && (
              <div className="p-3 bg-red-100 text-red-700 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-between">
              <button
                type="button"
                onClick={() => router.push('/dashboard/purchase')}
                className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={loading || !senderName || !senderAccount || !senderBank || !proofFile}
                className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
                  loading || !senderName || !senderAccount || !senderBank || !proofFile
                    ? 'bg-blue-300 text-white cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {loading ? 'Submitting...' : 'Submit Payment'}
              </button>
            </div>
          </form>

          {/* Instructions */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-2">Payment Instructions</h3>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Transfer R{zarAmount} to the company bank account above</li>
              <li>Use your Telegram ID ({user?.telegram_profile?.telegram_id}) as the reference</li>
              <li>Take a screenshot or save the confirmation from your bank</li>
              <li>Fill in your details and upload the proof of payment</li>
              <li>Submit this form for admin review</li>
              <li>You'll be notified once your payment is approved</li>
            </ol>
            <div className="mt-3 p-2 bg-yellow-100 rounded text-yellow-800 text-sm">
              <strong>Note:</strong> Exchange rates are approximate. Final share allocation will be based on the USD equivalent at the time of processing.
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
