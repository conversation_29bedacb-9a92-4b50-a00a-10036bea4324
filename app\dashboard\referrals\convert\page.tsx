'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { getCommissionBalance, getCurrentPhase, supabase } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

interface InvestmentPhase {
  id: number
  phase_number: number
  price_per_share: number
  shares_available: number
  shares_sold: number
  is_active: boolean
}

export default function ConvertCommissionPage() {
  const { user } = useAuth()
  const router = useRouter()
  
  const [availableBalance, setAvailableBalance] = useState(0)
  const [currentPhase, setCurrentPhase] = useState<InvestmentPhase | null>(null)
  const [conversionAmount, setConversionAmount] = useState('')
  const [calculatedShares, setCalculatedShares] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dataLoading, setDataLoading] = useState(true)

  useEffect(() => {
    const loadData = async () => {
      if (!user?.telegram_profile?.id) return
      
      try {
        // Get commission balance
        const balance = await getCommissionBalance(user.telegram_profile.id)
        setAvailableBalance(balance?.usdt_balance || 0)
        
        // Get current phase
        const phase = await getCurrentPhase()
        setCurrentPhase(phase)
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setDataLoading(false)
      }
    }
    
    loadData()
  }, [user])

  useEffect(() => {
    // Calculate shares when amount changes
    if (conversionAmount && currentPhase) {
      const amount = parseFloat(conversionAmount)
      if (!isNaN(amount) && amount > 0) {
        const shares = Math.floor(amount / currentPhase.price_per_share)
        setCalculatedShares(shares)
      } else {
        setCalculatedShares(0)
      }
    } else {
      setCalculatedShares(0)
    }
  }, [conversionAmount, currentPhase])

  const handleConvert = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.telegram_profile?.id || !currentPhase) {
      setError('User profile or phase information not found')
      return
    }
    
    const amount = parseFloat(conversionAmount)
    
    if (!amount || amount <= 0) {
      setError('Please enter a valid conversion amount')
      return
    }
    
    if (amount > availableBalance) {
      setError('Conversion amount exceeds available balance')
      return
    }
    
    if (calculatedShares === 0) {
      setError('Conversion amount too small to purchase shares')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // Create commission conversion record
      const { error: conversionError } = await supabase
        .from('commission_conversions')
        .insert({
          user_id: user.telegram_profile.id,
          commission_amount: amount,
          shares_purchased: calculatedShares,
          share_price: currentPhase.price_per_share,
          phase_id: currentPhase.id,
          status: 'pending'
        })
      
      if (conversionError) {
        throw new Error(conversionError.message)
      }
      
      // Create commission transaction record
      const { error: transactionError } = await supabase
        .from('commission_transactions')
        .insert({
          user_id: user.telegram_profile.id,
          transaction_type: 'conversion',
          amount: -amount, // Negative for conversion
          description: `Converted to ${calculatedShares} shares at $${currentPhase.price_per_share}/share`,
          status: 'pending'
        })
      
      if (transactionError) {
        console.error('Error creating transaction record:', transactionError)
      }
      
      // Redirect to success page
      router.push('/dashboard/referrals?status=conversion_submitted')
    } catch (err) {
      console.error('Conversion error:', err)
      setError('Failed to submit conversion request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleMaxAmount = () => {
    setConversionAmount(availableBalance.toString())
  }

  if (dataLoading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!currentPhase) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">No Active Phase</h1>
            <p className="text-gray-600 mb-4">There is currently no active investment phase available for conversion.</p>
            <a
              href="/dashboard/referrals"
              className="text-blue-600 hover:text-blue-800 font-medium underline"
            >
              ← Back to Referrals Dashboard
            </a>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Convert Commission to Shares</h1>
            <p className="text-gray-600">Convert your USDT commission earnings into gold mining shares</p>
          </div>

          {/* Balance and Phase Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 rounded-lg p-4 border border-green-100">
              <h2 className="font-semibold text-green-800 mb-2">Available Balance</h2>
              <div className="text-2xl font-bold text-green-600 mb-1">
                ${availableBalance.toFixed(2)} USDT
              </div>
              <p className="text-green-700 text-sm">Available for conversion</p>
            </div>
            
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <h2 className="font-semibold text-blue-800 mb-2">Current Phase</h2>
              <div className="text-2xl font-bold text-blue-600 mb-1">
                ${currentPhase.price_per_share.toFixed(2)}
              </div>
              <p className="text-blue-700 text-sm">Price per share (Phase #{currentPhase.phase_number})</p>
            </div>
          </div>

          {availableBalance === 0 ? (
            <div className="bg-yellow-50 rounded-lg p-4 mb-6 border border-yellow-200">
              <h3 className="font-medium text-yellow-800 mb-2">No Commission Available</h3>
              <p className="text-yellow-700 text-sm">
                You don't have any commission balance available for conversion. Start referring users to earn commissions!
              </p>
              <div className="mt-3">
                <a
                  href="/dashboard/referrals"
                  className="text-yellow-800 hover:text-yellow-900 font-medium text-sm underline"
                >
                  ← Back to Referrals Dashboard
                </a>
              </div>
            </div>
          ) : (
            <form onSubmit={handleConvert} className="space-y-6">
              {/* Conversion Amount */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Conversion Amount (USDT) *
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={conversionAmount}
                    onChange={(e) => setConversionAmount(e.target.value)}
                    placeholder="Enter amount to convert"
                    min="0.01"
                    max={availableBalance}
                    step="0.01"
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 pr-16"
                  />
                  <button
                    type="button"
                    onClick={handleMaxAmount}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
                  >
                    MAX
                  </button>
                </div>
                <p className="text-gray-500 text-sm mt-1">
                  Maximum: ${availableBalance.toFixed(2)} USDT
                </p>
              </div>

              {/* Conversion Preview */}
              {calculatedShares > 0 && (
                <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
                  <h3 className="font-medium text-purple-800 mb-3">Conversion Preview</h3>
                  <div className="space-y-2 text-sm text-purple-700">
                    <div className="flex justify-between">
                      <span>Commission Amount:</span>
                      <span className="font-medium">${parseFloat(conversionAmount).toFixed(2)} USDT</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Share Price:</span>
                      <span className="font-medium">${currentPhase.price_per_share.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Phase:</span>
                      <span className="font-medium">#{currentPhase.phase_number}</span>
                    </div>
                    <hr className="border-purple-200" />
                    <div className="flex justify-between font-semibold">
                      <span>Shares to Receive:</span>
                      <span className="text-purple-800">{calculatedShares.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Exact Value:</span>
                      <span>${(calculatedShares * currentPhase.price_per_share).toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="p-3 bg-red-100 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => router.push('/dashboard/referrals')}
                  className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || !conversionAmount || calculatedShares === 0}
                  className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
                    loading || !conversionAmount || calculatedShares === 0
                      ? 'bg-blue-300 text-white cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  {loading ? 'Processing...' : 'Convert to Shares'}
                </button>
              </div>
            </form>
          )}

          {/* Benefits Information */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-2">Benefits of Converting to Shares</h3>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Earn dividends from gold mining operations</li>
              <li>Own physical assets in gold mining equipment and land</li>
              <li>Benefit from potential share price appreciation</li>
              <li>Receive digital share certificates</li>
              <li>Participate in company growth and expansion</li>
              <li>No withdrawal fees or network costs</li>
            </ul>
          </div>

          {/* Important Notes */}
          <div className="mt-4 bg-blue-50 rounded-lg p-4">
            <h3 className="font-medium text-blue-800 mb-2">Important Notes</h3>
            <ul className="text-sm text-blue-700 space-y-1 list-disc list-inside">
              <li>Conversions are processed at current phase prices</li>
              <li>All conversions require admin approval</li>
              <li>Shares are allocated after approval (usually within 24 hours)</li>
              <li>Fractional shares are rounded down to whole numbers</li>
              <li>You'll receive a notification once conversion is complete</li>
            </ul>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
