'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getCommissionBalance, supabase } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import ReferralLinkGenerator from '@/components/referrals/ReferralLinkGenerator'
import ReferralStats from '@/components/referrals/ReferralStats'
import CommissionBalance from '@/components/referrals/CommissionBalance'
import ReferralList from '@/components/referrals/ReferralList'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface ReferralUser {
  id: number
  telegram_id: string
  username?: string
  first_name: string
  last_name?: string
  country?: string
  created_at: string
  total_shares?: number
  total_invested?: number
}

interface CommissionTransaction {
  id: string
  transaction_type: string
  amount: number
  description: string
  created_at: string
  status: string
}

interface CommissionBalance {
  usdt_balance: number
  total_earned: number
  total_withdrawn: number
  pending_withdrawals: number
}

export default function ReferralsPage() {
  const { user } = useAuth()
  const [referrals, setReferrals] = useState<ReferralUser[]>([])
  const [commissionBalance, setCommissionBalance] = useState<CommissionBalance | null>(null)
  const [commissionHistory, setCommissionHistory] = useState<CommissionTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [referralCode, setReferralCode] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  const [activeTab, setActiveTab] = useState<'overview' | 'referrals' | 'commission' | 'links'>('overview')

  useEffect(() => {
    // Check for success messages from URL params
    const urlParams = new URLSearchParams(window.location.search)
    const status = urlParams.get('status')

    if (status === 'withdrawal_submitted') {
      setSuccessMessage('Withdrawal request submitted successfully! You will be notified once it is processed.')
      setTimeout(() => setSuccessMessage(''), 5000)
    } else if (status === 'conversion_submitted') {
      setSuccessMessage('Commission conversion submitted successfully! Your shares will be allocated after approval.')
      setTimeout(() => setSuccessMessage(''), 5000)
    }
  }, [])

  useEffect(() => {
    const loadReferralData = async () => {
      if (!user?.telegram_profile?.id) return
      
      try {
        setLoading(true)
        
        // Get user's referral code (telegram_id)
        setReferralCode(user.telegram_profile.telegram_id.toString())
        
        // Get commission balance
        const balance = await getCommissionBalance(user.telegram_profile.id)
        setCommissionBalance(balance)
        
        // Get referrals
        const { data: referralData, error: referralError } = await supabase
          .from('referrals')
          .select(`
            referred_user_id,
            created_at,
            telegram_users!referred_user_id (
              id,
              telegram_id,
              username,
              first_name,
              last_name,
              country,
              created_at
            )
          `)
          .eq('referrer_user_id', user.telegram_profile.id)
        
        if (referralError) {
          console.error('Error loading referrals:', referralError)
        } else {
          // Transform the data and get additional stats
          const referralUsers = await Promise.all(
            (referralData || []).map(async (ref: any) => {
              const userData = ref.telegram_users
              
              // Get user's share purchases
              const { data: shareData } = await supabase
                .from('share_purchases')
                .select('shares_purchased, total_amount')
                .eq('user_id', userData.id)
              
              const totalShares = shareData?.reduce((sum, purchase) => sum + (purchase.shares_purchased || 0), 0) || 0
              const totalInvested = shareData?.reduce((sum, purchase) => sum + (purchase.total_amount || 0), 0) || 0
              
              return {
                ...userData,
                total_shares: totalShares,
                total_invested: totalInvested
              }
            })
          )
          
          setReferrals(referralUsers)
        }
        
        // Get commission transaction history
        const { data: commissionData, error: commissionError } = await supabase
          .from('commission_transactions')
          .select('*')
          .eq('user_id', user.telegram_profile.id)
          .order('created_at', { ascending: false })
          .limit(20)
        
        if (commissionError) {
          console.error('Error loading commission history:', commissionError)
        } else {
          setCommissionHistory(commissionData || [])
        }
        
      } catch (error) {
        console.error('Error loading referral data:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadReferralData()
  }, [user])

  const copyReferralLink = () => {
    const referralLink = `https://t.me/AureusAllianceBot?start=${referralCode}`
    navigator.clipboard.writeText(referralLink)
    // Show success message (you could add a toast notification here)
    alert('Referral link copied to clipboard!')
  }

  const shareReferralLink = () => {
    const referralLink = `https://t.me/AureusAllianceBot?start=${referralCode}`
    const shareText = `Join me in investing in gold mining shares with Aureus Alliance Holdings! Use my referral link: ${referralLink}`
    
    if (navigator.share) {
      navigator.share({
        title: 'Aureus Alliance Holdings - Gold Mining Investment',
        text: shareText,
        url: referralLink
      })
    } else {
      // Fallback to copying
      navigator.clipboard.writeText(shareText)
      alert('Referral message copied to clipboard!')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'referral_commission':
        return '💰'
      case 'withdrawal':
        return '📤'
      case 'conversion':
        return '🔄'
      default:
        return '💳'
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'referrals', label: 'My Referrals', icon: '👥' },
    { id: 'commission', label: 'Commission', icon: '💰' },
    { id: 'links', label: 'Share Links', icon: '🔗' }
  ]

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Referral System</h1>
          <p className="text-gray-600">
            Earn 15% commission on all investments made by your referrals
          </p>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="mb-6 p-4 bg-green-100 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <span className="text-green-600 text-xl mr-2">✅</span>
              <p className="text-green-800">{successMessage}</p>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && (
            <>
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <Card className="p-6 text-center">
                  <div className="text-3xl mb-2">👥</div>
                  <div className="text-2xl font-bold text-gray-800">
                    {referrals.length}
                  </div>
                  <div className="text-sm text-gray-600">Total Referrals</div>
                </Card>

                <Card className="p-6 text-center">
                  <div className="text-3xl mb-2">✅</div>
                  <div className="text-2xl font-bold text-green-600">
                    {referrals.filter(ref => (ref.total_shares || 0) > 0).length}
                  </div>
                  <div className="text-sm text-gray-600">Active Referrals</div>
                </Card>

                <Card className="p-6 text-center">
                  <div className="text-3xl mb-2">💰</div>
                  <div className="text-2xl font-bold text-green-600">
                    ${commissionBalance?.total_earned?.toFixed(2) || '0.00'}
                  </div>
                  <div className="text-sm text-gray-600">Total Commission</div>
                </Card>

                <Card className="p-6 text-center">
                  <div className="text-3xl mb-2">🎯</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {referrals.length > 0 ? ((referrals.filter(ref => (ref.total_shares || 0) > 0).length / referrals.length) * 100).toFixed(1) : '0.0'}%
                  </div>
                  <div className="text-sm text-gray-600">Conversion Rate</div>
                </Card>
              </div>

              {/* Overview Components */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <ReferralStats userId={user?.telegram_profile?.id} />
                <CommissionBalance userId={user?.telegram_profile?.id} />
              </div>

              <ReferralLinkGenerator referralCode={referralCode} />
            </>
          )}

          {activeTab === 'referrals' && (
            <ReferralList userId={user?.telegram_profile?.id} />
          )}

          {activeTab === 'commission' && (
            <div className="space-y-8">
              <CommissionBalance userId={user?.telegram_profile?.id} />

              {/* Commission History */}
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Commission History</h2>
                {commissionHistory.length > 0 ? (
                  <div className="space-y-3">
                    {commissionHistory.map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <span className="text-xl">{getTransactionIcon(transaction.transaction_type)}</span>
                          <div>
                            <div className="font-medium text-gray-800">
                              ${transaction.amount.toFixed(2)}
                            </div>
                            <div className="text-sm text-gray-600">
                              {transaction.description}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-green-600 font-medium">
                            {transaction.status === 'completed' ? 'Completed' : 'Pending'}
                          </div>
                          <div className="text-xs text-gray-500">
                            {formatDate(transaction.created_at)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-4">📊</div>
                    <p>No commission transactions yet</p>
                  </div>
                )}
              </Card>
            </div>
          )}

          {activeTab === 'links' && (
            <ReferralLinkGenerator referralCode={referralCode} />
          )}
        </div>

        {/* Help Section */}
        <Card className="mt-12 p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">How the Referral System Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-800 mb-3">💰 Commission Structure</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Earn 15% USDT commission on all referral investments</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Receive 15% share commission for additional passive income</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Commission paid instantly when referral investment is approved</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>No limits on referrals or commission earnings</span>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-800 mb-3">🚀 Best Practices</h3>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Share your personal investment experience and results</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Explain the gold mining opportunity clearly</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Be available to answer questions and provide support</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2 mt-0.5">•</span>
                  <span>Use multiple sharing channels for maximum reach</span>
                </li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </ProtectedRoute>
  )
}
