'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { getCommissionBalance, supabase } from '@/lib/supabase-client'
import { getRecommendedUSDTNetwork } from '@/lib/country-utils'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

const USDT_NETWORKS = [
  { code: 'ETH', name: 'Ethereum (ETH)', fee: 'High fees, secure' },
  { code: 'BSC', name: 'Binance Smart Chain (BSC)', fee: 'Low fees, fast' },
  { code: 'POLYGON', name: 'Polygon (MATIC)', fee: 'Very low fees, fast' },
  { code: 'TRON', name: 'TRON (TRX)', fee: 'Low fees, popular in Asia' }
]

export default function WithdrawCommissionPage() {
  const { user } = useAuth()
  const router = useRouter()
  
  const [availableBalance, setAvailableBalance] = useState(0)
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [walletAddress, setWalletAddress] = useState('')
  const [selectedNetwork, setSelectedNetwork] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [balanceLoading, setBalanceLoading] = useState(true)

  useEffect(() => {
    const loadBalance = async () => {
      if (!user?.telegram_profile?.id) return
      
      try {
        const balance = await getCommissionBalance(user.telegram_profile.id)
        setAvailableBalance(balance?.usdt_balance || 0)
        
        // Set recommended network based on country
        if (user?.telegram_profile?.country) {
          const recommendedNetwork = getRecommendedUSDTNetwork(user.telegram_profile.country)
          setSelectedNetwork(recommendedNetwork)
        }
      } catch (error) {
        console.error('Error loading balance:', error)
      } finally {
        setBalanceLoading(false)
      }
    }
    
    loadBalance()
  }, [user])

  const handleWithdraw = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }
    
    const amount = parseFloat(withdrawAmount)
    
    if (!amount || amount <= 0) {
      setError('Please enter a valid withdrawal amount')
      return
    }
    
    if (amount > availableBalance) {
      setError('Withdrawal amount exceeds available balance')
      return
    }
    
    if (amount < 10) {
      setError('Minimum withdrawal amount is $10 USDT')
      return
    }
    
    if (!walletAddress || !selectedNetwork) {
      setError('Please fill in all required fields')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      // Create withdrawal request
      const { error: withdrawalError } = await supabase
        .from('commission_withdrawal_requests')
        .insert({
          user_id: user.telegram_profile.id,
          withdrawal_amount: amount,
          wallet_address: walletAddress,
          network: selectedNetwork,
          currency: 'USDT',
          status: 'pending'
        })
      
      if (withdrawalError) {
        throw new Error(withdrawalError.message)
      }
      
      // Create commission transaction record
      const { error: transactionError } = await supabase
        .from('commission_transactions')
        .insert({
          user_id: user.telegram_profile.id,
          transaction_type: 'withdrawal',
          amount: -amount, // Negative for withdrawal
          description: `USDT withdrawal to ${selectedNetwork} network`,
          status: 'pending'
        })
      
      if (transactionError) {
        console.error('Error creating transaction record:', transactionError)
      }
      
      // Redirect to success page
      router.push('/dashboard/referrals?status=withdrawal_submitted')
    } catch (err) {
      console.error('Withdrawal error:', err)
      setError('Failed to submit withdrawal request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleMaxAmount = () => {
    setWithdrawAmount(availableBalance.toString())
  }

  if (balanceLoading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Withdraw Commission</h1>
            <p className="text-gray-600">Request a withdrawal of your USDT commission earnings</p>
          </div>

          {/* Balance Display */}
          <div className="bg-green-50 rounded-lg p-4 mb-6 border border-green-100">
            <h2 className="font-semibold text-green-800 mb-2">Available Balance</h2>
            <div className="text-3xl font-bold text-green-600 mb-1">
              ${availableBalance.toFixed(2)} USDT
            </div>
            <p className="text-green-700 text-sm">Available for withdrawal</p>
          </div>

          {availableBalance < 10 ? (
            <div className="bg-yellow-50 rounded-lg p-4 mb-6 border border-yellow-200">
              <h3 className="font-medium text-yellow-800 mb-2">Minimum Withdrawal Not Met</h3>
              <p className="text-yellow-700 text-sm">
                The minimum withdrawal amount is $10 USDT. You need ${(10 - availableBalance).toFixed(2)} more to make a withdrawal.
              </p>
              <div className="mt-3">
                <a
                  href="/dashboard/referrals"
                  className="text-yellow-800 hover:text-yellow-900 font-medium text-sm underline"
                >
                  ← Back to Referrals Dashboard
                </a>
              </div>
            </div>
          ) : (
            <form onSubmit={handleWithdraw} className="space-y-6">
              {/* Withdrawal Amount */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Withdrawal Amount (USDT) *
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={withdrawAmount}
                    onChange={(e) => setWithdrawAmount(e.target.value)}
                    placeholder="Enter amount to withdraw"
                    min="10"
                    max={availableBalance}
                    step="0.01"
                    required
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 pr-16"
                  />
                  <button
                    type="button"
                    onClick={handleMaxAmount}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
                  >
                    MAX
                  </button>
                </div>
                <p className="text-gray-500 text-sm mt-1">
                  Minimum: $10 USDT • Maximum: ${availableBalance.toFixed(2)} USDT
                </p>
              </div>

              {/* Network Selection */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  USDT Network *
                </label>
                <select
                  value={selectedNetwork}
                  onChange={(e) => setSelectedNetwork(e.target.value)}
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select network</option>
                  {USDT_NETWORKS.map(network => (
                    <option key={network.code} value={network.code}>
                      {network.name} - {network.fee}
                    </option>
                  ))}
                </select>
                <p className="text-gray-500 text-sm mt-1">
                  Choose the network that matches your wallet
                </p>
              </div>

              {/* Wallet Address */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Your USDT Wallet Address *
                </label>
                <input
                  type="text"
                  value={walletAddress}
                  onChange={(e) => setWalletAddress(e.target.value)}
                  placeholder="Enter your USDT wallet address"
                  required
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                />
                <p className="text-gray-500 text-sm mt-1">
                  ⚠️ Make sure this address supports USDT on the selected network
                </p>
              </div>

              {/* Fees Information */}
              {selectedNetwork && (
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                  <h3 className="font-medium text-blue-800 mb-2">Network Information</h3>
                  <div className="text-sm text-blue-700 space-y-1">
                    <div className="flex justify-between">
                      <span>Network:</span>
                      <span className="font-medium">
                        {USDT_NETWORKS.find(n => n.code === selectedNetwork)?.name}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Processing Time:</span>
                      <span className="font-medium">1-3 business days</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Network Fees:</span>
                      <span className="font-medium">Covered by Aureus Alliance</span>
                    </div>
                  </div>
                </div>
              )}

              {error && (
                <div className="p-3 bg-red-100 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => router.push('/dashboard/referrals')}
                  className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || !withdrawAmount || !walletAddress || !selectedNetwork}
                  className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
                    loading || !withdrawAmount || !walletAddress || !selectedNetwork
                      ? 'bg-blue-300 text-white cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  {loading ? 'Submitting...' : 'Submit Withdrawal Request'}
                </button>
              </div>
            </form>
          )}

          {/* Important Information */}
          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-800 mb-2">Important Information</h3>
            <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
              <li>Minimum withdrawal amount is $10 USDT</li>
              <li>Withdrawals are processed within 1-3 business days</li>
              <li>Network fees are covered by Aureus Alliance</li>
              <li>All withdrawals require admin approval</li>
              <li>You'll be notified once your withdrawal is processed</li>
              <li>Double-check your wallet address - incorrect addresses cannot be recovered</li>
            </ul>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
