'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getUserShares, supabase } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

interface SharePurchase {
  id: string
  package_name: string
  shares_purchased: number
  total_amount: number
  commission_used: number
  remaining_payment: number
  payment_method: string
  status: string
  created_at: string
  updated_at: string
}

export default function SharesPage() {
  const { user } = useAuth()
  const [shares, setShares] = useState<SharePurchase[]>([])
  const [loading, setLoading] = useState(true)
  const [totalShares, setTotalShares] = useState(0)
  const [totalInvested, setTotalInvested] = useState(0)

  useEffect(() => {
    const loadShares = async () => {
      if (!user?.telegram_profile?.id) return
      
      try {
        const userShares = await getUserShares(user.telegram_profile.id)
        setShares(userShares)
        
        // Calculate totals
        const total = userShares.reduce((sum, share) => sum + (share.shares_purchased || 0), 0)
        const invested = userShares.reduce((sum, share) => sum + (share.total_amount || 0), 0)
        
        setTotalShares(total)
        setTotalInvested(invested)
      } catch (error) {
        console.error('Error loading shares:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadShares()
  }, [user])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <ProtectedRoute requireTerms={true} requireCountry={true}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute requireTerms={true} requireCountry={true}>
      <div className="max-w-6xl mx-auto">
        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-xl p-6 mb-8 text-white">
          <h1 className="text-3xl font-bold mb-2">My Share Portfolio</h1>
          <p className="text-yellow-100">Your ownership in Aureus Alliance Holdings gold mining operations</p>
        </div>

        {/* Portfolio Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-yellow-500">
            <h2 className="text-lg font-semibold mb-2 text-gray-800">Total Shares</h2>
            <div className="text-3xl font-bold text-yellow-600 mb-1">{totalShares.toLocaleString()}</div>
            <p className="text-gray-600 text-sm">Shares owned</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500">
            <h2 className="text-lg font-semibold mb-2 text-gray-800">Total Invested</h2>
            <div className="text-3xl font-bold text-green-600 mb-1">${totalInvested.toFixed(2)}</div>
            <p className="text-gray-600 text-sm">USD invested</p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
            <h2 className="text-lg font-semibold mb-2 text-gray-800">Average Price</h2>
            <div className="text-3xl font-bold text-blue-600 mb-1">
              ${totalShares > 0 ? (totalInvested / totalShares).toFixed(2) : '0.00'}
            </div>
            <p className="text-gray-600 text-sm">Per share</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-6 flex flex-wrap gap-4">
          <a
            href="/dashboard/purchase"
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Purchase More Shares
          </a>
          <a
            href="/dashboard/payments"
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            View Payment History
          </a>
          <a
            href="/dashboard"
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Back to Dashboard
          </a>
        </div>

        {/* Shares List */}
        {shares.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-gray-400 text-6xl mb-4">📈</div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">No Shares Yet</h2>
            <p className="text-gray-600 mb-4">You haven't purchased any shares yet. Start building your gold mining portfolio today!</p>
            <a
              href="/dashboard/purchase"
              className="inline-block bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              Purchase Your First Shares
            </a>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800">Share Purchase History</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Package
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Shares
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Method
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {shares.map((share) => (
                    <tr key={share.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(share.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {share.package_name || 'Standard Package'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {share.shares_purchased.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${share.total_amount.toFixed(2)}
                        {share.commission_used > 0 && (
                          <div className="text-xs text-green-600">
                            Commission used: ${share.commission_used.toFixed(2)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {share.payment_method}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(share.status)}`}>
                          {share.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Share Information */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">About Your Shares</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Company Information</h4>
              <ul className="space-y-1">
                <li>• Company: AUREUS ALLIANCE HOLDINGS (PTY) LTD</li>
                <li>• Registration: 2025/368711/07</li>
                <li>• Country: South Africa</li>
                <li>• Business: Gold placer deposit mining</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Share Benefits</h4>
              <ul className="space-y-1">
                <li>• Dividend payments from mining profits</li>
                <li>• Ownership in physical gold mining operations</li>
                <li>• Digital share certificates</li>
                <li>• Transparent mining operations reporting</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Mining Operations</h4>
              <ul className="space-y-1">
                <li>• Location: 250 hectares of gold-rich land</li>
                <li>• Method: Gold placer deposit mining</li>
                <li>• Capacity: 200 TPH per plant</li>
                <li>• Schedule: 20 hours/day, 330 days/year</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Contact & Support</h4>
              <ul className="space-y-1">
                <li>• Telegram: @AureusAllianceBot</li>
                <li>• Website: aureus.africa</li>
                <li>• Email: Available through Telegram</li>
                <li>• Support: 24/7 via Telegram bot</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
