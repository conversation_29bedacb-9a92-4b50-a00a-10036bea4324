import '../aureus-design-system.css'
import { Inter } from 'next/font/google'
import { Metadata } from 'next'
import { SiteContentProvider } from '@/contexts/SiteContentContext'
import { AuthProvider } from '@/contexts/AuthContext'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { ToastProvider } from '@/components/ui/Toast'
import { ErrorBoundary } from '@/components/ui/ErrorBoundary'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Aureus Alliance Holdings',
  description: 'Gold placer deposit mining with blockchain-backed ownership',
  keywords: 'gold mining, placer deposits, blockchain, share ownership, South Africa',
  authors: [{ name: 'Aureus Alliance Holdings' }],
  creator: 'Aureus Alliance Holdings',
  publisher: 'Aureus Alliance Holdings',
  openGraph: {
    title: 'Aureus Alliance Holdings',
    description: 'Gold placer deposit mining with blockchain-backed ownership',
    url: 'https://aureus.africa',
    siteName: 'Aureus Alliance Holdings',
    locale: 'en_US',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ErrorBoundary>
          <ThemeProvider>
            <ToastProvider>
              <AuthProvider>
                <SiteContentProvider>
                  {children}
                </SiteContentProvider>
              </AuthProvider>
            </ToastProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
