'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import TelegramLoginButton from '@/components/auth/TelegramLoginButton'
import { EnhancedCard } from '@/components/ui/EnhancedCard'

export default function LoginPage() {
  const router = useRouter()
  const { user, loading } = useAuth()
  const [authError, setAuthError] = useState<string | null>(null)

  useEffect(() => {
    // Redirect if already logged in
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const handleAuthSuccess = () => {
    router.push('/dashboard')
  }

  const handleAuthError = (error: string) => {
    setAuthError(error)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Checking authentication...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Branding */}
        <div className="text-center">
          <Link href="/" className="inline-flex items-center space-x-3 mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">A</span>
            </div>
            <div className="text-left">
              <h1 className="text-2xl font-bold text-white">Aureus Alliance</h1>
              <p className="text-yellow-400 text-sm">Holdings</p>
            </div>
          </Link>
        </div>

        {/* Login Card */}
        <EnhancedCard variant="glass" className="p-8 border border-white/20">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-2">
              Welcome Back
            </h2>
            <p className="text-gray-300">
              Sign in to access your gold mining investment dashboard
            </p>
          </div>

          {/* Login Form */}
          <div className="space-y-6">
            <TelegramLoginButton
              botName="AureusAllianceBot"
              buttonSize="large"
              cornerRadius={8}
              showUserPic={true}
              onAuthCallback={handleAuthSuccess}
              onAuthError={handleAuthError}
              className="flex justify-center my-4"
            />

            {authError && (
              <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 text-red-300 rounded-md text-sm text-center">
                {authError}
              </div>
            )}

            {/* Benefits */}
            <div className="mt-8 pt-6 border-t border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4 text-center">
                Why Choose Aureus Alliance?
              </h3>
              <div className="space-y-3">
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                  <span className="text-sm">Secure gold mining investments</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                  <span className="text-sm">Real-time portfolio tracking</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                  <span className="text-sm">Transparent mining operations</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                  <span className="text-sm">Professional support team</span>
                </div>
              </div>
            </div>

            {/* Security Notice */}
            <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4 mt-6">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                <div>
                  <h4 className="text-blue-400 font-medium text-sm">Secure Authentication</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    We use Telegram's secure OAuth system to protect your account and ensure safe access to your investments.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8 text-center text-sm text-gray-300">
              <p>Don't have an account? Start by messaging our</p>
              <a
                href="https://t.me/AureusAllianceBot"
                target="_blank"
                rel="noopener noreferrer"
                className="text-yellow-400 hover:text-yellow-300 font-medium"
              >
                Telegram Bot @AureusAllianceBot
              </a>
            </div>
          </div>
        </EnhancedCard>

        {/* Footer Links */}
        <div className="text-center space-y-4">
          <div className="flex justify-center space-x-6 text-sm">
            <Link href="/#about" className="text-gray-400 hover:text-yellow-400 transition-colors">
              About Us
            </Link>
            <Link href="/#contact" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Contact
            </Link>
            <Link href="/privacy" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Terms
            </Link>
          </div>
          <p className="text-gray-500 text-xs">
            © 2024 Aureus Alliance Holdings. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}
