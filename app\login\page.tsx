'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import TelegramLoginButton from '@/components/auth/TelegramLoginButton'

export default function LoginPage() {
  const router = useRouter()
  const { user, loading } = useAuth()
  const [authError, setAuthError] = useState<string | null>(null)

  useEffect(() => {
    // Redirect if already logged in
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const handleAuthSuccess = () => {
    router.push('/dashboard')
  }

  const handleAuthError = (error: string) => {
    setAuthError(error)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Checking authentication...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full">
        <div className="text-center mb-8">
          <Image
            src="/logo.png"
            alt="Aureus Alliance Logo"
            width={120}
            height={120}
            className="mx-auto mb-4"
          />
          <h1 className="text-2xl font-bold text-gray-800">Aureus Alliance Dashboard</h1>
          <p className="text-gray-600 mt-2">Sign in with your Telegram account to access your dashboard</p>
        </div>

        <TelegramLoginButton
          botName="AureusAllianceBot"
          buttonSize="large"
          cornerRadius={8}
          showUserPic={true}
          onAuthCallback={handleAuthSuccess}
          onAuthError={handleAuthError}
          className="flex justify-center my-4"
        />

        {authError && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md text-sm text-center">
            {authError}
          </div>
        )}

        <div className="mt-8 text-center text-sm text-gray-600">
          <p>Don't have an account? Start by messaging our</p>
          <a
            href="https://t.me/AureusAllianceBot"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Telegram Bot @AureusAllianceBot
          </a>
        </div>
      </div>
    </div>
  )
}
