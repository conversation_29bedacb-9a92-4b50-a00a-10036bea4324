'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { CheckCircleIcon, SparklesIcon, ArrowRightIcon } from '@heroicons/react/24/solid'

export default function OnboardingCompletePage() {
  const { user, checkOnboardingStatus } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [onboardingStatus, setOnboardingStatus] = useState({
    hasAcceptedTerms: false,
    hasSelectedCountry: false,
    hasCompletedKYC: false
  })

  useEffect(() => {
    const verifyCompletion = async () => {
      if (!user) {
        router.push('/login')
        return
      }

      try {
        const status = await checkOnboardingStatus()
        setOnboardingStatus(status)

        // If onboarding is not complete, redirect to appropriate step
        if (!status.hasAcceptedTerms) {
          router.push('/onboarding/terms')
          return
        }
        if (!status.hasSelectedCountry) {
          router.push('/onboarding/country')
          return
        }
        if (!status.hasCompletedKYC) {
          router.push('/onboarding/kyc')
          return
        }

        // All steps completed, show success page
        setLoading(false)
      } catch (error) {
        console.error('Error verifying onboarding completion:', error)
        router.push('/onboarding/terms')
      }
    }

    verifyCompletion()
  }, [user, router, checkOnboardingStatus])

  const handleContinueToDashboard = () => {
    router.push('/dashboard')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Verifying completion...</p>
        </div>
      </div>
    )
  }

  const completedSteps = [
    {
      title: 'Terms & Conditions Accepted',
      description: 'You have agreed to our terms of service',
      completed: onboardingStatus.hasAcceptedTerms
    },
    {
      title: 'Country Selected',
      description: `Country: ${user?.telegram_profile?.country || 'Not specified'}`,
      completed: onboardingStatus.hasSelectedCountry
    },
    {
      title: 'KYC Verification Submitted',
      description: 'Your identity verification is under review',
      completed: onboardingStatus.hasCompletedKYC
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Success Animation */}
          <div className="text-center mb-8">
            <div className="relative">
              <div className="mx-auto w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mb-6 animate-pulse">
                <CheckCircleIcon className="w-12 h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2">
                <SparklesIcon className="w-8 h-8 text-yellow-400 animate-bounce" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-white mb-4">
              Welcome to Aureus Alliance!
            </h1>
            <p className="text-xl text-gray-300 mb-2">
              Your account setup is complete
            </p>
            <p className="text-gray-400">
              You're now ready to start your gold mining investment journey
            </p>
          </div>

          {/* Completion Summary */}
          <div className="bg-white rounded-lg shadow-xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
              Setup Summary
            </h2>
            
            <div className="space-y-4 mb-8">
              {completedSteps.map((step, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">
                    <CheckCircleIcon className="w-6 h-6 text-green-500" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-800">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* User Profile Summary */}
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="font-semibold text-gray-800 mb-4">Your Profile</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>
                  <span className="ml-2 text-gray-800">
                    {user?.telegram_profile?.first_name} {user?.telegram_profile?.last_name}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Username:</span>
                  <span className="ml-2 text-gray-800">
                    @{user?.telegram_profile?.username || 'Not set'}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Country:</span>
                  <span className="ml-2 text-gray-800">
                    {user?.telegram_profile?.country || 'Not specified'}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">KYC Status:</span>
                  <span className="ml-2 text-yellow-600 font-medium">
                    Under Review
                  </span>
                </div>
              </div>
            </div>

            {/* Next Steps */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <h3 className="font-semibold text-blue-800 mb-3">What's Next?</h3>
              <ul className="space-y-2 text-sm text-blue-700">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Your KYC verification will be reviewed within 24-48 hours
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  You'll receive a notification once approved
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  You can start exploring your dashboard and investment options
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  Access the referral system to earn commissions
                </li>
              </ul>
            </div>

            {/* Action Button */}
            <div className="text-center">
              <button
                onClick={handleContinueToDashboard}
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-lg shadow-lg transform transition-all duration-200 hover:scale-105"
              >
                <span>Continue to Dashboard</span>
                <ArrowRightIcon className="w-5 h-5 ml-2" />
              </button>
            </div>
          </div>

          {/* Support Information */}
          <div className="text-center text-gray-300">
            <p className="text-sm mb-2">
              Need help? Contact our support team
            </p>
            <a
              href="https://t.me/AureusAllianceBot"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 text-sm underline"
            >
              @AureusAllianceBot on Telegram
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
