'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import { COUNTRIES, getCountryInstructions, getAvailablePaymentMethods } from '@/lib/country-utils'

export default function CountrySelectionPage() {
  const { user, refreshUser } = useAuth()
  const router = useRouter()
  const [selectedCountry, setSelectedCountry] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleCountrySelection = async () => {
    if (!selectedCountry) {
      setError('Please select your country')
      return
    }

    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Update user's country in telegram_users table
      const { error: updateError } = await supabase
        .from('telegram_users')
        .update({ country: selectedCountry })
        .eq('id', user.telegram_profile.id)

      if (updateError) {
        throw new Error(updateError.message)
      }

      // Refresh user data
      await refreshUser()

      // Redirect to KYC step
      router.push('/onboarding/kyc')
    } catch (err) {
      console.error('Error updating country:', err)
      setError('Failed to save country selection. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-800">Select Your Country</h1>
        <p className="text-gray-600 mt-2">This helps us provide you with the appropriate payment options</p>
      </div>

          <div className="mb-6">
            <label className="block text-gray-700 font-medium mb-2">
              Country of Residence
            </label>
            <select
              value={selectedCountry}
              onChange={(e) => setSelectedCountry(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select your country</option>
              {COUNTRIES.map((country) => (
                <option key={country.code} value={country.code}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>

          {selectedCountry && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Payment Options Available</h3>
              <p className="text-sm text-blue-700 mb-2">{getCountryInstructions(selectedCountry)}</p>
              <div className="flex flex-wrap gap-2 mt-2">
                {getAvailablePaymentMethods(selectedCountry).map(method => (
                  <span key={method} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                    {method}
                  </span>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div className="mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}

      <div className="flex justify-between">
        <button
          onClick={() => router.push('/onboarding/terms')}
          className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
        >
          Back
        </button>
        <button
          onClick={handleCountrySelection}
          disabled={loading || !selectedCountry}
          className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
            loading || !selectedCountry
              ? 'bg-blue-300 text-white cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Processing...' : 'Continue to KYC'}
        </button>
      </div>
    </div>
  )
}
