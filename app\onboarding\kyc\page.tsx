'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import { requiresEnhancedKYC } from '@/lib/country-utils'

export default function KYCPage() {
  const { user, refreshUser } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    firstName: user?.telegram_profile?.first_name || '',
    lastName: user?.telegram_profile?.last_name || '',
    idType: 'national_id',
    idNumber: '',
    phoneNumber: '',
    emailAddress: '',
    address: '',
    city: '',
    postalCode: '',
    acceptedPrivacy: false
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.acceptedPrivacy) {
      setError('You must accept the privacy policy to continue')
      return
    }

    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Create KYC submission using the correct table name
      const { error: kycError } = await supabase
        .from('kyc_information')
        .insert({
          user_id: user.telegram_profile.id,
          first_name: formData.firstName,
          last_name: formData.lastName,
          id_type: formData.idType,
          id_number_encrypted: formData.idNumber, // In production, this should be properly encrypted
          id_number_hash: formData.idNumber, // In production, this should be a proper hash
          phone_number: formData.phoneNumber,
          email_address: formData.emailAddress,
          street_address: formData.address,
          city: formData.city,
          postal_code: formData.postalCode,
          country_code: user.telegram_profile.country || 'ZA',
          country_name: user.telegram_profile.country || 'South Africa',
          data_consent_given: formData.acceptedPrivacy,
          privacy_policy_accepted: formData.acceptedPrivacy,
          kyc_status: 'pending',
          created_by_telegram_id: user.telegram_profile.telegram_id
        })

      if (kycError) {
        throw new Error(kycError.message)
      }

      // Refresh user data
      await refreshUser()

      // Redirect to dashboard
      router.push('/dashboard')
    } catch (err) {
      console.error('Error submitting KYC:', err)
      setError('Failed to submit KYC information. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Check if enhanced KYC is required based on country
  const needsEnhancedKYC = user?.telegram_profile?.country 
    ? requiresEnhancedKYC(user.telegram_profile.country) 
    : false

  return (
    <div className="p-8">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-800">KYC Verification</h1>
        <p className="text-gray-600 mt-2">Please provide your information for verification</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Personal Information */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Personal Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                First Name
              </label>
              <input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                Last Name
              </label>
              <input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                ID Type
              </label>
              <select
                name="idType"
                value={formData.idType}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="national_id">National ID</option>
                <option value="passport">Passport</option>
                <option value="drivers_license">Driver's License</option>
              </select>
            </div>
            
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                ID Number
              </label>
              <input
                type="text"
                name="idNumber"
                value={formData.idNumber}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
        
        {/* Contact Information */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h2 className="text-lg font-medium text-gray-800 mb-4">Contact Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-700 text-sm font-medium mb-1">
                Email Address
              </label>
              <input
                type="email"
                name="emailAddress"
                value={formData.emailAddress}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
        
        {/* Address (only for enhanced KYC) */}
        {needsEnhancedKYC && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">Address Information</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-700 text-sm font-medium mb-1">
                  Address
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  required
                  className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                  rows={2}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-700 text-sm font-medium mb-1">
                    City
                  </label>
                  <input
                    type="text"
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    required
                    className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-700 text-sm font-medium mb-1">
                    Postal Code
                  </label>
                  <input
                    type="text"
                    name="postalCode"
                    value={formData.postalCode}
                    onChange={handleChange}
                    required
                    className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Privacy Policy */}
        <div className="mb-6">
          <label className="flex items-start cursor-pointer">
            <input 
              type="checkbox" 
              name="acceptedPrivacy"
              checked={formData.acceptedPrivacy}
              onChange={handleChange}
              className="mt-1 h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500" 
            />
            <span className="ml-2 text-gray-700 text-sm">
              I consent to the collection and processing of my personal information in accordance with 
              Aureus Alliance Holdings' Privacy Policy and applicable data protection laws.
            </span>
          </label>
        </div>

        {error && (
          <div className="mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}

        <div className="flex justify-between">
          <button
            type="button"
            onClick={() => router.push('/onboarding/country')}
            className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
          >
            Back
          </button>
          <button
            type="submit"
            disabled={loading || !formData.acceptedPrivacy}
            className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
              loading || !formData.acceptedPrivacy
                ? 'bg-blue-300 text-white cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {loading ? 'Processing...' : 'Submit & Continue'}
          </button>
        </div>
      </form>
    </div>
  )
}
