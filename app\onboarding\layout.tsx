'use client'

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import OnboardingProgress from '@/components/onboarding/OnboardingProgress'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, checkOnboardingStatus } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [onboardingStatus, setOnboardingStatus] = useState({
    hasAcceptedTerms: false,
    hasSelectedCountry: false,
    hasCompletedKYC: false
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkStatus = async () => {
      if (!user) return
      
      try {
        const status = await checkOnboardingStatus()
        setOnboardingStatus(status)
        
        // Redirect logic based on current status and path
        if (status.hasAcceptedTerms && status.hasSelectedCountry && status.hasCompletedKYC) {
          // User has completed full onboarding, redirect to dashboard
          router.push('/dashboard')
          return
        }

        // Redirect to appropriate step if user is on wrong page
        if (!status.hasAcceptedTerms && pathname !== '/onboarding/terms') {
          router.push('/onboarding/terms')
          return
        }

        if (status.hasAcceptedTerms && !status.hasSelectedCountry && pathname !== '/onboarding/country') {
          router.push('/onboarding/country')
          return
        }

        if (status.hasAcceptedTerms && status.hasSelectedCountry && !status.hasCompletedKYC && pathname !== '/onboarding/kyc') {
          router.push('/onboarding/kyc')
          return
        }
        
      } catch (error) {
        console.error('Error checking onboarding status:', error)
      } finally {
        setLoading(false)
      }
    }
    
    checkStatus()
  }, [user, pathname, router, checkOnboardingStatus])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading onboarding...</p>
        </div>
      </div>
    )
  }

  // Define onboarding steps
  const steps = [
    {
      id: 'terms',
      title: 'Accept Terms & Conditions',
      description: 'Review and accept our terms of service',
      completed: onboardingStatus.hasAcceptedTerms,
      current: pathname === '/onboarding/terms'
    },
    {
      id: 'country',
      title: 'Select Country',
      description: 'Choose your country of residence',
      completed: onboardingStatus.hasSelectedCountry,
      current: pathname === '/onboarding/country'
    },
    {
      id: 'kyc',
      title: 'KYC Verification',
      description: 'Provide your identification details',
      completed: onboardingStatus.hasCompletedKYC,
      current: pathname === '/onboarding/kyc'
    },
    {
      id: 'complete',
      title: 'Setup Complete',
      description: 'Access your dashboard',
      completed: onboardingStatus.hasAcceptedTerms &&
                onboardingStatus.hasSelectedCountry &&
                onboardingStatus.hasCompletedKYC,
      current: false
    }
  ]

  const currentStepIndex = steps.findIndex(step => step.current)
  const completedSteps = steps.filter(step => step.completed).length

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">Welcome to Aureus Alliance</h1>
              <p className="text-gray-300">Let's get your account set up in just a few steps</p>
            </div>

            {/* Progress Component */}
            <OnboardingProgress 
              steps={steps}
              currentStep={completedSteps}
              totalSteps={steps.length}
            />

            {/* Main Content */}
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              {children}
            </div>

            {/* Help Text */}
            <div className="text-center mt-6">
              <p className="text-gray-300 text-sm">
                Need help? Contact us at{' '}
                <a 
                  href="https://t.me/AureusAllianceBot" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-yellow-400 hover:text-yellow-300 underline"
                >
                  @AureusAllianceBot
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
