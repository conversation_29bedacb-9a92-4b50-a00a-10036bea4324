'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'
import ProtectedRoute from '@/components/auth/ProtectedRoute'

export default function TermsPage() {
  const { user, refreshUser } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [accepted, setAccepted] = useState(false)

  const handleAcceptTerms = async () => {
    if (!accepted) {
      setError('You must accept the terms and conditions to continue')
      return
    }

    if (!user?.telegram_profile?.id) {
      setError('User profile not found')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Insert terms acceptance record
      const { error: termsError } = await supabase
        .from('terms_acceptance')
        .insert({
          user_id: user.telegram_profile.id,
          terms_type: 'general',
          version: '1.0',
          accepted_at: new Date().toISOString()
        })

      if (termsError) {
        throw new Error(termsError.message)
      }

      // Refresh user data
      await refreshUser()

      // Redirect to next step
      router.push('/onboarding/country')
    } catch (err) {
      console.error('Error accepting terms:', err)
      setError('Failed to save terms acceptance. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-gray-800">Terms and Conditions</h1>
        <p className="text-gray-600 mt-2">Please review and accept our terms to continue</p>
      </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6 h-96 overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">AUREUS ALLIANCE HOLDINGS (PTY) LTD</h2>
            <h3 className="text-lg font-medium mb-2">Terms and Conditions</h3>
            
            <p className="mb-4">
              These Terms and Conditions ("Terms") govern your use of the Aureus Alliance Holdings (Pty) Ltd ("Aureus Alliance") 
              platform, website, and services. By accessing or using our services, you agree to be bound by these Terms.
            </p>
            
            <h4 className="font-medium mb-2">1. Share Ownership</h4>
            <p className="mb-4">
              When you purchase shares through Aureus Alliance Holdings, you are acquiring ownership shares in a South African 
              registered company that owns and operates gold placer deposit mining operations. These shares represent partial 
              ownership in the company and entitle you to dividends as declared by the company.
            </p>
            
            <h4 className="font-medium mb-2">2. Risk Disclosure</h4>
            <p className="mb-4">
              Mining operations involve inherent risks, including but not limited to operational challenges, regulatory changes, 
              market fluctuations, and environmental factors. The value of shares may fluctuate, and past performance is not 
              indicative of future results. You should only purchase shares with funds you can afford to risk.
            </p>
            
            <h4 className="font-medium mb-2">3. Compliance with Laws</h4>
            <p className="mb-4">
              Aureus Alliance operates under South African law. By using our services, you agree to comply with all applicable 
              laws and regulations in your jurisdiction. You represent that your purchase of shares does not violate any laws 
              in your country of residence.
            </p>
            
            <h4 className="font-medium mb-2">4. KYC Requirements</h4>
            <p className="mb-4">
              To comply with regulatory requirements, Aureus Alliance implements Know Your Customer (KYC) procedures. You agree 
              to provide accurate and complete information as requested and to update this information if it changes.
            </p>
            
            <h4 className="font-medium mb-2">5. Privacy Policy</h4>
            <p className="mb-4">
              Your privacy is important to us. Our collection and use of your personal information is governed by our Privacy 
              Policy, which is incorporated into these Terms by reference.
            </p>
            
            <h4 className="font-medium mb-2">6. Limitation of Liability</h4>
            <p className="mb-4">
              To the maximum extent permitted by law, Aureus Alliance shall not be liable for any indirect, incidental, special, 
              consequential, or punitive damages, including loss of profits, data, or goodwill, arising from or in connection 
              with your use of our services.
            </p>
            
            <h4 className="font-medium mb-2">7. Amendments</h4>
            <p className="mb-4">
              Aureus Alliance reserves the right to modify these Terms at any time. We will provide notice of significant changes. 
              Your continued use of our services after such modifications constitutes your acceptance of the updated Terms.
            </p>
            
            <h4 className="font-medium mb-2">8. Governing Law</h4>
            <p className="mb-4">
              These Terms shall be governed by and construed in accordance with the laws of South Africa, without regard to its 
              conflict of law provisions.
            </p>
          </div>

          <div className="mb-6">
            <label className="flex items-start cursor-pointer">
              <input 
                type="checkbox" 
                className="mt-1 h-5 w-5 text-blue-600 rounded border-gray-300 focus:ring-blue-500" 
                checked={accepted}
                onChange={(e) => setAccepted(e.target.checked)}
              />
              <span className="ml-2 text-gray-700">
                I have read, understood, and agree to the Terms and Conditions of Aureus Alliance Holdings (Pty) Ltd
              </span>
            </label>
          </div>

          {error && (
            <div className="mb-6 p-3 bg-red-100 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}

      <div className="flex justify-between">
        <button
          onClick={() => router.push('/login')}
          className="px-6 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 rounded-lg transition-colors duration-200"
        >
          Cancel
        </button>
        <button
          onClick={handleAcceptTerms}
          disabled={loading || !accepted}
          className={`px-6 py-2 rounded-lg transition-colors duration-200 ${
            loading || !accepted
              ? 'bg-blue-300 text-white cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {loading ? 'Processing...' : 'Accept & Continue'}
        </button>
      </div>
    </div>
  )
}
