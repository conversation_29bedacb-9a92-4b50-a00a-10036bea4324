'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import TelegramLoginButton from '@/components/auth/TelegramLoginButton'
import { EnhancedCard } from '@/components/ui/EnhancedCard'

export default function RegisterPage() {
  const router = useRouter()
  const { user, loading } = useAuth()
  const [authError, setAuthError] = useState<string | null>(null)

  useEffect(() => {
    // Redirect if already logged in
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  const handleAuthSuccess = () => {
    router.push('/onboarding')
  }

  const handleAuthError = (error: string) => {
    setAuthError(error)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Checking authentication...</p>
        </div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Branding */}
        <div className="text-center">
          <Link href="/" className="inline-flex items-center space-x-3 mb-8">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-2xl">A</span>
            </div>
            <div className="text-left">
              <h1 className="text-2xl font-bold text-white">Aureus Alliance</h1>
              <p className="text-yellow-400 text-sm">Holdings</p>
            </div>
          </Link>
        </div>

        {/* Registration Card */}
        <EnhancedCard variant="glass" className="p-8 border border-white/20">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-2">
              Join Aureus Alliance
            </h2>
            <p className="text-gray-300">
              Create your account to start investing in gold mining shares
            </p>
          </div>

          {/* Registration Form */}
          <div className="space-y-6">
            <TelegramLoginButton
              botName="AureusAllianceBot"
              buttonSize="large"
              cornerRadius={8}
              showUserPic={true}
              onAuthCallback={handleAuthSuccess}
              onAuthError={handleAuthError}
              className="flex justify-center my-4"
            />

            {authError && (
              <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 text-red-300 rounded-md text-sm text-center">
                {authError}
              </div>
            )}
            
            {/* Getting Started Steps */}
            <div className="mt-8 pt-6 border-t border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4 text-center">
                Getting Started is Easy
              </h3>
              <div className="space-y-4">
                <div className="flex items-start text-gray-300">
                  <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <span className="text-black text-xs font-bold">1</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-white">Sign up with Telegram</span>
                    <p className="text-xs text-gray-400 mt-1">Secure authentication using your Telegram account</p>
                  </div>
                </div>
                <div className="flex items-start text-gray-300">
                  <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <span className="text-black text-xs font-bold">2</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-white">Complete onboarding</span>
                    <p className="text-xs text-gray-400 mt-1">Provide basic information and verify your identity</p>
                  </div>
                </div>
                <div className="flex items-start text-gray-300">
                  <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <span className="text-black text-xs font-bold">3</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-white">Start investing</span>
                    <p className="text-xs text-gray-400 mt-1">Purchase gold mining shares and track your portfolio</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Investment Benefits */}
            <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4 mt-6">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <div>
                  <h4 className="text-blue-400 font-medium text-sm">Start with $5 per Share</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    Begin your gold mining investment journey with our affordable presale pricing and transparent profit sharing.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8 text-center text-sm text-gray-300">
              <p>Already have an account? 
                <Link href="/login" className="text-yellow-400 hover:text-yellow-300 font-medium ml-1">
                  Sign in here
                </Link>
              </p>
            </div>

            <div className="mt-4 text-center text-sm text-gray-400">
              <p>Need help getting started?</p>
              <a
                href="https://t.me/AureusAllianceBot"
                target="_blank"
                rel="noopener noreferrer"
                className="text-yellow-400 hover:text-yellow-300 font-medium"
              >
                Message our Telegram Bot @AureusAllianceBot
              </a>
            </div>
          </div>
        </EnhancedCard>

        {/* Footer Links */}
        <div className="text-center space-y-4">
          <div className="flex justify-center space-x-6 text-sm">
            <Link href="/#about" className="text-gray-400 hover:text-yellow-400 transition-colors">
              About Us
            </Link>
            <Link href="/#contact" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Contact
            </Link>
            <Link href="/privacy" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Privacy
            </Link>
            <Link href="/terms" className="text-gray-400 hover:text-yellow-400 transition-colors">
              Terms
            </Link>
          </div>
          <p className="text-gray-500 text-xs">
            © 2024 Aureus Alliance Holdings. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}
