-- Check User 88's share purchases to understand the pending shares
SELECT 
    id,
    user_id,
    shares_purchased,
    status,
    payment_type,
    created_at,
    updated_at
FROM aureus_share_purchases 
WHERE user_id = 88 
ORDER BY created_at DESC;

-- Also check if there are any pending commission conversions
SELECT 
    id,
    user_id,
    shares_requested,
    status,
    created_at
FROM commission_conversions 
WHERE user_id = 88 
ORDER BY created_at DESC;
