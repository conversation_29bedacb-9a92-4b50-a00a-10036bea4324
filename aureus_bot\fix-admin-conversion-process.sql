-- Fix Admin Commission Conversion Process
-- This script ensures admins can process commission conversions from escrowed funds
-- The escrow system is working correctly - we just need to remove the blocking constraint

-- Step 1: Remove the problematic constraint that blocks legitimate admin operations
ALTER TABLE commission_balances 
DROP CONSTRAINT IF EXISTS check_sufficient_balance;

-- Step 2: Add a better constraint that allows proper escrow operations
-- This constraint ensures balances are non-negative but doesn't block escrow processing
DO $$
BEGIN
    ALTER TABLE commission_balances 
    ADD CONSTRAINT check_positive_balances 
    CHECK (
        usdt_balance >= 0 AND 
        share_balance >= 0 AND 
        total_earned_usdt >= 0 AND 
        total_earned_shares >= 0 AND
        escrowed_amount >= 0 AND
        total_withdrawn >= 0
    );
EXCEPTION
    WHEN duplicate_object THEN
        RAISE NOTICE 'Constraint check_positive_balances already exists';
END $$;

-- Step 3: Ensure proper column structure
ALTER TABLE commission_balances 
ADD COLUMN IF NOT EXISTS escrowed_amount DECIMAL(15,2) DEFAULT 0.00;

ALTER TABLE commission_balances 
ADD COLUMN IF NOT EXISTS total_withdrawn DECIMAL(15,2) DEFAULT 0.00;

-- Step 4: Update any NULL values to prevent issues
UPDATE commission_balances 
SET 
    usdt_balance = COALESCE(usdt_balance, 0.00),
    share_balance = COALESCE(share_balance, 0.00),
    total_earned_usdt = COALESCE(total_earned_usdt, 0.00),
    total_earned_shares = COALESCE(total_earned_shares, 0.00),
    escrowed_amount = COALESCE(escrowed_amount, 0.00),
    total_withdrawn = COALESCE(total_withdrawn, 0.00);

-- Step 5: Create or replace the commission conversion processing function
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_conversion_id UUID,
    p_admin_id BIGINT,
    p_admin_username TEXT
) RETURNS VOID AS $$
DECLARE
    v_conversion_record RECORD;
    v_balance_record RECORD;
    v_required_amount DECIMAL(15,2);
    v_available_amount DECIMAL(15,2);
    v_escrowed_amount DECIMAL(15,2);
BEGIN
    -- Get conversion details
    SELECT * INTO v_conversion_record
    FROM commission_conversions
    WHERE id = p_conversion_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Conversion request not found or already processed';
    END IF;
    
    -- Get user's commission balance
    SELECT * INTO v_balance_record
    FROM commission_balances
    WHERE user_id = v_conversion_record.user_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'User commission balance not found';
    END IF;
    
    v_required_amount := v_conversion_record.usdt_amount;
    v_escrowed_amount := COALESCE(v_balance_record.escrowed_amount, 0);
    v_available_amount := COALESCE(v_balance_record.usdt_balance, 0) - v_escrowed_amount;
    
    -- Check if sufficient funds (either available or escrowed)
    IF v_escrowed_amount < v_required_amount AND v_available_amount < v_required_amount THEN
        RAISE EXCEPTION 'Insufficient funds: Required %, Available %, Escrowed %', 
            v_required_amount, v_available_amount, v_escrowed_amount;
    END IF;
    
    -- Update conversion status to approved
    UPDATE commission_conversions
    SET 
        status = 'approved',
        approved_by_admin_id = p_admin_id,
        approved_at = NOW(),
        updated_at = NOW()
    WHERE id = p_conversion_id;
    
    -- Update commission balance (deduct USDT, add shares, clear escrow)
    UPDATE commission_balances
    SET 
        usdt_balance = usdt_balance - v_required_amount,
        share_balance = share_balance + v_conversion_record.shares_requested,
        total_earned_shares = total_earned_shares + v_conversion_record.shares_requested,
        escrowed_amount = 0, -- CRITICAL: Clear escrow after approval
        last_updated = NOW()
    WHERE user_id = v_conversion_record.user_id;
    
    -- Update shares sold in the investment phase
    UPDATE investment_phases
    SET 
        shares_sold = shares_sold + v_conversion_record.shares_requested,
        updated_at = NOW()
    WHERE id = v_conversion_record.phase_id;
    
    -- Log the admin action (if table exists)
    BEGIN
        INSERT INTO admin_audit_logs (
            admin_id,
            admin_username,
            action_type,
            target_table,
            target_id,
            action_details,
            created_at
        ) VALUES (
            p_admin_id,
            p_admin_username,
            'commission_conversion_approval',
            'commission_conversions',
            p_conversion_id,
            jsonb_build_object(
                'user_id', v_conversion_record.user_id,
                'usdt_amount', v_required_amount,
                'shares_requested', v_conversion_record.shares_requested,
                'escrow_cleared', v_escrowed_amount
            ),
            NOW()
        );
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip logging
            NULL;
    END;
    
END;
$$ LANGUAGE plpgsql;

-- Step 6: Fix any existing stuck escrow amounts (like User 88)
UPDATE commission_balances 
SET escrowed_amount = 0, last_updated = NOW()
WHERE user_id IN (
    SELECT DISTINCT cc.user_id 
    FROM commission_conversions cc
    WHERE cc.status = 'approved' 
    AND cc.approved_at IS NOT NULL
    AND EXISTS (
        SELECT 1 FROM commission_balances cb 
        WHERE cb.user_id = cc.user_id 
        AND cb.escrowed_amount > 0
    )
);

-- Step 7: Show status after fixes
SELECT 
    'FIXED RECORDS' as status,
    COUNT(*) as total_balances,
    COUNT(*) FILTER (WHERE escrowed_amount > 0) as still_escrowed,
    SUM(escrowed_amount) as total_escrowed_remaining
FROM commission_balances;

-- Success notification
DO $$
BEGIN
    RAISE NOTICE '✅ Commission conversion admin process has been fixed!';
    RAISE NOTICE '🔧 Problematic constraints removed';
    RAISE NOTICE '🔧 Escrow clearing logic implemented';
    RAISE NOTICE '🔧 Stuck escrow amounts cleared';
    RAISE NOTICE '✅ Admins can now process conversions properly!';
END $$;
