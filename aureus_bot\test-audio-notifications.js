// Audio Notification Diagnostic Script
// Run this to test audio notifications step by step

const { Telegraf } = require('telegraf');

// Replace with your bot token and your Telegram ID
const BOT_TOKEN = 'YOUR_BOT_TOKEN';
const YOUR_TELEGRAM_ID = 'YOUR_TELEGRAM_ID'; // Your actual Telegram user ID

const bot = new Telegraf(BOT_TOKEN);

// Test different notification types
const testNotifications = [
  {
    type: 'SUCCESS',
    emoji: '🔔',
    message: 'SUCCESS notification test - you should hear a sound!',
    disable_notification: false
  },
  {
    type: 'PAYMENT',
    emoji: '💰',
    message: 'PAYMENT notification test - financial update sound!',
    disable_notification: false
  },
  {
    type: 'ERROR',
    emoji: '🚨',
    message: 'ERROR notification test - alert sound!',
    disable_notification: false
  },
  {
    type: 'SILENT',
    emoji: '',
    message: 'SILENT notification test - no sound expected',
    disable_notification: true
  }
];

async function runAudioTests() {
  console.log('🔊 Starting Audio Notification Tests...');
  console.log(`📱 Sending test notifications to Telegram ID: ${YOUR_TELEGRAM_ID}`);
  
  for (let i = 0; i < testNotifications.length; i++) {
    const test = testNotifications[i];
    
    console.log(`\n📤 Test ${i + 1}/${testNotifications.length}: ${test.type}`);
    
    try {
      const message = test.emoji ? `${test.emoji} ${test.message}` : test.message;
      
      await bot.telegram.sendMessage(YOUR_TELEGRAM_ID, message, {
        parse_mode: 'Markdown',
        disable_notification: test.disable_notification
      });
      
      console.log(`✅ Sent: ${test.type} notification`);
      console.log(`   Message: ${message}`);
      console.log(`   Audio: ${test.disable_notification ? 'DISABLED' : 'ENABLED'}`);
      
      // Wait 3 seconds between tests
      await new Promise(resolve => setTimeout(resolve, 3000));
      
    } catch (error) {
      console.error(`❌ Error sending ${test.type} notification:`, error.message);
    }
  }
  
  console.log('\n🎯 Test Summary:');
  console.log('1. SUCCESS notification - Should have 🔔 emoji and play sound');
  console.log('2. PAYMENT notification - Should have 💰 emoji and play sound');
  console.log('3. ERROR notification - Should have 🚨 emoji and play sound');
  console.log('4. SILENT notification - Should have no emoji and no sound');
  
  console.log('\n📱 Check your Telegram app now!');
  console.log('If you don\'t hear sounds, check:');
  console.log('- Telegram notification settings');
  console.log('- Device volume');
  console.log('- Do Not Disturb mode');
  
  process.exit(0);
}

// Test Telegram client settings
async function testTelegramSettings() {
  console.log('📱 Testing Telegram Client Settings...');
  
  try {
    // Send a regular message first
    await bot.telegram.sendMessage(YOUR_TELEGRAM_ID, 
      '📋 **TELEGRAM SETTINGS TEST**\n\n' +
      'This is a regular message to test basic connectivity.\n\n' +
      '**Next Steps:**\n' +
      '1. Ensure you received this message\n' +
      '2. Check your Telegram notification settings\n' +
      '3. Run the audio tests', 
      { parse_mode: 'Markdown' }
    );
    
    console.log('✅ Basic message sent successfully');
    
    // Wait 2 seconds then send audio test
    setTimeout(async () => {
      await bot.telegram.sendMessage(YOUR_TELEGRAM_ID,
        '🔔 **AUDIO TEST MESSAGE**\n\n' +
        'This message should play a notification sound.\n\n' +
        'If you don\'t hear anything, check:\n' +
        '• Telegram Settings → Notifications\n' +
        '• Device volume settings\n' +
        '• Do Not Disturb mode',
        { 
          parse_mode: 'Markdown',
          disable_notification: false // Explicitly enable sound
        }
      );
      
      console.log('✅ Audio test message sent');
      console.log('📱 Check your Telegram app for sound!');
    }, 2000);
    
  } catch (error) {
    console.error('❌ Error testing Telegram settings:', error.message);
  }
}

// Database connectivity test (requires your database setup)
async function testDatabaseConnection() {
  console.log('🗄️ Testing Database Connection...');
  
  // You would need to add your Supabase client here
  // const { createClient } = require('@supabase/supabase-js');
  // const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  console.log('⚠️ Database test requires Supabase client setup');
  console.log('Check if these tables exist in your database:');
  console.log('- user_notification_preferences');
  console.log('- admin_notification_preferences');
  console.log('- notification_log');
  console.log('- notification_sound_types');
}

// Main test runner
async function main() {
  console.log('🎵 Audio Notification Diagnostic Tool');
  console.log('=====================================\n');
  
  if (!BOT_TOKEN || BOT_TOKEN === 'YOUR_BOT_TOKEN') {
    console.error('❌ Please set your BOT_TOKEN in the script');
    process.exit(1);
  }
  
  if (!YOUR_TELEGRAM_ID || YOUR_TELEGRAM_ID === 'YOUR_TELEGRAM_ID') {
    console.error('❌ Please set YOUR_TELEGRAM_ID in the script');
    process.exit(1);
  }
  
  const args = process.argv.slice(2);
  const command = args[0] || 'settings';
  
  switch (command) {
    case 'settings':
      await testTelegramSettings();
      break;
    case 'audio':
      await runAudioTests();
      break;
    case 'database':
      await testDatabaseConnection();
      break;
    default:
      console.log('Usage:');
      console.log('  node test-audio-notifications.js settings   # Test basic Telegram settings');
      console.log('  node test-audio-notifications.js audio      # Test audio notifications');
      console.log('  node test-audio-notifications.js database   # Test database connection');
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the tests
main().catch(console.error);
