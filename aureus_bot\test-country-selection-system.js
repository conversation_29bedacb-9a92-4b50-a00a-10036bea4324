// Test Country Selection System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

class CountrySelectionSystemTester {
  constructor() {
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Testing Country Selection System...\n');
    
    try {
      // 1. Test database schema
      await this.testDatabaseSchema();
      
      // 2. Test country functions
      await this.testCountryFunctions();
      
      // 3. Test country data
      await this.testCountryData();
      
      // 4. Test validation logic
      await this.testValidationLogic();
      
      // 5. Generate test report
      await this.generateTestReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testDatabaseSchema() {
    console.log('🗄️ Testing Database Schema...');
    
    // Test users table columns
    try {
      const { data: usersTest, error: usersError } = await supabase
        .from('users')
        .select('country_of_residence, country_name, country_selection_completed')
        .limit(1);
      
      if (usersError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'users_table_columns',
          status: 'FAIL',
          message: `Country columns not accessible: ${usersError.message}`
        });
        console.log('   ❌ Users table country columns: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'users_table_columns',
          status: 'PASS',
          message: 'Country columns accessible'
        });
        console.log('   ✅ Users table country columns: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'users_table_columns',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ Users table country columns: Error');
    }
    
    // Test supported_countries table
    try {
      const { data: countriesTest, error: countriesError } = await supabase
        .from('supported_countries')
        .select('*')
        .limit(1);
      
      if (countriesError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'supported_countries',
          status: 'FAIL',
          message: `Table not accessible: ${countriesError.message}`
        });
        console.log('   ❌ supported_countries table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'supported_countries',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ supported_countries table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'supported_countries',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ supported_countries table: Error');
    }
    
    // Test country_change_log table
    try {
      const { data: logTest, error: logError } = await supabase
        .from('country_change_log')
        .select('*')
        .limit(1);
      
      if (logError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'country_change_log',
          status: 'FAIL',
          message: `Table not accessible: ${logError.message}`
        });
        console.log('   ❌ country_change_log table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'country_change_log',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ country_change_log table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'country_change_log',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ country_change_log table: Error');
    }
    
    console.log('');
  }

  async testCountryFunctions() {
    console.log('🔧 Testing Country Functions...');
    
    // Test check_country_selection function
    try {
      const { data: checkResult, error: checkError } = await supabase
        .rpc('check_country_selection', { p_user_id: 999999 }); // Non-existent user
      
      if (checkError) {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'check_country_selection',
          status: 'FAIL',
          message: `Function error: ${checkError.message}`
        });
        console.log('   ❌ check_country_selection function: Error');
      } else {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'check_country_selection',
          status: 'PASS',
          message: `Function works (returned: ${checkResult})`
        });
        console.log('   ✅ check_country_selection function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'COUNTRY_FUNCTIONS',
        component: 'check_country_selection',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ check_country_selection function: Error');
    }
    
    // Test get_primary_countries function
    try {
      const { data: primaryResult, error: primaryError } = await supabase
        .rpc('get_primary_countries');
      
      if (primaryError) {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'get_primary_countries',
          status: 'FAIL',
          message: `Function error: ${primaryError.message}`
        });
        console.log('   ❌ get_primary_countries function: Error');
      } else {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'get_primary_countries',
          status: 'PASS',
          message: `Function works (returned ${primaryResult?.length || 0} countries)`
        });
        console.log(`   ✅ get_primary_countries function: Working (${primaryResult?.length || 0} countries)`);
      }
    } catch (error) {
      this.testResults.push({
        test: 'COUNTRY_FUNCTIONS',
        component: 'get_primary_countries',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ get_primary_countries function: Error');
    }
    
    // Test update_user_country function
    try {
      const { data: updateResult, error: updateError } = await supabase
        .rpc('update_user_country', {
          p_user_id: 999999,
          p_country_code: 'ZAF',
          p_country_name: 'South Africa'
        });
      
      if (updateError) {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'update_user_country',
          status: 'FAIL',
          message: `Function error: ${updateError.message}`
        });
        console.log('   ❌ update_user_country function: Error');
      } else {
        this.testResults.push({
          test: 'COUNTRY_FUNCTIONS',
          component: 'update_user_country',
          status: 'PASS',
          message: `Function works (returned: ${updateResult})`
        });
        console.log('   ✅ update_user_country function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'COUNTRY_FUNCTIONS',
        component: 'update_user_country',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ update_user_country function: Error');
    }
    
    console.log('');
  }

  async testCountryData() {
    console.log('🌍 Testing Country Data...');
    
    // Test primary countries data
    try {
      const { data: primaryCountries, error: primaryError } = await supabase
        .from('supported_countries')
        .select('*')
        .eq('is_primary', true)
        .order('display_order');
      
      if (primaryError) {
        this.testResults.push({
          test: 'COUNTRY_DATA',
          component: 'primary_countries',
          status: 'FAIL',
          message: `Error accessing primary countries: ${primaryError.message}`
        });
        console.log('   ❌ Primary countries data: Error');
      } else {
        const expectedPrimary = ['ZAF', 'USA', 'GBR', 'CAN', 'AUS'];
        const actualPrimary = primaryCountries.map(c => c.country_code);
        const hasAllExpected = expectedPrimary.every(code => actualPrimary.includes(code));
        
        this.testResults.push({
          test: 'COUNTRY_DATA',
          component: 'primary_countries',
          status: hasAllExpected ? 'PASS' : 'FAIL',
          message: `Primary countries: ${actualPrimary.length} found, expected codes ${hasAllExpected ? 'present' : 'missing'}`
        });
        
        const icon = hasAllExpected ? '✅' : '❌';
        console.log(`   ${icon} Primary countries data: ${actualPrimary.length} countries, expected codes ${hasAllExpected ? 'present' : 'missing'}`);
      }
    } catch (error) {
      this.testResults.push({
        test: 'COUNTRY_DATA',
        component: 'primary_countries',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ Primary countries data: Error');
    }
    
    // Test country statistics view
    try {
      const { data: statsTest, error: statsError } = await supabase
        .from('country_statistics')
        .select('*')
        .limit(1);
      
      if (statsError) {
        this.testResults.push({
          test: 'COUNTRY_DATA',
          component: 'country_statistics',
          status: 'FAIL',
          message: `Statistics view error: ${statsError.message}`
        });
        console.log('   ❌ Country statistics view: Error');
      } else {
        this.testResults.push({
          test: 'COUNTRY_DATA',
          component: 'country_statistics',
          status: 'PASS',
          message: 'Statistics view accessible'
        });
        console.log('   ✅ Country statistics view: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'COUNTRY_DATA',
        component: 'country_statistics',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ Country statistics view: Error');
    }
    
    console.log('');
  }

  async testValidationLogic() {
    console.log('✅ Testing Validation Logic...');
    
    // Test country code validation
    const countryCodeTests = [
      { input: 'ZAF', expected: true, description: 'Valid ISO 3166-1 alpha-3 code' },
      { input: 'USA', expected: true, description: 'Valid US code' },
      { input: 'GB', expected: false, description: 'Invalid alpha-2 code' },
      { input: 'INVALID', expected: false, description: 'Invalid long code' },
      { input: '', expected: false, description: 'Empty string' },
      { input: '123', expected: false, description: 'Numeric code' }
    ];
    
    const countryCodePattern = /^[A-Z]{3}$/;
    
    for (const test of countryCodeTests) {
      const isValid = countryCodePattern.test(test.input);
      const passed = isValid === test.expected;
      
      this.testResults.push({
        test: 'VALIDATION_LOGIC',
        component: 'country_code_validation',
        status: passed ? 'PASS' : 'FAIL',
        message: `${test.description}: "${test.input}" -> ${isValid}`
      });
      
      const icon = passed ? '✅' : '❌';
      console.log(`   ${icon} ${test.description}: "${test.input}"`);
    }
    
    console.log('');
  }

  async generateTestReport() {
    console.log('📋 COUNTRY SELECTION SYSTEM TEST REPORT');
    console.log('═'.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`\n📊 TEST SUMMARY:`);
    console.log(`• Total Tests: ${totalTests}`);
    console.log(`• Passed: ${passedTests} ✅`);
    console.log(`• Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
    console.log(`• Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // Group results by test type
    const testTypes = [...new Set(this.testResults.map(r => r.test))];
    
    for (const testType of testTypes) {
      const typeResults = this.testResults.filter(r => r.test === testType);
      const typePassed = typeResults.filter(r => r.status === 'PASS').length;
      
      console.log(`\n🧪 ${testType}:`);
      console.log(`   Status: ${typePassed === typeResults.length ? '✅ ALL PASS' : '❌ SOME FAILED'}`);
      console.log(`   Results: ${typePassed}/${typeResults.length}`);
      
      typeResults.forEach(result => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`   ${icon} ${result.component}: ${result.message}`);
      });
    }
    
    console.log('\n💡 IMPLEMENTATION STATUS:');
    
    const schemaIssues = this.testResults.filter(r => r.test === 'DATABASE_SCHEMA' && r.status === 'FAIL');
    if (schemaIssues.length > 0) {
      console.log('❌ Database schema needs to be applied');
      console.log('   Run: country-selection-schema.sql in Supabase');
    } else {
      console.log('✅ Database schema is ready');
    }
    
    const functionIssues = this.testResults.filter(r => r.test === 'COUNTRY_FUNCTIONS' && r.status === 'FAIL');
    if (functionIssues.length > 0) {
      console.log('❌ Database functions need to be created');
      console.log('   Run: country-selection-schema.sql in Supabase');
    } else {
      console.log('✅ Database functions are working');
    }
    
    const dataIssues = this.testResults.filter(r => r.test === 'COUNTRY_DATA' && r.status === 'FAIL');
    if (dataIssues.length > 0) {
      console.log('❌ Country data needs to be populated');
      console.log('   Run: country-selection-schema.sql in Supabase');
    } else {
      console.log('✅ Country data is populated');
    }
    
    if (failedTests === 0) {
      console.log('\n🎉 SYSTEM READY: Country selection system is fully operational!');
    } else {
      console.log('\n⚠️ SETUP REQUIRED: Some components need configuration');
    }
    
    console.log('\n═'.repeat(60));
    console.log('📋 TEST COMPLETE');
  }
}

// Run the tests
async function runTests() {
  const tester = new CountrySelectionSystemTester();
  await tester.runAllTests();
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { CountrySelectionSystemTester };
