// Test KYC (Know Your Customer) System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

class KYCSystemTester {
  constructor() {
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Testing KYC (Know Your Customer) System...\n');
    
    try {
      // 1. Test database schema
      await this.testDatabaseSchema();
      
      // 2. Test KYC functions
      await this.testKYCFunctions();
      
      // 3. Test data validation
      await this.testDataValidation();
      
      // 4. Test certificate timeline
      await this.testCertificateTimeline();
      
      // 5. Generate test report
      await this.generateTestReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testDatabaseSchema() {
    console.log('🗄️ Testing Database Schema...');
    
    // Test kyc_information table
    try {
      const { data: kycTest, error: kycError } = await supabase
        .from('kyc_information')
        .select('*')
        .limit(1);
      
      if (kycError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'kyc_information',
          status: 'FAIL',
          message: `Table not accessible: ${kycError.message}`
        });
        console.log('   ❌ kyc_information table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'kyc_information',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ kyc_information table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'kyc_information',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ kyc_information table: Error');
    }
    
    // Test kyc_audit_log table
    try {
      const { data: auditTest, error: auditError } = await supabase
        .from('kyc_audit_log')
        .select('*')
        .limit(1);
      
      if (auditError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'kyc_audit_log',
          status: 'FAIL',
          message: `Table not accessible: ${auditError.message}`
        });
        console.log('   ❌ kyc_audit_log table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'kyc_audit_log',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ kyc_audit_log table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'kyc_audit_log',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ kyc_audit_log table: Error');
    }
    
    console.log('');
  }

  async testKYCFunctions() {
    console.log('🔧 Testing KYC Functions...');
    
    // Test check_kyc_completion function
    try {
      const { data: checkResult, error: checkError } = await supabase
        .rpc('check_kyc_completion', { p_user_id: 999999 }); // Non-existent user
      
      if (checkError) {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'check_kyc_completion',
          status: 'FAIL',
          message: `Function error: ${checkError.message}`
        });
        console.log('   ❌ check_kyc_completion function: Error');
      } else {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'check_kyc_completion',
          status: 'PASS',
          message: `Function works (returned: ${checkResult})`
        });
        console.log('   ✅ check_kyc_completion function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'KYC_FUNCTIONS',
        component: 'check_kyc_completion',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ check_kyc_completion function: Error');
    }
    
    // Test hash_id_number function
    try {
      const { data: hashResult, error: hashError } = await supabase
        .rpc('hash_id_number', { p_id_number: '1234567890123' });
      
      if (hashError) {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'hash_id_number',
          status: 'FAIL',
          message: `Function error: ${hashError.message}`
        });
        console.log('   ❌ hash_id_number function: Error');
      } else {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'hash_id_number',
          status: 'PASS',
          message: `Function works (hash length: ${hashResult?.length || 0})`
        });
        console.log('   ✅ hash_id_number function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'KYC_FUNCTIONS',
        component: 'hash_id_number',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ hash_id_number function: Error');
    }
    
    // Test get_certificate_timeline function
    try {
      const { data: timelineResult, error: timelineError } = await supabase
        .rpc('get_certificate_timeline');
      
      if (timelineError) {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'get_certificate_timeline',
          status: 'FAIL',
          message: `Function error: ${timelineError.message}`
        });
        console.log('   ❌ get_certificate_timeline function: Error');
      } else {
        this.testResults.push({
          test: 'KYC_FUNCTIONS',
          component: 'get_certificate_timeline',
          status: 'PASS',
          message: `Function works (timeline: ${timelineResult?.substring(0, 50)}...)`
        });
        console.log('   ✅ get_certificate_timeline function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'KYC_FUNCTIONS',
        component: 'get_certificate_timeline',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ get_certificate_timeline function: Error');
    }
    
    console.log('');
  }

  async testDataValidation() {
    console.log('✅ Testing Data Validation...');
    
    // Test name validation patterns
    const nameTests = [
      { input: 'John', expected: true, description: 'Valid simple name' },
      { input: 'Mary-Jane', expected: true, description: 'Valid hyphenated name' },
      { input: "O'Connor", expected: true, description: 'Valid apostrophe name' },
      { input: 'John123', expected: false, description: 'Invalid with numbers' },
      { input: 'J', expected: false, description: 'Too short' },
      { input: '', expected: false, description: 'Empty string' }
    ];
    
    const namePattern = /^[a-zA-Z\s'-]+$/;
    
    for (const test of nameTests) {
      const isValid = test.input.length >= 2 && namePattern.test(test.input);
      const passed = isValid === test.expected;
      
      this.testResults.push({
        test: 'DATA_VALIDATION',
        component: 'name_validation',
        status: passed ? 'PASS' : 'FAIL',
        message: `${test.description}: "${test.input}" -> ${isValid}`
      });
      
      const icon = passed ? '✅' : '❌';
      console.log(`   ${icon} ${test.description}: "${test.input}"`);
    }
    
    // Test ID number validation patterns
    const idTests = [
      { input: '1234567890123', type: 'national_id', expected: true, description: 'Valid SA ID (13 digits)' },
      { input: '12345678901', type: 'national_id', expected: false, description: 'Invalid SA ID (11 digits)' },
      { input: 'A12345678', type: 'passport', expected: true, description: 'Valid passport format' },
      { input: '123', type: 'passport', expected: false, description: 'Invalid passport (too short)' }
    ];
    
    for (const test of idTests) {
      let isValid = false;
      
      if (test.type === 'national_id') {
        isValid = /^\d{13}$/.test(test.input);
      } else if (test.type === 'passport') {
        isValid = /^[A-Z0-9]{6,20}$/i.test(test.input);
      }
      
      const passed = isValid === test.expected;
      
      this.testResults.push({
        test: 'DATA_VALIDATION',
        component: 'id_validation',
        status: passed ? 'PASS' : 'FAIL',
        message: `${test.description}: "${test.input}" -> ${isValid}`
      });
      
      const icon = passed ? '✅' : '❌';
      console.log(`   ${icon} ${test.description}: "${test.input}"`);
    }
    
    console.log('');
  }

  async testCertificateTimeline() {
    console.log('⏰ Testing Certificate Timeline...');
    
    try {
      const { data: timeline, error } = await supabase
        .rpc('get_certificate_timeline');
      
      if (error) {
        this.testResults.push({
          test: 'CERTIFICATE_TIMELINE',
          component: 'timeline_generation',
          status: 'FAIL',
          message: `Error: ${error.message}`
        });
        console.log('   ❌ Timeline generation: Error');
      } else {
        this.testResults.push({
          test: 'CERTIFICATE_TIMELINE',
          component: 'timeline_generation',
          status: 'PASS',
          message: `Generated timeline: ${timeline}`
        });
        console.log(`   ✅ Timeline generated: ${timeline}`);
      }
    } catch (error) {
      this.testResults.push({
        test: 'CERTIFICATE_TIMELINE',
        component: 'timeline_generation',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ Timeline generation: Error');
    }
    
    console.log('');
  }

  async generateTestReport() {
    console.log('📋 KYC SYSTEM TEST REPORT');
    console.log('═'.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`\n📊 TEST SUMMARY:`);
    console.log(`• Total Tests: ${totalTests}`);
    console.log(`• Passed: ${passedTests} ✅`);
    console.log(`• Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
    console.log(`• Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // Group results by test type
    const testTypes = [...new Set(this.testResults.map(r => r.test))];
    
    for (const testType of testTypes) {
      const typeResults = this.testResults.filter(r => r.test === testType);
      const typePassed = typeResults.filter(r => r.status === 'PASS').length;
      
      console.log(`\n🧪 ${testType}:`);
      console.log(`   Status: ${typePassed === typeResults.length ? '✅ ALL PASS' : '❌ SOME FAILED'}`);
      console.log(`   Results: ${typePassed}/${typeResults.length}`);
      
      typeResults.forEach(result => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`   ${icon} ${result.component}: ${result.message}`);
      });
    }
    
    console.log('\n💡 IMPLEMENTATION STATUS:');
    
    const schemaIssues = this.testResults.filter(r => r.test === 'DATABASE_SCHEMA' && r.status === 'FAIL');
    if (schemaIssues.length > 0) {
      console.log('❌ Database schema needs to be applied');
      console.log('   Run: kyc-system-schema.sql in Supabase');
    } else {
      console.log('✅ Database schema is ready');
    }
    
    const functionIssues = this.testResults.filter(r => r.test === 'KYC_FUNCTIONS' && r.status === 'FAIL');
    if (functionIssues.length > 0) {
      console.log('❌ Database functions need to be created');
      console.log('   Run: kyc-system-schema.sql in Supabase');
    } else {
      console.log('✅ Database functions are working');
    }
    
    const validationIssues = this.testResults.filter(r => r.test === 'DATA_VALIDATION' && r.status === 'FAIL');
    if (validationIssues.length > 0) {
      console.log('⚠️ Some validation tests failed');
      console.log('   Review validation logic in bot code');
    } else {
      console.log('✅ Data validation is working correctly');
    }
    
    if (failedTests === 0) {
      console.log('\n🎉 SYSTEM READY: KYC system is fully operational!');
    } else {
      console.log('\n⚠️ SETUP REQUIRED: Some components need configuration');
    }
    
    console.log('\n═'.repeat(60));
    console.log('📋 TEST COMPLETE');
  }
}

// Run the tests
async function runTests() {
  const tester = new KYCSystemTester();
  await tester.runAllTests();
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { KYCSystemTester };
