// Test Legal Documents System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

class LegalDocumentsSystemTester {
  constructor() {
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Testing Legal Documents System...\n');
    
    try {
      // 1. Test database schema
      await this.testDatabaseSchema();
      
      // 2. Test document URLs
      await this.testDocumentURLs();
      
      // 3. Test NDA functions
      await this.testNDAFunctions();
      
      // 4. Generate test report
      await this.generateTestReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testDatabaseSchema() {
    console.log('🗄️ Testing Database Schema...');
    
    // Test nda_acceptances table
    try {
      const { data: ndaTest, error: ndaError } = await supabase
        .from('nda_acceptances')
        .select('*')
        .limit(1);
      
      if (ndaError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'nda_acceptances',
          status: 'FAIL',
          message: `Table not accessible: ${ndaError.message}`
        });
        console.log('   ❌ nda_acceptances table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'nda_acceptances',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ nda_acceptances table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'nda_acceptances',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ nda_acceptances table: Error');
    }
    
    // Test document_access_logs table
    try {
      const { data: logTest, error: logError } = await supabase
        .from('document_access_logs')
        .select('*')
        .limit(1);
      
      if (logError) {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'document_access_logs',
          status: 'FAIL',
          message: `Table not accessible: ${logError.message}`
        });
        console.log('   ❌ document_access_logs table: Not accessible');
      } else {
        this.testResults.push({
          test: 'DATABASE_SCHEMA',
          component: 'document_access_logs',
          status: 'PASS',
          message: 'Table accessible'
        });
        console.log('   ✅ document_access_logs table: Accessible');
      }
    } catch (error) {
      this.testResults.push({
        test: 'DATABASE_SCHEMA',
        component: 'document_access_logs',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ document_access_logs table: Error');
    }
    
    console.log('');
  }

  async testDocumentURLs() {
    console.log('📄 Testing Document URLs...');
    
    const documents = {
      'CIPC Registration': 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets//cipc.pdf',
      'SARS Tax Registration': 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets//sars.pdf',
      'FNB Bank Confirmation': 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets//fnb.pdf',
      'Ubuntu Afrique Placer Report': 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets//Ubuntu_Afrique_Kadoma_Placer_Report.pdf'
    };
    
    for (const [name, url] of Object.entries(documents)) {
      try {
        // Test URL accessibility (basic check)
        const response = await fetch(url, { method: 'HEAD' });
        
        if (response.ok) {
          this.testResults.push({
            test: 'DOCUMENT_URLS',
            component: name,
            status: 'PASS',
            message: `URL accessible (${response.status})`
          });
          console.log(`   ✅ ${name}: Accessible`);
        } else {
          this.testResults.push({
            test: 'DOCUMENT_URLS',
            component: name,
            status: 'FAIL',
            message: `HTTP ${response.status}`
          });
          console.log(`   ❌ ${name}: HTTP ${response.status}`);
        }
      } catch (error) {
        this.testResults.push({
          test: 'DOCUMENT_URLS',
          component: name,
          status: 'FAIL',
          message: `Network error: ${error.message}`
        });
        console.log(`   ❌ ${name}: Network error`);
      }
    }
    
    console.log('');
  }

  async testNDAFunctions() {
    console.log('🔒 Testing NDA Functions...');
    
    // Test check_nda_acceptance function
    try {
      const { data: checkResult, error: checkError } = await supabase
        .rpc('check_nda_acceptance', { p_user_id: 999999 }); // Non-existent user
      
      if (checkError) {
        this.testResults.push({
          test: 'NDA_FUNCTIONS',
          component: 'check_nda_acceptance',
          status: 'FAIL',
          message: `Function error: ${checkError.message}`
        });
        console.log('   ❌ check_nda_acceptance function: Error');
      } else {
        this.testResults.push({
          test: 'NDA_FUNCTIONS',
          component: 'check_nda_acceptance',
          status: 'PASS',
          message: `Function works (returned: ${checkResult})`
        });
        console.log('   ✅ check_nda_acceptance function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'NDA_FUNCTIONS',
        component: 'check_nda_acceptance',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ check_nda_acceptance function: Error');
    }
    
    // Test log_document_access function
    try {
      const { data: logResult, error: logError } = await supabase
        .rpc('log_document_access', {
          p_user_id: 999999,
          p_document_type: 'test',
          p_document_url: 'https://test.com/test.pdf',
          p_telegram_user_id: 123456789,
          p_username: 'testuser'
        });
      
      if (logError) {
        this.testResults.push({
          test: 'NDA_FUNCTIONS',
          component: 'log_document_access',
          status: 'FAIL',
          message: `Function error: ${logError.message}`
        });
        console.log('   ❌ log_document_access function: Error');
      } else {
        this.testResults.push({
          test: 'NDA_FUNCTIONS',
          component: 'log_document_access',
          status: 'PASS',
          message: `Function works (returned: ${logResult})`
        });
        console.log('   ✅ log_document_access function: Working');
      }
    } catch (error) {
      this.testResults.push({
        test: 'NDA_FUNCTIONS',
        component: 'log_document_access',
        status: 'FAIL',
        message: `Error: ${error.message}`
      });
      console.log('   ❌ log_document_access function: Error');
    }
    
    console.log('');
  }

  async generateTestReport() {
    console.log('📋 LEGAL DOCUMENTS SYSTEM TEST REPORT');
    console.log('═'.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`\n📊 TEST SUMMARY:`);
    console.log(`• Total Tests: ${totalTests}`);
    console.log(`• Passed: ${passedTests} ✅`);
    console.log(`• Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
    console.log(`• Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // Group results by test type
    const testTypes = [...new Set(this.testResults.map(r => r.test))];
    
    for (const testType of testTypes) {
      const typeResults = this.testResults.filter(r => r.test === testType);
      const typePassed = typeResults.filter(r => r.status === 'PASS').length;
      
      console.log(`\n🧪 ${testType}:`);
      console.log(`   Status: ${typePassed === typeResults.length ? '✅ ALL PASS' : '❌ SOME FAILED'}`);
      console.log(`   Results: ${typePassed}/${typeResults.length}`);
      
      typeResults.forEach(result => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`   ${icon} ${result.component}: ${result.message}`);
      });
    }
    
    console.log('\n💡 IMPLEMENTATION STATUS:');
    
    const schemaIssues = this.testResults.filter(r => r.test === 'DATABASE_SCHEMA' && r.status === 'FAIL');
    if (schemaIssues.length > 0) {
      console.log('❌ Database schema needs to be applied');
      console.log('   Run: legal-documents-schema.sql in Supabase');
    } else {
      console.log('✅ Database schema is ready');
    }
    
    const urlIssues = this.testResults.filter(r => r.test === 'DOCUMENT_URLS' && r.status === 'FAIL');
    if (urlIssues.length > 0) {
      console.log('⚠️ Some document URLs are not accessible');
      console.log('   Check document uploads in Supabase storage');
    } else {
      console.log('✅ All document URLs are accessible');
    }
    
    const functionIssues = this.testResults.filter(r => r.test === 'NDA_FUNCTIONS' && r.status === 'FAIL');
    if (functionIssues.length > 0) {
      console.log('❌ Database functions need to be created');
      console.log('   Run: legal-documents-schema.sql in Supabase');
    } else {
      console.log('✅ Database functions are working');
    }
    
    if (failedTests === 0) {
      console.log('\n🎉 SYSTEM READY: Legal Documents system is fully operational!');
    } else {
      console.log('\n⚠️ SETUP REQUIRED: Some components need configuration');
    }
    
    console.log('\n═'.repeat(60));
    console.log('📋 TEST COMPLETE');
  }
}

// Run the tests
async function runTests() {
  const tester = new LegalDocumentsSystemTester();
  await tester.runAllTests();
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { LegalDocumentsSystemTester };
