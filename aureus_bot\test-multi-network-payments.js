// Test Multi-Network Payment System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

class MultiNetworkTester {
  constructor() {
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🧪 Testing Multi-Network Payment System...\n');
    
    try {
      // 1. Test wallet availability for all networks
      await this.testWalletAvailability();
      
      // 2. Test network display mapping
      await this.testNetworkDisplayMapping();
      
      // 3. Test payment creation simulation
      await this.testPaymentCreationSimulation();
      
      // 4. Generate test report
      await this.generateTestReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testWalletAvailability() {
    console.log('🏦 Testing Wallet Availability...');
    
    const networks = ['ETH', 'BSC', 'POL', 'TRON'];
    
    for (const network of networks) {
      const { data: wallet, error } = await supabase
        .from('company_wallets')
        .select('*')
        .eq('network', network)
        .eq('currency', 'USDT')
        .eq('is_active', true)
        .single();
      
      if (error || !wallet) {
        this.testResults.push({
          test: 'WALLET_AVAILABILITY',
          network: network,
          status: 'FAIL',
          message: `No active wallet found for ${network} USDT`
        });
        console.log(`   ❌ ${network}: No wallet found`);
      } else {
        this.testResults.push({
          test: 'WALLET_AVAILABILITY',
          network: network,
          status: 'PASS',
          message: `Wallet found: ${wallet.wallet_address.substring(0, 10)}...`
        });
        console.log(`   ✅ ${network}: ${wallet.wallet_address.substring(0, 10)}...`);
      }
    }
    
    console.log('');
  }

  async testNetworkDisplayMapping() {
    console.log('🗺️ Testing Network Display Mapping...');
    
    const networkMappings = [
      { input: 'ETH', expected: 'USDT-ERC20' },
      { input: 'BSC', expected: 'USDT-BEP20' },
      { input: 'POL', expected: 'USDT-Polygon' },
      { input: 'TRON', expected: 'USDT-TRC20' }
    ];
    
    const networkDisplayMap = {
      'ETH': 'USDT-ERC20',
      'BSC': 'USDT-BEP20', 
      'POL': 'USDT-Polygon',
      'TRON': 'USDT-TRC20'
    };
    
    for (const mapping of networkMappings) {
      const result = networkDisplayMap[mapping.input];
      
      if (result === mapping.expected) {
        this.testResults.push({
          test: 'NETWORK_MAPPING',
          network: mapping.input,
          status: 'PASS',
          message: `${mapping.input} -> ${result}`
        });
        console.log(`   ✅ ${mapping.input} -> ${result}`);
      } else {
        this.testResults.push({
          test: 'NETWORK_MAPPING',
          network: mapping.input,
          status: 'FAIL',
          message: `Expected ${mapping.expected}, got ${result}`
        });
        console.log(`   ❌ ${mapping.input}: Expected ${mapping.expected}, got ${result}`);
      }
    }
    
    console.log('');
  }

  async testPaymentCreationSimulation() {
    console.log('💳 Testing Payment Creation Simulation...');
    
    const networks = ['ETH', 'BSC', 'POL', 'TRON'];
    const testAmount = 100;
    
    for (const network of networks) {
      console.log(`\n   🧪 Testing ${network} payment creation...`);
      
      // Get wallet for this network
      const { data: wallet, error: walletError } = await supabase
        .from('company_wallets')
        .select('wallet_address')
        .eq('network', network)
        .eq('currency', 'USDT')
        .eq('is_active', true)
        .single();
      
      if (walletError || !wallet) {
        this.testResults.push({
          test: 'PAYMENT_SIMULATION',
          network: network,
          status: 'FAIL',
          message: 'No wallet available for payment creation'
        });
        console.log(`      ❌ No wallet available`);
        continue;
      }
      
      // Simulate payment data structure
      const networkDisplayMap = {
        'ETH': 'USDT-ERC20',
        'BSC': 'USDT-BEP20', 
        'POL': 'USDT-Polygon',
        'TRON': 'USDT-TRC20'
      };
      
      const simulatedPayment = {
        amount: testAmount,
        currency: 'USDT',
        network: networkDisplayMap[network],
        receiver_wallet: wallet.wallet_address,
        status: 'pending'
      };
      
      // Test network info extraction
      const networkInfo = this.getNetworkDisplayInfo(simulatedPayment.network);
      
      this.testResults.push({
        test: 'PAYMENT_SIMULATION',
        network: network,
        status: 'PASS',
        message: `Payment structure valid: ${networkInfo.fullName} (${networkInfo.technical})`
      });
      
      console.log(`      ✅ Payment: $${testAmount} USDT`);
      console.log(`      ✅ Network: ${simulatedPayment.network} (${networkInfo.fullName})`);
      console.log(`      ✅ Wallet: ${wallet.wallet_address.substring(0, 10)}...`);
    }
    
    console.log('');
  }

  getNetworkDisplayInfo(networkCode) {
    const networkMap = {
      'USDT-ERC20': {
        fullName: 'Ethereum',
        technical: 'ERC-20',
        icon: '🔷'
      },
      'USDT-BEP20': {
        fullName: 'Binance Smart Chain',
        technical: 'BEP-20',
        icon: '🟡'
      },
      'USDT-Polygon': {
        fullName: 'Polygon',
        technical: 'Polygon',
        icon: '🟣'
      },
      'USDT-TRC20': {
        fullName: 'TRON',
        technical: 'TRC-20',
        icon: '🔴'
      }
    };

    return networkMap[networkCode] || {
      fullName: 'Unknown Network',
      technical: networkCode,
      icon: '❓'
    };
  }

  async generateTestReport() {
    console.log('📋 MULTI-NETWORK PAYMENT TEST REPORT');
    console.log('═'.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(`\n📊 TEST SUMMARY:`);
    console.log(`• Total Tests: ${totalTests}`);
    console.log(`• Passed: ${passedTests} ✅`);
    console.log(`• Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`);
    console.log(`• Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    // Group results by test type
    const testTypes = [...new Set(this.testResults.map(r => r.test))];
    
    for (const testType of testTypes) {
      const typeResults = this.testResults.filter(r => r.test === testType);
      const typePassed = typeResults.filter(r => r.status === 'PASS').length;
      
      console.log(`\n🧪 ${testType}:`);
      console.log(`   Status: ${typePassed === typeResults.length ? '✅ ALL PASS' : '❌ SOME FAILED'}`);
      console.log(`   Results: ${typePassed}/${typeResults.length}`);
      
      typeResults.forEach(result => {
        const icon = result.status === 'PASS' ? '✅' : '❌';
        console.log(`   ${icon} ${result.network}: ${result.message}`);
      });
    }
    
    console.log('\n💡 RECOMMENDATIONS:');
    
    const failedWallets = this.testResults.filter(r => r.test === 'WALLET_AVAILABILITY' && r.status === 'FAIL');
    if (failedWallets.length > 0) {
      console.log('• Add missing wallet addresses to company_wallets table');
      failedWallets.forEach(f => console.log(`  - ${f.network} USDT wallet needed`));
    }
    
    const failedMappings = this.testResults.filter(r => r.test === 'NETWORK_MAPPING' && r.status === 'FAIL');
    if (failedMappings.length > 0) {
      console.log('• Fix network display mapping logic');
    }
    
    if (failedTests === 0) {
      console.log('✅ All tests passed! Multi-network payment system is ready.');
    }
    
    console.log('\n═'.repeat(60));
    console.log('📋 TEST COMPLETE');
  }
}

// Run the tests
async function runTests() {
  const tester = new MultiNetworkTester();
  await tester.runAllTests();
}

// Execute if run directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { MultiNetworkTester };
