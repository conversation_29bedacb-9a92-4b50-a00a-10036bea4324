require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function testUser88Portfolio() {
    console.log('🧪 Testing User 88 portfolio display after escrow fix...\n');
    
    try {
        // Get user's commission balance
        const { data: balance, error: balanceError } = await supabase
            .from('commission_balances')
            .select('*')
            .eq('user_id', 88)
            .single();
        
        if (balanceError) {
            console.error('❌ Error fetching balance:', balanceError);
            return;
        }
        
        console.log('📊 Current balance data:', balance);
        
        // Get pending commission conversion requests
        const { data: pendingConversions, error: conversionError } = await supabase
            .from('commission_conversions')
            .select('*')
            .eq('user_id', 88)
            .eq('status', 'pending');
        
        if (conversionError) {
            console.error('❌ Error fetching pending conversions:', conversionError);
            return;
        }
        
        console.log('⏳ Pending conversions:', pendingConversions);
        
        // Simulate portfolio display logic
        const availableShares = balance?.share_balance || 0;
        const escrowedAmount = balance?.escrowed_amount || 0;
        const pendingShares = pendingConversions?.reduce((sum, conv) => sum + (conv.shares_requested || 0), 0) || 0;
        
        console.log('\n📱 Portfolio Display:');
        console.log(`💎 Available Shares: ${availableShares}`);
        console.log(`💰 USDT Balance: $${balance?.usdt_balance || 0}`);
        console.log(`🔒 Escrowed USDT: $${escrowedAmount}`);
        console.log(`⏳ Pending Shares: ${pendingShares}`);
        
        if (escrowedAmount === 0 && pendingShares === 0) {
            console.log('\n✅ SUCCESS: Portfolio should now display correctly!');
            console.log('✅ No escrow amount showing');
            console.log('✅ No pending shares showing');
        } else {
            console.log('\n⚠️  Issues still present:');
            if (escrowedAmount > 0) console.log(`  - Escrow still showing: $${escrowedAmount}`);
            if (pendingShares > 0) console.log(`  - Pending shares still showing: ${pendingShares}`);
        }
        
    } catch (error) {
        console.error('❌ Error in test:', error);
    }
}

testUser88Portfolio();
