import React, { useState } from 'react'
import { signInWithEmail, signUpWithEmail } from '../lib/supabase'

interface AdminLoginProps {
  onLoginSuccess: (user: any) => void
}

export const AdminLogin: React.FC<AdminLoginProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showSignup, setShowSignup] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const { user, error } = showSignup
        ? await signUpWithEmail(email, password)
        : await signInWithEmail(email, password)

      if (error) {
        setError(error.message)
      } else if (user) {
        if (showSignup) {
          setError('')
          alert('Account created! You can now login.')
          setShowSignup(false)
        } else {
          onLoginSuccess(user)
        }
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-900 p-4">
      <div className="glass-card w-full max-w-lg">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="mb-4">
            <h1 className="text-4xl font-bold text-gradient-gold mb-2">
              🏆 AUREUS
            </h1>
            <p className="text-xl text-gray-300 font-medium">Alliance Holdings</p>
          </div>
          <div className="h-px bg-gradient-to-r from-transparent via-yellow-500/50 to-transparent mb-4"></div>
          <p className="text-gray-400">Admin Portal</p>
        </div>

        {/* Test Credentials Info */}
        <div className="mb-8 p-6 bg-gradient-to-r from-blue-900/30 to-blue-800/20 border border-blue-500/30 rounded-xl backdrop-blur-sm">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <span className="text-blue-400 text-sm">🧪</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-300">Test Credentials</h3>
              <p className="text-sm text-blue-200/80">For development/testing</p>
            </div>
          </div>

          <div className="space-y-3 mb-4">
            <div className="flex items-center gap-3">
              <span className="text-sm text-blue-200 w-20">Email:</span>
              <code className="bg-blue-800/50 px-3 py-1 rounded-lg text-blue-100 font-mono text-sm flex-1"><EMAIL></code>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm text-blue-200 w-20">Password:</span>
              <code className="bg-blue-800/50 px-3 py-1 rounded-lg text-blue-100 font-mono text-sm flex-1">admin123</code>
            </div>
          </div>

          <button
            type="button"
            onClick={() => {
              setEmail('<EMAIL>')
              setPassword('admin123')
            }}
            className="w-full py-2 px-4 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/30 text-blue-300 hover:text-blue-200 rounded-lg transition-all duration-200 text-sm font-medium"
          >
            Fill test credentials
          </button>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-5">
            <div>
              <label htmlFor="email" className="block text-sm font-semibold text-gray-300 mb-3">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-4 py-4 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-gray-300 mb-3">
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full px-4 py-4 bg-gray-800/60 border border-gray-600/50 rounded-xl focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 text-white placeholder-gray-400 text-lg backdrop-blur-sm transition-all duration-200"
                placeholder="••••••••"
              />
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-900/30 border border-red-500/50 rounded-xl text-red-300 text-sm backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full py-4 px-6 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"></div>
                {showSignup ? 'Creating Account...' : 'Signing In...'}
              </div>
            ) : (
              showSignup ? 'Create Account' : 'Sign In'
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-8 text-center space-y-4">
          <div className="h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent"></div>

          <button
            type="button"
            onClick={() => setShowSignup(!showSignup)}
            className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors duration-200"
          >
            {showSignup ? 'Already have an account? Sign In' : 'Need to create an account? Sign Up'}
          </button>

          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            <span>Authorized personnel only</span>
          </div>
        </div>
      </div>
    </div>
  )
}
