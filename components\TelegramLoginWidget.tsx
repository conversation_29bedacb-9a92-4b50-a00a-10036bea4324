import React, { useEffect, useRef, useState } from 'react';

interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

interface TelegramLoginWidgetProps {
  botName: string;
  buttonSize?: 'small' | 'medium' | 'large';
  cornerRadius?: number;
  requestAccess?: 'write';
  showUserPic?: boolean;
  onAuth?: (user: TelegramUser) => void;
  onError?: (error: string) => void;
  className?: string;
}

declare global {
  interface Window {
    telegramLoginCallback?: (user: TelegramUser) => void;
  }
}

export default function TelegramLoginWidget({
  botName,
  buttonSize = 'large',
  cornerRadius = 8,
  requestAccess = 'write',
  showUserPic = true,
  onAuth,
  onError,
  className = ''
}: TelegramLoginWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Create unique callback function name
    const callbackName = `telegramLoginCallback_${Date.now()}`;
    
    // Define the callback function
    window[callbackName as any] = async (user: TelegramUser) => {
      setLoading(true);
      setError(null);
      
      try {
        // Verify the authentication data
        const response = await fetch('/api/auth/telegram-callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(user),
        });

        if (!response.ok) {
          throw new Error('Authentication failed');
        }

        const result = await response.json();
        
        if (result.success) {
          // Redirect to dashboard or call success callback
          if (onAuth) {
            onAuth(user);
          } else {
            window.location.href = '/dashboard';
          }
        } else {
          throw new Error(result.error || 'Authentication failed');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Authentication failed';
        setError(errorMessage);
        if (onError) {
          onError(errorMessage);
        }
      } finally {
        setLoading(false);
      }
    };

    // Clean up any existing script
    const existingScript = document.getElementById(`telegram-login-script-${botName}`);
    if (existingScript) {
      existingScript.remove();
    }

    // Create and append the script
    const script = document.createElement('script');
    script.id = `telegram-login-script-${botName}`;
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.setAttribute('data-telegram-login', botName);
    script.setAttribute('data-size', buttonSize);
    script.setAttribute('data-radius', cornerRadius.toString());
    script.setAttribute('data-request-access', requestAccess);
    script.setAttribute('data-userpic', showUserPic.toString());
    script.setAttribute('data-onauth', `${callbackName}(user)`);
    script.async = true;

    // Add script to container
    if (containerRef.current) {
      containerRef.current.appendChild(script);
    }

    return () => {
      // Clean up
      delete window[callbackName as any];
      const scriptToRemove = document.getElementById(`telegram-login-script-${botName}`);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [botName, buttonSize, cornerRadius, requestAccess, showUserPic, onAuth, onError]);

  return (
    <div className={`telegram-login-widget ${className}`}>
      <div ref={containerRef} className="flex justify-center">
        {loading && (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-400">Authenticating...</span>
          </div>
        )}
      </div>
      
      {error && (
        <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded-lg">
          <p className="text-red-400 text-sm text-center">{error}</p>
        </div>
      )}
    </div>
  );
}
