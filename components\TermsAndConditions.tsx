import React from 'react';

export const TermsAndConditions: React.FC = () => {
    return (
        <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-gold mb-8">Terms & Conditions</h1>
            
            <div className="space-y-8 text-gray-300 leading-relaxed">
                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">1. Introduction and Acceptance</h2>
                    <p className="mb-4">
                        These Terms and Conditions ("Terms") govern your use of the Aureus Alliance Holdings (Pty) Ltd website
                        (aureus.africa) and any share purchase transactions with Aureus Alliance Holdings (Pty) Ltd ("Company," "we," "our").
                    </p>
                    <div className="bg-amber-900/20 border border-amber-500/30 rounded-lg p-4 mb-6">
                        <h3 className="text-lg font-semibold text-amber-400 mb-2">Important Notice</h3>
                        <p className="text-sm">
                            By accessing our website or purchasing shares, you acknowledge that you have read, understood,
                            and agree to be bound by these Terms and all applicable South African laws. If you do not agree
                            to these Terms, you must not use our website or purchase shares.
                        </p>
                    </div>
                    <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                        <h3 className="text-lg font-semibold text-blue-400 mb-2">Company Details</h3>
                        <p className="text-sm">
                            <strong>Company Name:</strong> Aureus Alliance Holdings (Pty) Ltd<br/>
                            <strong>Registration Number:</strong> 2025/368711/07<br/>
                            <strong>Registered Address:</strong> 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169<br/>
                            <strong>Website:</strong> aureus.africa<br/>
                            <strong>Business:</strong> Gold mining operations and share ownership platform<br/>
                            <strong>Governing Law:</strong> Republic of South Africa
                        </p>
                    </div>
                </section>

                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">2. Share Ownership - Not Investment Products</h2>
                    <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mb-6">
                        <h3 className="text-lg font-semibold text-green-400 mb-2">Important Clarification</h3>
                        <p className="text-sm mb-3">
                            The purchase of shares in Aureus Alliance Holdings (Pty) Ltd constitutes direct equity ownership
                            in a South African mining company, not the purchase of financial investment products, securities,
                            or collective investment schemes.
                        </p>
                        <p className="text-sm">
                            <strong>What You Are Purchasing:</strong> Direct ownership shares in a registered South African company
                            that owns and operates gold mining concessions.
                        </p>
                    </div>
                </section>

                <section>
                    <h2 className="text-2xl font-semibent text-gold mb-4">3. Website Use and Restrictions</h2>
                    <p className="mb-4">You agree to use our website only for lawful purposes and in accordance with these Terms.</p>
                    <ul className="list-disc list-inside space-y-2 mb-4">
                        <li>You must be at least 18 years old to use our services</li>
                        <li>You must provide accurate and complete information</li>
                        <li>You are responsible for maintaining the confidentiality of your account</li>
                        <li>You must not use our website for any illegal or unauthorized purpose</li>
                    </ul>
                </section>

                <section>
                    <h2 className="text-2xl font-semibold text-gold mb-4">14. Contact Information</h2>
                    <p className="mb-4">
                        For questions about these Terms or share ownership:<br/>
                        Email: <EMAIL><br/>
                        Address: 1848 Mees Avenue, Randpark Ridge, Randburg, Gauteng, 2169<br/>
                        Website: aureus.africa
                    </p>
                </section>

                <div className="border-t border-gray-600 pt-6 mt-8">
                    <p className="text-sm text-gray-400">
                        Last Updated: {new Date().toLocaleDateString()}<br/>
                        These Terms are effective as of the date last updated.
                    </p>
                </div>
            </div>
        </div>
    );
};
