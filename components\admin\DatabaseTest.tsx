import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';

export const DatabaseTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setTestResult('Testing connection...');
    
    try {
      // Test basic connection
      const { data, error } = await supabase
        .from('admin_users')
        .select('count')
        .limit(1);
      
      if (error) {
        setTestResult(`❌ Connection failed: ${error.message}`);
      } else {
        setTestResult('✅ Database connection successful!');
      }
    } catch (err) {
      setTestResult(`❌ Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="glass-card-strong p-6">
      <h3 className="text-xl font-semibold text-gold-primary mb-4">Database Connection Test</h3>
      
      <button
        onClick={testConnection}
        disabled={loading}
        className="btn-primary mb-4"
      >
        {loading ? 'Testing...' : 'Test Database Connection'}
      </button>
      
      {testResult && (
        <div className="p-4 rounded-lg bg-black-soft border border-gray-600">
          <pre className="text-sm text-white whitespace-pre-wrap">{testResult}</pre>
        </div>
      )}
    </div>
  );
};
