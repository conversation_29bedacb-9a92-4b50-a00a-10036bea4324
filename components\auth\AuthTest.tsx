'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface AuthTestResult {
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  details?: any
}

export default function AuthTest() {
  const { user, loading, signOut, refreshUser, refreshSession, checkOnboardingStatus } = useAuth()
  const [testResults, setTestResults] = useState<AuthTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (result: AuthTestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runAuthTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Check if user is loaded
    addResult({
      test: 'User Authentication State',
      status: user ? 'success' : 'error',
      message: user ? 'User is authenticated' : 'No authenticated user',
      details: user ? {
        id: user.id,
        email: user.email,
        telegram_id: user.user_metadata?.telegram_id
      } : null
    })

    if (user) {
      // Test 2: Check Telegram profile
      addResult({
        test: 'Telegram Profile',
        status: user.telegram_profile ? 'success' : 'error',
        message: user.telegram_profile ? 'Telegram profile loaded' : 'No Telegram profile found',
        details: user.telegram_profile ? {
          telegram_id: user.telegram_profile.telegram_id,
          first_name: user.telegram_profile.first_name,
          username: user.telegram_profile.username,
          country: user.telegram_profile.country
        } : null
      })

      // Test 3: Check onboarding status
      try {
        const onboardingStatus = await checkOnboardingStatus()
        addResult({
          test: 'Onboarding Status',
          status: 'success',
          message: 'Onboarding status retrieved',
          details: onboardingStatus
        })
      } catch (error) {
        addResult({
          test: 'Onboarding Status',
          status: 'error',
          message: 'Failed to get onboarding status',
          details: error
        })
      }

      // Test 4: Test user refresh
      try {
        await refreshUser()
        addResult({
          test: 'User Refresh',
          status: 'success',
          message: 'User data refreshed successfully'
        })
      } catch (error) {
        addResult({
          test: 'User Refresh',
          status: 'error',
          message: 'Failed to refresh user data',
          details: error
        })
      }

      // Test 5: Test session refresh
      try {
        const refreshResult = await refreshSession()
        addResult({
          test: 'Session Refresh',
          status: refreshResult ? 'success' : 'error',
          message: refreshResult ? 'Session refreshed successfully' : 'Session refresh failed'
        })
      } catch (error) {
        addResult({
          test: 'Session Refresh',
          status: 'error',
          message: 'Session refresh threw error',
          details: error
        })
      }

      // Test 6: Test API endpoints
      try {
        const userResponse = await fetch('/api/auth/user', {
          credentials: 'include'
        })
        
        if (userResponse.ok) {
          const userData = await userResponse.json()
          addResult({
            test: 'User API Endpoint',
            status: 'success',
            message: 'User API endpoint working',
            details: userData
          })
        } else {
          addResult({
            test: 'User API Endpoint',
            status: 'error',
            message: `API returned ${userResponse.status}`,
            details: await userResponse.text()
          })
        }
      } catch (error) {
        addResult({
          test: 'User API Endpoint',
          status: 'error',
          message: 'Failed to call user API',
          details: error
        })
      }
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: AuthTestResult['status']) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: AuthTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'pending':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-center mt-2 text-gray-600">Loading authentication state...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Authentication System Test</h2>
      
      <div className="mb-6 flex space-x-4">
        <button
          onClick={runAuthTests}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          {isRunning ? 'Running Tests...' : 'Run Authentication Tests'}
        </button>
        
        {user && (
          <button
            onClick={signOut}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Sign Out
          </button>
        )}
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results:</h3>
          
          {testResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">
                  {getStatusIcon(result.status)} {result.test}
                </h4>
                <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      )}

      {!user && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            <strong>Note:</strong> You need to be authenticated to run comprehensive tests. 
            Please sign in with Telegram first.
          </p>
        </div>
      )}
    </div>
  )
}
