'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireTerms?: boolean
  requireCountry?: boolean
  requireKYC?: boolean
}

export default function ProtectedRoute({
  children,
  requireTerms = false,
  requireCountry = false,
  requireKYC = false
}: ProtectedRouteProps) {
  const { user, loading, checkOnboardingStatus } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      if (loading) return

      if (!user) {
        router.push('/login')
        return
      }

      // If we have additional requirements, check them
      if (requireTerms || requireCountry || requireKYC) {
        const status = await checkOnboardingStatus()
        
        if (requireTerms && !status.hasAcceptedTerms) {
          router.push('/onboarding/terms')
          return
        }
        
        if (requireCountry && !status.hasSelectedCountry) {
          router.push('/onboarding/country')
          return
        }
        
        if (requireKYC && !status.hasCompletedKYC) {
          router.push('/onboarding/kyc')
          return
        }
      }

      setIsAuthorized(true)
      setIsChecking(false)
    }

    checkAuth()
  }, [user, loading, router, requireTerms, requireCountry, requireKYC, checkOnboardingStatus])

  if (isChecking || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Verifying authentication...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return null // Router will handle redirect
  }

  return <>{children}</>
}
