'use client'

import { useEffect, useRef, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface TelegramLoginButtonProps {
  botName: string
  buttonSize?: 'large' | 'medium' | 'small'
  cornerRadius?: number
  requestAccess?: 'write' | 'read'
  showUserPic?: boolean
  onAuthCallback?: (user: any) => void
  onAuthError?: (error: string) => void
  className?: string
}

declare global {
  interface Window {
    TelegramLoginWidget?: {
      dataOnauth: (user: any) => void
    }
  }
}

export default function TelegramLoginButton({
  botName,
  buttonSize = 'large',
  cornerRadius = 8,
  requestAccess = 'write',
  showUserPic = true,
  onAuthCallback,
  onAuthError,
  className
}: TelegramLoginButtonProps) {
  const { signInWithTelegram } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetId = `telegram-login-${botName}`

  useEffect(() => {
    // Clean up any existing script
    const existingScript = document.getElementById(`telegram-login-script-${botName}`)
    if (existingScript) {
      existingScript.remove()
    }

    // Create a unique callback name for this instance
    const callbackName = `TelegramLoginCallback_${Math.random().toString(36).substring(2, 15)}`

    // Define the callback function
    window[callbackName as any] = async (user: any) => {
      try {
        setLoading(true)
        setError(null)
        
        console.log('Telegram auth data received:', user)
        
        // Process the authentication with our backend
        const { success, error } = await signInWithTelegram(user)
        
        if (success) {
          if (onAuthCallback) {
            onAuthCallback(user)
          }
        } else {
          setError(error || 'Authentication failed')
          if (onAuthError) {
            onAuthError(error || 'Authentication failed')
          }
        }
      } catch (err) {
        console.error('Telegram login error:', err)
        setError('Authentication failed')
        if (onAuthError) {
          onAuthError('Authentication failed')
        }
      } finally {
        setLoading(false)
      }
    }

    // Create and append the script
    const script = document.createElement('script')
    script.id = `telegram-login-script-${botName}`
    script.src = 'https://telegram.org/js/telegram-widget.js?22'
    script.setAttribute('data-telegram-login', botName)
    script.setAttribute('data-size', buttonSize)
    script.setAttribute('data-radius', cornerRadius.toString())
    script.setAttribute('data-request-access', requestAccess)
    script.setAttribute('data-userpic', showUserPic.toString())
    script.setAttribute('data-onauth', `${callbackName}(user)`)
    script.async = true
    
    // Add script to container
    if (containerRef.current) {
      containerRef.current.appendChild(script)
    }
    
    return () => {
      // Clean up
      delete window[callbackName as any]
      if (existingScript) {
        existingScript.remove()
      }
    }
  }, [botName, buttonSize, cornerRadius, requestAccess, showUserPic, signInWithTelegram, onAuthCallback, onAuthError])
  
  return (
    <div className={className}>
      <div id={widgetId} ref={containerRef} className="telegram-login-container"></div>
      {loading && (
        <div className="mt-2 text-center text-sm text-gray-600">
          <div className="inline-block animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
          Authenticating...
        </div>
      )}
      {error && (
        <div className="mt-2 text-center text-sm text-red-600">
          {error}
        </div>
      )}
    </div>
  )
}
