'use client'

import { Card } from '@/components/ui/Card'

interface DashboardStatsProps {
  userShares: any[]
  commissionBalance: any
  currentPhase: any
  userPayments: any[]
  className?: string
}

export default function DashboardStats({
  userShares,
  commissionBalance,
  currentPhase,
  userPayments,
  className
}: DashboardStatsProps) {
  
  // Calculate statistics
  const totalShares = userShares.reduce((total, share) => total + (share.shares_purchased || 0), 0)
  const totalInvested = userShares.reduce((total, share) => total + parseFloat(share.total_amount || 0), 0)
  const pendingPayments = userPayments.filter(payment => payment.status === 'pending').length
  const approvedPayments = userPayments.filter(payment => payment.status === 'approved').length
  
  // Calculate portfolio value (shares * current price)
  const currentSharePrice = currentPhase?.price_per_share || 1
  const portfolioValue = totalShares * currentSharePrice
  const unrealizedGains = portfolioValue - totalInvested
  const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0

  // Calculate commission statistics
  const totalCommissionEarned = commissionBalance?.total_earned || 0
  const availableCommission = commissionBalance?.usdt_balance || 0
  const shareCommission = commissionBalance?.share_balance || 0

  const stats = [
    {
      title: 'Total Shares',
      value: totalShares.toLocaleString(),
      subtitle: `Worth ${portfolioValue.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}`,
      icon: '📈',
      color: 'yellow',
      trend: unrealizedGains > 0 ? 'up' : unrealizedGains < 0 ? 'down' : 'neutral',
      trendValue: `${unrealizedGainsPercent >= 0 ? '+' : ''}${unrealizedGainsPercent.toFixed(1)}%`,
      link: '/dashboard/shares'
    },
    {
      title: 'Total Invested',
      value: totalInvested.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
      subtitle: `${approvedPayments} successful investments`,
      icon: '💰',
      color: 'green',
      trend: 'neutral',
      trendValue: `${approvedPayments} payments`,
      link: '/dashboard/payments'
    },
    {
      title: 'Commission Balance',
      value: availableCommission.toLocaleString('en-US', { style: 'currency', currency: 'USD' }),
      subtitle: `${shareCommission.toLocaleString()} shares earned`,
      icon: '🤝',
      color: 'blue',
      trend: totalCommissionEarned > 0 ? 'up' : 'neutral',
      trendValue: `${totalCommissionEarned.toLocaleString('en-US', { style: 'currency', currency: 'USD' })} total`,
      link: '/dashboard/referrals'
    },
    {
      title: 'Current Phase',
      value: currentPhase?.phase_name || 'Phase 1',
      subtitle: `${currentSharePrice.toLocaleString('en-US', { style: 'currency', currency: 'USD' })} per share`,
      icon: '🚀',
      color: 'purple',
      trend: 'up',
      trendValue: `${((currentPhase?.shares_sold || 0) / (currentPhase?.shares_available || 1) * 100).toFixed(1)}% sold`,
      link: '/dashboard/purchase'
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      yellow: 'border-yellow-500 bg-yellow-50',
      green: 'border-green-500 bg-green-50',
      blue: 'border-blue-500 bg-blue-50',
      purple: 'border-purple-500 bg-purple-50'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.yellow
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return '📈'
      case 'down':
        return '📉'
      default:
        return '➡️'
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {stats.map((stat, index) => (
        <Card key={index} className={`border-t-4 ${getColorClasses(stat.color)} hover:shadow-lg transition-shadow duration-200`}>
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{stat.icon}</span>
                <h3 className="font-semibold text-gray-800 text-sm">{stat.title}</h3>
              </div>
              {pendingPayments > 0 && stat.title === 'Total Invested' && (
                <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {pendingPayments}
                </span>
              )}
            </div>

            {/* Main Value */}
            <div className="mb-2">
              <div className="text-2xl font-bold text-gray-800 mb-1">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600">
                {stat.subtitle}
              </div>
            </div>

            {/* Trend */}
            <div className="flex items-center justify-between">
              <div className={`flex items-center space-x-1 text-xs ${getTrendColor(stat.trend)}`}>
                <span>{getTrendIcon(stat.trend)}</span>
                <span>{stat.trendValue}</span>
              </div>
              
              <a 
                href={stat.link}
                className="text-blue-600 hover:text-blue-800 text-xs font-medium transition-colors duration-200"
              >
                View details →
              </a>
            </div>
          </div>
        </Card>
      ))}

      {/* Additional Stats Row */}
      <div className="col-span-full grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
        {/* Portfolio Performance */}
        <Card className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-green-800 text-sm">Portfolio Performance</h4>
              <div className="text-lg font-bold text-green-600">
                {unrealizedGains >= 0 ? '+' : ''}{unrealizedGains.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
              </div>
              <div className="text-xs text-green-600">
                {unrealizedGainsPercent >= 0 ? '+' : ''}{unrealizedGainsPercent.toFixed(2)}% unrealized
              </div>
            </div>
            <div className="text-2xl">
              {unrealizedGains >= 0 ? '📈' : '📉'}
            </div>
          </div>
        </Card>

        {/* Phase Progress */}
        <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-800 text-sm">Phase Progress</h4>
              <div className="text-lg font-bold text-blue-600">
                {((currentPhase?.shares_sold || 0) / (currentPhase?.shares_available || 1) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-blue-600">
                {(currentPhase?.shares_available - currentPhase?.shares_sold || 0).toLocaleString()} remaining
              </div>
            </div>
            <div className="text-2xl">🎯</div>
          </div>
          <div className="mt-2">
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ 
                  width: `${Math.min((currentPhase?.shares_sold || 0) / (currentPhase?.shares_available || 1) * 100, 100)}%` 
                }}
              ></div>
            </div>
          </div>
        </Card>

        {/* Account Status */}
        <Card className="p-4 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-purple-800 text-sm">Account Status</h4>
              <div className="text-lg font-bold text-purple-600">
                {pendingPayments > 0 ? 'Pending' : 'Active'}
              </div>
              <div className="text-xs text-purple-600">
                {pendingPayments > 0 ? `${pendingPayments} pending payments` : 'All payments processed'}
              </div>
            </div>
            <div className="text-2xl">
              {pendingPayments > 0 ? '⏳' : '✅'}
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
