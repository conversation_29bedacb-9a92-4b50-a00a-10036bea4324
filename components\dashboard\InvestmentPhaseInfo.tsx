'use client'

import { Card } from '@/components/ui/Card'

interface InvestmentPhaseInfoProps {
  currentPhase: any
  className?: string
}

export default function InvestmentPhaseInfo({
  currentPhase,
  className
}: InvestmentPhaseInfoProps) {

  if (!currentPhase) {
    return (
      <Card className={`p-6 ${className}`}>
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span className="mr-2">🚀</span>
          Investment Phase
        </h2>
        
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">⏳</div>
          <p className="text-gray-600">Loading phase information...</p>
        </div>
      </Card>
    )
  }

  const sharesSold = currentPhase.shares_sold || 0
  const sharesAvailable = currentPhase.shares_available || 1
  const progressPercentage = (sharesSold / sharesAvailable) * 100
  const remainingShares = sharesAvailable - sharesSold
  const pricePerShare = currentPhase.price_per_share || 1

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getPhaseColor = (phaseNumber: number) => {
    const colors = [
      'from-blue-500 to-blue-600',
      'from-green-500 to-green-600', 
      'from-purple-500 to-purple-600',
      'from-orange-500 to-orange-600',
      'from-red-500 to-red-600'
    ]
    return colors[(phaseNumber - 1) % colors.length] || colors[0]
  }

  const getUrgencyLevel = (percentage: number) => {
    if (percentage >= 90) return { level: 'critical', color: 'text-red-600', message: 'Almost sold out!' }
    if (percentage >= 75) return { level: 'high', color: 'text-orange-600', message: 'Limited availability' }
    if (percentage >= 50) return { level: 'medium', color: 'text-yellow-600', message: 'Good availability' }
    return { level: 'low', color: 'text-green-600', message: 'Plenty available' }
  }

  const urgency = getUrgencyLevel(progressPercentage)

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <span className="mr-2">🚀</span>
          Current Investment Phase
        </h2>
        <a
          href="/dashboard/purchase"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
        >
          Purchase Now →
        </a>
      </div>

      {/* Phase Header */}
      <div className={`bg-gradient-to-r ${getPhaseColor(currentPhase.phase_number)} text-white rounded-lg p-6 mb-6`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-2xl font-bold">{currentPhase.phase_name}</h3>
            <p className="text-blue-100">Investment Phase {currentPhase.phase_number}</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{formatCurrency(pricePerShare)}</div>
            <div className="text-blue-100 text-sm">per share</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-blue-100 mb-2">
            <span>Progress</span>
            <span>{progressPercentage.toFixed(1)}% sold</span>
          </div>
          <div className="w-full bg-blue-400 bg-opacity-30 rounded-full h-3">
            <div 
              className="bg-white h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${Math.min(progressPercentage, 100)}%` }}
            ></div>
          </div>
        </div>

        <div className="flex justify-between text-sm text-blue-100">
          <span>{sharesSold.toLocaleString()} sold</span>
          <span>{remainingShares.toLocaleString()} remaining</span>
        </div>
      </div>

      {/* Phase Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <span className="mr-2">📊</span>
            Phase Statistics
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Shares:</span>
              <span className="font-medium">{sharesAvailable.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Shares Sold:</span>
              <span className="font-medium">{sharesSold.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Remaining:</span>
              <span className="font-medium">{remainingShares.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Progress:</span>
              <span className={`font-medium ${urgency.color}`}>
                {progressPercentage.toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <span className="mr-2">💰</span>
            Investment Info
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Price per Share:</span>
              <span className="font-medium">{formatCurrency(pricePerShare)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Minimum Purchase:</span>
              <span className="font-medium">$25</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Maximum Purchase:</span>
              <span className="font-medium">$10,000</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className={`font-medium ${urgency.color}`}>
                {urgency.message}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Urgency Alert */}
      {urgency.level === 'critical' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-3">
            <span className="text-red-600 text-xl">🚨</span>
            <div>
              <h4 className="font-medium text-red-800">Phase Almost Complete!</h4>
              <p className="text-red-700 text-sm">
                Only {remainingShares.toLocaleString()} shares remaining. Price will increase in the next phase.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Next Phase Preview */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
          <span className="mr-2">🔮</span>
          Next Phase Preview
        </h4>
        <div className="text-sm text-blue-700">
          <p className="mb-2">
            <strong>Phase {currentPhase.phase_number + 1}:</strong> Price will increase to approximately{' '}
            <span className="font-semibold">{formatCurrency(pricePerShare * 1.25)}</span> per share
          </p>
          <p className="text-blue-600">
            💡 Purchase now to lock in the current lower price!
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <a
          href="/dashboard/purchase"
          className="flex-1 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          💰 Purchase Shares Now
        </a>
        <a
          href="/calculator"
          className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          🧮 Calculate Returns
        </a>
      </div>

      {/* Phase Benefits */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h4 className="font-semibold text-green-800 mb-2">✨ Phase Benefits</h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span>Lock in current price before next phase increase</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span>Start earning dividends immediately after purchase</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span>Benefit from gold mining operations and profits</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span>Earn 15% commission on referrals</span>
          </li>
        </ul>
      </div>
    </Card>
  )
}
