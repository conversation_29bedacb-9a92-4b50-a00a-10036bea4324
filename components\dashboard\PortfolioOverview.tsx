'use client'

import { Card } from '@/components/ui/Card'

interface PortfolioOverviewProps {
  userShares: any[]
  currentPhase: any
  commissionBalance: any
  className?: string
}

export default function PortfolioOverview({
  userShares,
  currentPhase,
  commissionBalance,
  className
}: PortfolioOverviewProps) {

  // Calculate portfolio metrics
  const totalShares = userShares.reduce((total, share) => total + (share.shares_purchased || 0), 0)
  const totalInvested = userShares.reduce((total, share) => total + parseFloat(share.total_amount || 0), 0)
  const currentSharePrice = currentPhase?.price_per_share || 1
  const portfolioValue = totalShares * currentSharePrice
  const unrealizedGains = portfolioValue - totalInvested
  const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0

  // Calculate expected annual dividends (assuming 25% annual ROI)
  const expectedAnnualDividends = totalShares * 0.25 // $0.25 per share annually
  const monthlyDividends = expectedAnnualDividends / 12

  // Commission data
  const totalCommissionEarned = commissionBalance?.total_earned || 0
  const availableCommission = commissionBalance?.usdt_balance || 0
  const shareCommission = commissionBalance?.share_balance || 0

  // Portfolio breakdown by package
  const packageBreakdown = userShares.reduce((acc, share) => {
    const packageName = share.package_name || 'Unknown'
    if (!acc[packageName]) {
      acc[packageName] = {
        shares: 0,
        invested: 0,
        count: 0
      }
    }
    acc[packageName].shares += share.shares_purchased || 0
    acc[packageName].invested += parseFloat(share.total_amount || 0)
    acc[packageName].count += 1
    return acc
  }, {} as Record<string, { shares: number; invested: number; count: number }>)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getPerformanceColor = (value: number) => {
    if (value > 0) return 'text-green-600'
    if (value < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getPerformanceIcon = (value: number) => {
    if (value > 0) return '📈'
    if (value < 0) return '📉'
    return '➡️'
  }

  if (totalShares === 0) {
    return (
      <Card className={`p-6 ${className}`}>
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span className="mr-2">📊</span>
          Portfolio Overview
        </h2>
        
        <div className="text-center py-12">
          <div className="text-gray-400 text-4xl mb-4">📈</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">No Shares Yet</h3>
          <p className="text-gray-600 text-sm mb-6">
            Start building your gold mining portfolio today
          </p>
          <div className="space-y-3">
            <a
              href="/dashboard/purchase"
              className="inline-flex items-center px-6 py-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors duration-200 font-medium"
            >
              <span className="mr-2">💰</span>
              Purchase Shares
            </a>
            <div className="text-xs text-gray-500">
              Current price: {formatCurrency(currentSharePrice)} per share
            </div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <span className="mr-2">📊</span>
          Portfolio Overview
        </h2>
        <a
          href="/dashboard/shares"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
        >
          View Details →
        </a>
      </div>

      {/* Portfolio Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-yellow-600 text-xl">📈</span>
            <span className="text-xs text-yellow-600 font-medium">SHARES</span>
          </div>
          <div className="text-2xl font-bold text-yellow-800 mb-1">
            {totalShares.toLocaleString()}
          </div>
          <div className="text-sm text-yellow-600">
            Current Value: {formatCurrency(portfolioValue)}
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-green-600 text-xl">💰</span>
            <span className="text-xs text-green-600 font-medium">INVESTED</span>
          </div>
          <div className="text-2xl font-bold text-green-800 mb-1">
            {formatCurrency(totalInvested)}
          </div>
          <div className="text-sm text-green-600">
            Total Investment
          </div>
        </div>

        <div className={`bg-gradient-to-r ${unrealizedGains >= 0 ? 'from-blue-50 to-indigo-50 border-blue-200' : 'from-red-50 to-pink-50 border-red-200'} border rounded-lg p-4`}>
          <div className="flex items-center justify-between mb-2">
            <span className={`text-xl ${unrealizedGains >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
              {getPerformanceIcon(unrealizedGains)}
            </span>
            <span className={`text-xs font-medium ${unrealizedGains >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
              P&L
            </span>
          </div>
          <div className={`text-2xl font-bold mb-1 ${getPerformanceColor(unrealizedGains)}`}>
            {unrealizedGains >= 0 ? '+' : ''}{formatCurrency(unrealizedGains)}
          </div>
          <div className={`text-sm ${getPerformanceColor(unrealizedGains)}`}>
            {unrealizedGainsPercent >= 0 ? '+' : ''}{unrealizedGainsPercent.toFixed(2)}% unrealized
          </div>
        </div>
      </div>

      {/* Expected Returns */}
      <div className="bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-purple-800 mb-3 flex items-center">
          <span className="mr-2">💎</span>
          Expected Returns
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-lg font-bold text-purple-600">
              {formatCurrency(monthlyDividends)}
            </div>
            <div className="text-sm text-purple-600">Monthly Dividends</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-600">
              {formatCurrency(expectedAnnualDividends)}
            </div>
            <div className="text-sm text-purple-600">Annual Dividends</div>
          </div>
        </div>
        <div className="text-xs text-purple-500 mt-2">
          * Based on 25% annual ROI estimate
        </div>
      </div>

      {/* Package Breakdown */}
      {Object.keys(packageBreakdown).length > 0 && (
        <div className="mb-6">
          <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
            <span className="mr-2">📦</span>
            Package Breakdown
          </h3>
          <div className="space-y-2">
            {Object.entries(packageBreakdown).map(([packageName, data]) => (
              <div key={packageName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-800">{packageName}</div>
                  <div className="text-sm text-gray-600">
                    {data.count} purchase{data.count > 1 ? 's' : ''}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-gray-800">
                    {data.shares.toLocaleString()} shares
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatCurrency(data.invested)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Commission Summary */}
      {totalCommissionEarned > 0 && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-green-800 mb-3 flex items-center">
            <span className="mr-2">🤝</span>
            Referral Commission
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <div className="text-lg font-bold text-green-600">
                {formatCurrency(totalCommissionEarned)}
              </div>
              <div className="text-sm text-green-600">Total Earned</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {formatCurrency(availableCommission)}
              </div>
              <div className="text-sm text-green-600">Available</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {shareCommission.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Share Commission</div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3">
        <a
          href="/dashboard/purchase"
          className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center"
        >
          <span className="mr-2">💰</span>
          Buy More Shares
        </a>
        <a
          href="/dashboard/referrals"
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center"
        >
          <span className="mr-2">🤝</span>
          Earn Commission
        </a>
        <a
          href="/calculator"
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center"
        >
          <span className="mr-2">🧮</span>
          Calculate Returns
        </a>
      </div>
    </Card>
  )
}
