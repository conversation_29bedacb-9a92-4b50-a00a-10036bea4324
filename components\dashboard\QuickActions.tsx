'use client'

import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface QuickActionsProps {
  pendingPayments: number
  hasShares: boolean
  hasKYC: boolean
  className?: string
}

export default function QuickActions({
  pendingPayments,
  hasShares,
  hasKYC,
  className
}: QuickActionsProps) {

  const primaryActions = [
    {
      title: 'Purchase Shares',
      description: 'Buy gold mining shares',
      icon: '💰',
      href: '/dashboard/purchase',
      color: 'bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
      priority: 'high'
    },
    {
      title: 'Payment History',
      description: 'View transaction history',
      icon: '📊',
      href: '/dashboard/payments',
      color: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
      badge: pendingPayments > 0 ? pendingPayments : null,
      priority: 'medium'
    },
    {
      title: 'KYC Documents',
      description: 'Complete verification',
      icon: '📄',
      href: '/dashboard/kyc',
      color: hasKYC 
        ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
        : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
      priority: hasKYC ? 'low' : 'high'
    },
    {
      title: 'My Referrals',
      description: 'Earn commission',
      icon: '🤝',
      href: '/dashboard/referrals',
      color: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
      priority: 'medium'
    }
  ]

  const secondaryActions = [
    {
      title: 'Share Portfolio',
      description: 'View your shares',
      icon: '📈',
      href: '/dashboard/shares',
      enabled: hasShares
    },
    {
      title: 'Financial Calculator',
      description: 'Calculate returns',
      icon: '🧮',
      href: '/calculator',
      enabled: true
    },
    {
      title: 'Mining Operations',
      description: 'View mining data',
      icon: '⛏️',
      href: '/mining',
      enabled: true
    },
    {
      title: 'Support Center',
      description: 'Get help',
      icon: '💬',
      href: 'https://t.me/AureusAllianceBot',
      enabled: true,
      external: true
    }
  ]

  const getPriorityOrder = (priority: string) => {
    switch (priority) {
      case 'high': return 1
      case 'medium': return 2
      case 'low': return 3
      default: return 4
    }
  }

  const sortedPrimaryActions = primaryActions.sort((a, b) => 
    getPriorityOrder(a.priority) - getPriorityOrder(b.priority)
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Primary Actions */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span className="mr-2">⚡</span>
          Quick Actions
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {sortedPrimaryActions.map((action, index) => (
            <a
              key={index}
              href={action.href}
              className={`${action.color} text-white p-4 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl relative overflow-hidden group`}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-white opacity-10 transform -skew-y-6 group-hover:skew-y-6 transition-transform duration-300"></div>
              
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{action.icon}</span>
                  <div>
                    <div className="font-semibold">{action.title}</div>
                    <div className="text-sm opacity-90">{action.description}</div>
                  </div>
                </div>
                
                {action.badge && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                    {action.badge}
                  </span>
                )}
              </div>
            </a>
          ))}
        </div>
      </Card>

      {/* Secondary Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <span className="mr-2">🔧</span>
          Additional Tools
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {secondaryActions.map((action, index) => (
            <a
              key={index}
              href={action.href}
              target={action.external ? '_blank' : '_self'}
              rel={action.external ? 'noopener noreferrer' : undefined}
              className={`p-3 rounded-lg border-2 transition-all duration-200 text-center ${
                action.enabled
                  ? 'border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700 hover:text-blue-700'
                  : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
              }`}
            >
              <div className="text-2xl mb-2">{action.icon}</div>
              <div className="font-medium text-sm">{action.title}</div>
              <div className="text-xs opacity-75">{action.description}</div>
              {action.external && (
                <div className="text-xs text-blue-500 mt-1">↗ External</div>
              )}
            </a>
          ))}
        </div>
      </Card>

      {/* Status Alerts */}
      <div className="space-y-3">
        {!hasKYC && (
          <Card className="p-4 bg-yellow-50 border border-yellow-200">
            <div className="flex items-center space-x-3">
              <span className="text-yellow-600 text-xl">⚠️</span>
              <div className="flex-1">
                <h4 className="font-medium text-yellow-800">KYC Verification Required</h4>
                <p className="text-yellow-700 text-sm">
                  Complete your KYC verification to access all platform features.
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/dashboard/kyc'}
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Complete KYC
              </Button>
            </div>
          </Card>
        )}

        {pendingPayments > 0 && (
          <Card className="p-4 bg-blue-50 border border-blue-200">
            <div className="flex items-center space-x-3">
              <span className="text-blue-600 text-xl">⏳</span>
              <div className="flex-1">
                <h4 className="font-medium text-blue-800">Pending Payments</h4>
                <p className="text-blue-700 text-sm">
                  You have {pendingPayments} payment{pendingPayments > 1 ? 's' : ''} awaiting approval.
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/dashboard/payments'}
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                View Payments
              </Button>
            </div>
          </Card>
        )}

        {hasShares && hasKYC && pendingPayments === 0 && (
          <Card className="p-4 bg-green-50 border border-green-200">
            <div className="flex items-center space-x-3">
              <span className="text-green-600 text-xl">✅</span>
              <div className="flex-1">
                <h4 className="font-medium text-green-800">Account Active</h4>
                <p className="text-green-700 text-sm">
                  Your account is fully verified and active. Start earning with referrals!
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/dashboard/referrals'}
                className="border-green-300 text-green-700 hover:bg-green-100"
              >
                Share & Earn
              </Button>
            </div>
          </Card>
        )}
      </div>

      {/* Help Section */}
      <Card className="p-4 bg-gray-50 border border-gray-200">
        <div className="flex items-center space-x-3">
          <span className="text-gray-600 text-xl">💡</span>
          <div className="flex-1">
            <h4 className="font-medium text-gray-800">Need Help?</h4>
            <p className="text-gray-600 text-sm">
              Contact our support team for assistance with your account or investments.
            </p>
          </div>
          <div className="flex space-x-2">
            <a
              href="https://t.me/AureusAllianceBot"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
            >
              📱 Telegram
            </a>
            <a
              href="mailto:<EMAIL>"
              className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
            >
              ✉️ Email
            </a>
          </div>
        </div>
      </Card>
    </div>
  )
}
