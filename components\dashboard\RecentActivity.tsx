'use client'

import { Card } from '@/components/ui/Card'

interface RecentActivityProps {
  userShares: any[]
  userPayments: any[]
  commissionHistory?: any[]
  className?: string
}

interface ActivityItem {
  id: string
  type: 'share_purchase' | 'payment' | 'commission' | 'kyc' | 'referral'
  title: string
  description: string
  amount?: number
  status: 'completed' | 'pending' | 'failed' | 'approved' | 'rejected'
  date: string
  icon: string
}

export default function RecentActivity({
  userShares,
  userPayments,
  commissionHistory = [],
  className
}: RecentActivityProps) {

  // Combine all activities into a single timeline
  const activities: ActivityItem[] = []

  // Add share purchases
  userShares.forEach(share => {
    activities.push({
      id: `share_${share.id}`,
      type: 'share_purchase',
      title: 'Share Purchase',
      description: `Purchased ${share.shares_purchased?.toLocaleString()} shares (${share.package_name})`,
      amount: parseFloat(share.total_amount || 0),
      status: share.status || 'completed',
      date: share.created_at,
      icon: '📈'
    })
  })

  // Add payments
  userPayments.forEach(payment => {
    activities.push({
      id: `payment_${payment.id}`,
      type: 'payment',
      title: 'Payment Transaction',
      description: `${payment.currency} payment via ${payment.network || payment.payment_method}`,
      amount: parseFloat(payment.amount || 0),
      status: payment.status,
      date: payment.created_at,
      icon: '💳'
    })
  })

  // Add commission history
  commissionHistory.forEach(commission => {
    activities.push({
      id: `commission_${commission.id}`,
      type: 'commission',
      title: 'Commission Earned',
      description: `Referral commission from investment`,
      amount: commission.usdt_commission || commission.amount,
      status: commission.status || 'completed',
      date: commission.created_at,
      icon: '🤝'
    })
  })

  // Sort by date (most recent first)
  const sortedActivities = activities
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 10) // Show only last 10 activities

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return 'text-green-600 bg-green-50'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50'
      case 'failed':
      case 'rejected':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed'
      case 'approved':
        return 'Approved'
      case 'pending':
        return 'Pending'
      case 'failed':
        return 'Failed'
      case 'rejected':
        return 'Rejected'
      default:
        return status
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) {
      return 'Today'
    } else if (diffDays === 2) {
      return 'Yesterday'
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      })
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (sortedActivities.length === 0) {
    return (
      <Card className={`p-6 ${className}`}>
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <span className="mr-2">📋</span>
          Recent Activity
        </h2>
        
        <div className="text-center py-12">
          <div className="text-gray-400 text-4xl mb-4">📊</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">No Activity Yet</h3>
          <p className="text-gray-600 text-sm mb-4">
            Your recent transactions and activities will appear here
          </p>
          <a
            href="/dashboard/purchase"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >
            <span className="mr-2">💰</span>
            Make Your First Purchase
          </a>
        </div>
      </Card>
    )
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <span className="mr-2">📋</span>
          Recent Activity
        </h2>
        <a
          href="/dashboard/payments"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
        >
          View All →
        </a>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {sortedActivities.map((activity, index) => (
          <div
            key={activity.id}
            className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            {/* Icon */}
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-lg">{activity.icon}</span>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className="font-medium text-gray-800 text-sm">
                  {activity.title}
                </h3>
                <div className="flex items-center space-x-2">
                  {activity.amount && (
                    <span className="font-semibold text-gray-800 text-sm">
                      {formatCurrency(activity.amount)}
                    </span>
                  )}
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                    {getStatusText(activity.status)}
                  </span>
                </div>
              </div>
              
              <p className="text-gray-600 text-sm mb-2">
                {activity.description}
              </p>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-500 text-xs">
                  {formatDate(activity.date)}
                </span>
                
                {/* Activity-specific actions */}
                {activity.type === 'payment' && activity.status === 'pending' && (
                  <a
                    href="/dashboard/payments"
                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    View Details
                  </a>
                )}
                
                {activity.type === 'share_purchase' && (
                  <a
                    href="/dashboard/shares"
                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    View Shares
                  </a>
                )}
                
                {activity.type === 'commission' && (
                  <a
                    href="/dashboard/referrals"
                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                  >
                    View Referrals
                  </a>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Activity Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {userShares.length}
            </div>
            <div className="text-xs text-gray-600">Share Purchases</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {userPayments.filter(p => p.status === 'approved').length}
            </div>
            <div className="text-xs text-gray-600">Approved Payments</div>
          </div>
          <div>
            <div className="text-lg font-semibold text-gray-800">
              {commissionHistory.length}
            </div>
            <div className="text-xs text-gray-600">Commission Earned</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-4 flex justify-center space-x-3">
        <a
          href="/dashboard/purchase"
          className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
        >
          💰 Purchase More
        </a>
        <a
          href="/dashboard/referrals"
          className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
        >
          🤝 Refer Friends
        </a>
      </div>
    </Card>
  )
}
