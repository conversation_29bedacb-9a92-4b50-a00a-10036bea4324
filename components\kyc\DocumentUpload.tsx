'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'

interface RequiredDocument {
  type: string
  name: string
  description: string
  required: boolean
}

interface ExistingDocument {
  id: string
  document_type: string
  file_name: string
  status: 'pending' | 'approved' | 'rejected'
}

interface DocumentUploadProps {
  requiredDocuments: RequiredDocument[]
  existingDocuments: ExistingDocument[]
  onUpload: (documentType: string, file: File) => Promise<void>
  uploading: boolean
}

export default function DocumentUpload({
  requiredDocuments,
  existingDocuments,
  onUpload,
  uploading
}: DocumentUploadProps) {
  const [dragOver, setDragOver] = useState<string | null>(null)
  const [selectedFiles, setSelectedFiles] = useState<{ [key: string]: File }>({})
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({})

  const handleFileSelect = (documentType: string, file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      alert('Please upload only JPG, PNG, or PDF files')
      return
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      alert('File size must be less than 10MB')
      return
    }

    setSelectedFiles(prev => ({
      ...prev,
      [documentType]: file
    }))
  }

  const handleDrop = (e: React.DragEvent, documentType: string) => {
    e.preventDefault()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(documentType, files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent, documentType: string) => {
    e.preventDefault()
    setDragOver(documentType)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(null)
  }

  const handleUpload = async (documentType: string) => {
    const file = selectedFiles[documentType]
    if (!file) return

    try {
      await onUpload(documentType, file)
      setSelectedFiles(prev => {
        const newFiles = { ...prev }
        delete newFiles[documentType]
        return newFiles
      })
    } catch (error) {
      console.error('Upload error:', error)
    }
  }

  const getDocumentStatus = (documentType: string) => {
    const existing = existingDocuments.find(doc => doc.document_type === documentType)
    return existing?.status || null
  }

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'approved':
        return <span className="text-green-500 text-xl">✅</span>
      case 'pending':
        return <span className="text-yellow-500 text-xl">⏳</span>
      case 'rejected':
        return <span className="text-red-500 text-xl">❌</span>
      default:
        return <span className="text-gray-400 text-xl">📄</span>
    }
  }

  const getStatusText = (status: string | null) => {
    switch (status) {
      case 'approved':
        return 'Approved'
      case 'pending':
        return 'Under Review'
      case 'rejected':
        return 'Rejected - Please reupload'
      default:
        return 'Not uploaded'
    }
  }

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'approved':
        return 'text-green-600'
      case 'pending':
        return 'text-yellow-600'
      case 'rejected':
        return 'text-red-600'
      default:
        return 'text-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {requiredDocuments.map((doc) => {
        const status = getDocumentStatus(doc.type)
        const selectedFile = selectedFiles[doc.type]
        const isUploaded = status === 'approved'
        const canUpload = !isUploaded || status === 'rejected'

        return (
          <Card key={doc.type} className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getStatusIcon(status)}
                <div>
                  <h3 className="font-semibold text-gray-800">
                    {doc.name}
                    {doc.required && <span className="text-red-500 ml-1">*</span>}
                  </h3>
                  <p className="text-sm text-gray-600">{doc.description}</p>
                </div>
              </div>
              <div className="text-right">
                <span className={`text-sm font-medium ${getStatusColor(status)}`}>
                  {getStatusText(status)}
                </span>
              </div>
            </div>

            {canUpload && (
              <div className="space-y-4">
                {/* File Drop Zone */}
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200 ${
                    dragOver === doc.type
                      ? 'border-blue-500 bg-blue-50'
                      : selectedFile
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDrop={(e) => handleDrop(e, doc.type)}
                  onDragOver={(e) => handleDragOver(e, doc.type)}
                  onDragLeave={handleDragLeave}
                >
                  {selectedFile ? (
                    <div className="space-y-2">
                      <div className="text-green-600 text-2xl">📎</div>
                      <p className="font-medium text-gray-800">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="text-gray-400 text-4xl">📁</div>
                      <p className="text-gray-600">
                        Drag and drop your file here, or{' '}
                        <button
                          type="button"
                          onClick={() => fileInputRefs.current[doc.type]?.click()}
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          browse
                        </button>
                      </p>
                      <p className="text-xs text-gray-500">
                        Supported formats: JPG, PNG, PDF (max 10MB)
                      </p>
                    </div>
                  )}
                </div>

                {/* Hidden File Input */}
                <input
                  ref={(el) => (fileInputRefs.current[doc.type] = el)}
                  type="file"
                  accept=".jpg,.jpeg,.png,.pdf"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      handleFileSelect(doc.type, file)
                    }
                  }}
                  className="hidden"
                />

                {/* Upload Actions */}
                {selectedFile && (
                  <div className="flex justify-between items-center">
                    <button
                      type="button"
                      onClick={() => {
                        setSelectedFiles(prev => {
                          const newFiles = { ...prev }
                          delete newFiles[doc.type]
                          return newFiles
                        })
                      }}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Remove file
                    </button>
                    
                    <Button
                      onClick={() => handleUpload(doc.type)}
                      disabled={uploading}
                      variant="primary"
                      size="sm"
                    >
                      {uploading ? 'Uploading...' : 'Upload Document'}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {isUploaded && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center text-green-800">
                  <span className="mr-2">✅</span>
                  <span className="font-medium">Document approved and verified</span>
                </div>
              </div>
            )}

            {status === 'rejected' && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center text-red-800">
                  <span className="mr-2">❌</span>
                  <span className="font-medium">Document rejected - please upload a new document</span>
                </div>
              </div>
            )}
          </Card>
        )
      })}

      {/* Upload Guidelines */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-3">Document Upload Guidelines</h3>
        <ul className="text-sm text-blue-700 space-y-2">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Ensure all documents are clear, legible, and in color</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Documents should not be older than 3 months (for proof of address)</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>All four corners of the document must be visible</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>For selfie with ID: hold your ID next to your face, both should be clearly visible</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Maximum file size: 10MB per document</span>
          </li>
        </ul>
      </Card>
    </div>
  )
}
