'use client'

import { StepProgress } from '@/components/ui/Progress'

interface KYCInfo {
  id: string
  kyc_status: 'pending' | 'approved' | 'rejected' | 'incomplete'
  first_name: string
  last_name: string
  created_at: string
  verified_at?: string
}

interface KYCDocument {
  id: string
  document_type: string
  status: 'pending' | 'approved' | 'rejected'
}

interface RequiredDocument {
  type: string
  name: string
  required: boolean
}

interface KYCStatusTrackerProps {
  kycInfo: KYCInfo | null
  documents: KYCDocument[]
  requiredDocuments: RequiredDocument[]
}

export default function KYCStatusTracker({
  kycInfo,
  documents,
  requiredDocuments
}: KYCStatusTrackerProps) {
  
  const getDocumentStatus = (documentType: string) => {
    const doc = documents.find(d => d.document_type === documentType)
    return doc?.status || 'not_uploaded'
  }

  const getOverallProgress = () => {
    if (!kycInfo) {
      return {
        percentage: 0,
        status: 'not_started',
        message: 'KYC verification not started'
      }
    }

    const requiredDocs = requiredDocuments.filter(doc => doc.required)
    const uploadedDocs = requiredDocs.filter(doc => 
      getDocumentStatus(doc.type) !== 'not_uploaded'
    )
    const approvedDocs = requiredDocs.filter(doc => 
      getDocumentStatus(doc.type) === 'approved'
    )

    let percentage = 0
    let status = 'in_progress'
    let message = 'KYC verification in progress'

    if (kycInfo.kyc_status === 'approved') {
      percentage = 100
      status = 'completed'
      message = 'KYC verification completed successfully'
    } else if (kycInfo.kyc_status === 'rejected') {
      percentage = 25
      status = 'rejected'
      message = 'KYC verification rejected - please resubmit documents'
    } else if (kycInfo.kyc_status === 'pending') {
      if (approvedDocs.length === requiredDocs.length) {
        percentage = 90
        message = 'All documents approved - final review in progress'
      } else if (uploadedDocs.length === requiredDocs.length) {
        percentage = 75
        message = 'All documents uploaded - under review'
      } else {
        percentage = 50
        message = 'Some documents uploaded - please complete all uploads'
      }
    } else {
      percentage = Math.round((uploadedDocs.length / requiredDocs.length) * 50)
      message = `${uploadedDocs.length} of ${requiredDocs.length} documents uploaded`
    }

    return { percentage, status, message }
  }

  const getStepStatus = () => {
    const progress = getOverallProgress()
    
    const steps = [
      {
        id: 'personal_info',
        title: 'Personal Information',
        description: 'Basic information submitted',
        completed: !!kycInfo
      },
      {
        id: 'document_upload',
        title: 'Document Upload',
        description: 'Required documents uploaded',
        completed: requiredDocuments.every(doc => 
          !doc.required || getDocumentStatus(doc.type) !== 'not_uploaded'
        )
      },
      {
        id: 'review',
        title: 'Document Review',
        description: 'Documents under admin review',
        completed: kycInfo?.kyc_status === 'pending' && 
          requiredDocuments.every(doc => 
            !doc.required || ['approved', 'pending'].includes(getDocumentStatus(doc.type))
          )
      },
      {
        id: 'approval',
        title: 'Final Approval',
        description: 'KYC verification completed',
        completed: kycInfo?.kyc_status === 'approved'
      }
    ]

    return steps
  }

  const progress = getOverallProgress()
  const steps = getStepStatus()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600'
      case 'rejected':
        return 'text-red-600'
      case 'in_progress':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return '✅'
      case 'rejected':
        return '❌'
      case 'in_progress':
        return '⏳'
      default:
        return '📋'
    }
  }

  return (
    <div className="space-y-6">
      {/* Overall Status */}
      <div className="text-center">
        <div className="text-4xl mb-2">{getStatusIcon(progress.status)}</div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">KYC Verification Status</h2>
        <p className={`text-lg font-medium ${getStatusColor(progress.status)}`}>
          {progress.message}
        </p>
      </div>

      {/* Progress Bar */}
      <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
        <div
          className={`h-full transition-all duration-500 ease-out ${
            progress.status === 'completed'
              ? 'bg-green-500'
              : progress.status === 'rejected'
              ? 'bg-red-500'
              : 'bg-blue-500'
          }`}
          style={{ width: `${progress.percentage}%` }}
        />
      </div>
      <div className="text-center text-sm text-gray-600">
        {progress.percentage}% Complete
      </div>

      {/* Step Progress */}
      <div className="mt-8">
        <StepProgress steps={steps} orientation="vertical" />
      </div>

      {/* Document Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        {requiredDocuments.map((doc) => {
          const status = getDocumentStatus(doc.type)
          
          const getDocStatusIcon = (docStatus: string) => {
            switch (docStatus) {
              case 'approved':
                return '✅'
              case 'pending':
                return '⏳'
              case 'rejected':
                return '❌'
              default:
                return '📄'
            }
          }

          const getDocStatusColor = (docStatus: string) => {
            switch (docStatus) {
              case 'approved':
                return 'text-green-600 bg-green-50 border-green-200'
              case 'pending':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200'
              case 'rejected':
                return 'text-red-600 bg-red-50 border-red-200'
              default:
                return 'text-gray-600 bg-gray-50 border-gray-200'
            }
          }

          const getDocStatusText = (docStatus: string) => {
            switch (docStatus) {
              case 'approved':
                return 'Approved'
              case 'pending':
                return 'Under Review'
              case 'rejected':
                return 'Rejected'
              default:
                return 'Not Uploaded'
            }
          }

          return (
            <div
              key={doc.type}
              className={`border rounded-lg p-4 ${getDocStatusColor(status)}`}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-2xl">{getDocStatusIcon(status)}</span>
                <span className="text-xs font-medium uppercase tracking-wide">
                  {getDocStatusText(status)}
                </span>
              </div>
              <h3 className="font-medium text-gray-800 mb-1">{doc.name}</h3>
              <p className="text-xs opacity-75">
                {doc.required ? 'Required' : 'Optional'}
              </p>
            </div>
          )
        })}
      </div>

      {/* Timeline Information */}
      {kycInfo && (
        <div className="bg-gray-50 rounded-lg p-4 mt-6">
          <h3 className="font-semibold text-gray-800 mb-3">Verification Timeline</h3>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>KYC Started:</span>
              <span>{new Date(kycInfo.created_at).toLocaleDateString()}</span>
            </div>
            {kycInfo.verified_at && (
              <div className="flex justify-between">
                <span>Verified:</span>
                <span>{new Date(kycInfo.verified_at).toLocaleDateString()}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>Expected Completion:</span>
              <span>
                {kycInfo.kyc_status === 'approved' 
                  ? 'Completed' 
                  : '1-3 business days'
                }
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Help Section */}
      {progress.status === 'rejected' && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="font-semibold text-red-800 mb-2">Action Required</h3>
          <p className="text-red-700 text-sm mb-3">
            Your KYC verification was rejected. Please review the feedback and resubmit the required documents.
          </p>
          <div className="text-xs text-red-600">
            <p>Common rejection reasons:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Document image is blurry or unclear</li>
              <li>Document is expired or invalid</li>
              <li>Information doesn't match profile data</li>
              <li>Document corners are cut off</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}
