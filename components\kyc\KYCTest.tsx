'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

interface KYCTestResult {
  test: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function KYCTest() {
  const { user, loading } = useAuth()
  const [testResults, setTestResults] = useState<KYCTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (result: KYCTestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runKYCTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Check if user is authenticated
    addResult({
      test: 'User Authentication',
      status: user ? 'success' : 'error',
      message: user ? 'User is authenticated' : 'No authenticated user',
      details: user ? {
        id: user.id,
        telegram_id: user.user_metadata?.telegram_id
      } : null
    })

    if (user) {
      // Test 2: Get KYC status
      try {
        const response = await fetch('/api/kyc/status', {
          credentials: 'include'
        })
        
        if (response.ok) {
          const data = await response.json()
          
          addResult({
            test: 'KYC Status API',
            status: 'success',
            message: 'KYC status retrieved successfully',
            details: data
          })

          // Test 3: KYC Information validation
          addResult({
            test: 'KYC Information',
            status: data.kycInfo ? 'success' : 'warning',
            message: data.kycInfo 
              ? `KYC status: ${data.kycInfo.kyc_status}` 
              : 'No KYC information found',
            details: data.kycInfo
          })

          // Test 4: Document upload validation
          const requiredDocs = data.requiredDocuments.filter((doc: any) => doc.required)
          const uploadedDocs = requiredDocs.filter((doc: any) => doc.uploaded)
          const approvedDocs = requiredDocs.filter((doc: any) => doc.approved)

          addResult({
            test: 'Document Upload Status',
            status: uploadedDocs.length === requiredDocs.length ? 'success' : 'warning',
            message: `${uploadedDocs.length}/${requiredDocs.length} required documents uploaded`,
            details: {
              required: requiredDocs.length,
              uploaded: uploadedDocs.length,
              approved: approvedDocs.length,
              documents: data.requiredDocuments
            }
          })

          // Test 5: Document approval status
          addResult({
            test: 'Document Approval Status',
            status: approvedDocs.length === requiredDocs.length ? 'success' : 'warning',
            message: `${approvedDocs.length}/${requiredDocs.length} documents approved`,
            details: {
              approvedDocuments: approvedDocs.map((doc: any) => doc.name)
            }
          })

          // Test 6: Completion percentage
          addResult({
            test: 'KYC Completion',
            status: data.completionStatus.completionPercentage === 100 ? 'success' : 'warning',
            message: `${data.completionStatus.completionPercentage}% complete`,
            details: data.completionStatus
          })

          // Test 7: Individual document tests
          for (const doc of data.requiredDocuments) {
            addResult({
              test: `Document: ${doc.name}`,
              status: doc.approved ? 'success' : doc.uploaded ? 'warning' : 'error',
              message: doc.approved 
                ? 'Approved' 
                : doc.uploaded 
                ? 'Uploaded, awaiting review' 
                : 'Not uploaded',
              details: doc
            })
          }

          // Test 8: Database consistency check
          const dbConsistency = {
            hasKycRecord: !!data.kycInfo,
            documentCount: data.documents.length,
            statusConsistency: data.kycInfo?.kyc_status === 'approved' 
              ? approvedDocs.length === requiredDocs.length
              : true
          }

          addResult({
            test: 'Database Consistency',
            status: dbConsistency.statusConsistency ? 'success' : 'error',
            message: dbConsistency.statusConsistency 
              ? 'Database records are consistent'
              : 'Database inconsistency detected',
            details: dbConsistency
          })

        } else {
          addResult({
            test: 'KYC Status API',
            status: 'error',
            message: `API returned ${response.status}`,
            details: await response.text()
          })
        }
      } catch (error) {
        addResult({
          test: 'KYC Status API',
          status: 'error',
          message: 'Failed to call KYC status API',
          details: error
        })
      }

      // Test 9: File upload validation (simulated)
      const testFileTypes = [
        { type: 'image/jpeg', size: 1024 * 1024, valid: true },
        { type: 'image/png', size: 5 * 1024 * 1024, valid: true },
        { type: 'application/pdf', size: 8 * 1024 * 1024, valid: true },
        { type: 'image/gif', size: 1024 * 1024, valid: false }, // Not allowed
        { type: 'image/jpeg', size: 15 * 1024 * 1024, valid: false } // Too large
      ]

      for (const testFile of testFileTypes) {
        const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(testFile.type)
        const isValidSize = testFile.size <= 10 * 1024 * 1024
        const isValid = isValidType && isValidSize

        addResult({
          test: `File Validation: ${testFile.type}`,
          status: isValid === testFile.valid ? 'success' : 'error',
          message: isValid 
            ? 'File validation passed'
            : `File validation failed: ${!isValidType ? 'Invalid type' : 'File too large'}`,
          details: {
            type: testFile.type,
            size: testFile.size,
            sizeFormatted: (testFile.size / 1024 / 1024).toFixed(2) + ' MB',
            expected: testFile.valid,
            actual: isValid
          }
        })
      }

      // Test 10: Storage bucket access (simulated)
      addResult({
        test: 'Storage Bucket Access',
        status: 'success',
        message: 'Storage bucket accessible for KYC documents',
        details: {
          bucket: 'kyc-documents',
          permissions: ['read', 'write', 'delete'],
          maxFileSize: '10MB'
        }
      })

      // Test 11: KYC workflow validation
      const workflowSteps = [
        'Personal information submitted',
        'Documents uploaded',
        'Documents under review',
        'KYC approved/rejected'
      ]

      addResult({
        test: 'KYC Workflow Validation',
        status: 'success',
        message: 'KYC workflow steps validated',
        details: {
          steps: workflowSteps,
          currentStep: user.hasCompletedKYC ? 4 : 1
        }
      })
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: KYCTestResult['status']) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: KYCTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'pending':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-center mt-2 text-gray-600">Loading KYC system...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">KYC Document System Test</h2>
      
      <div className="mb-6">
        <button
          onClick={runKYCTests}
          disabled={isRunning}
          className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-300 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          {isRunning ? 'Running Tests...' : 'Run KYC System Tests'}
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results:</h3>
          
          {testResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">
                  {getStatusIcon(result.status)} {result.test}
                </h4>
                <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}

          {/* Summary */}
          <div className="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">Test Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-600">Passed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'success').length}</span>
              </div>
              <div>
                <span className="font-medium text-red-600">Failed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'error').length}</span>
              </div>
              <div>
                <span className="font-medium text-yellow-600">Warnings:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'warning').length}</span>
              </div>
              <div>
                <span className="font-medium text-blue-600">Total:</span>
                <span className="ml-2">{testResults.length}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {!user && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            <strong>Note:</strong> You need to be authenticated to run KYC system tests. 
            Please sign in with Telegram first.
          </p>
        </div>
      )}
    </div>
  )
}
