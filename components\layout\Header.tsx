'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { EnhancedButton } from '@/components/ui/EnhancedButton'

export default function Header() {
  const { user, loading, signOut } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleSignOut = async () => {
    await signOut()
    setIsMenuOpen(false)
  }

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">A</span>
              </div>
              <div className="hidden sm:block">
                <h1 className={`text-xl font-bold transition-colors duration-300 ${
                  isScrolled ? 'text-gray-900' : 'text-white'
                }`}>
                  Aureus Alliance
                </h1>
                <p className={`text-sm transition-colors duration-300 ${
                  isScrolled ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Holdings
                </p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link 
              href="#about" 
              className={`font-medium transition-colors duration-300 hover:text-yellow-400 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
            >
              About
            </Link>
            <Link 
              href="#calculator" 
              className={`font-medium transition-colors duration-300 hover:text-yellow-400 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
            >
              Calculator
            </Link>
            <Link 
              href="#gallery" 
              className={`font-medium transition-colors duration-300 hover:text-yellow-400 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
            >
              Gallery
            </Link>
            <Link 
              href="#contact" 
              className={`font-medium transition-colors duration-300 hover:text-yellow-400 ${
                isScrolled ? 'text-gray-700' : 'text-white'
              }`}
            >
              Contact
            </Link>
          </nav>

          {/* Auth Section */}
          <div className="hidden lg:flex items-center space-x-4">
            {loading ? (
              <div className="w-8 h-8 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
            ) : user ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user.telegram_profile?.first_name?.[0] || user.email?.[0]?.toUpperCase() || 'U'}
                    </span>
                  </div>
                  <span className={`text-sm font-medium ${
                    isScrolled ? 'text-gray-700' : 'text-white'
                  }`}>
                    {user.telegram_profile?.first_name || 'User'}
                  </span>
                </div>
                <Link href="/dashboard">
                  <EnhancedButton variant="gold" size="sm">
                    Dashboard
                  </EnhancedButton>
                </Link>
                <EnhancedButton 
                  variant="outline" 
                  size="sm" 
                  onClick={handleSignOut}
                  className={isScrolled ? 'border-gray-300 text-gray-700' : 'border-white text-white'}
                >
                  Sign Out
                </EnhancedButton>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link href="/login">
                  <EnhancedButton 
                    variant="outline" 
                    size="sm"
                    className={isScrolled ? 'border-gray-300 text-gray-700' : 'border-white text-white'}
                  >
                    Sign In
                  </EnhancedButton>
                </Link>
                <Link href="/login">
                  <EnhancedButton variant="gold" size="sm">
                    Get Started
                  </EnhancedButton>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`lg:hidden p-2 rounded-md transition-colors duration-300 ${
              isScrolled 
                ? 'text-gray-700 hover:bg-gray-100' 
                : 'text-white hover:bg-white/10'
            }`}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white/95 backdrop-blur-md rounded-lg mt-2 shadow-lg border border-gray-200">
              <Link 
                href="#about" 
                className="block px-3 py-2 text-gray-700 hover:text-yellow-600 hover:bg-gray-50 rounded-md font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </Link>
              <Link 
                href="#calculator" 
                className="block px-3 py-2 text-gray-700 hover:text-yellow-600 hover:bg-gray-50 rounded-md font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Calculator
              </Link>
              <Link 
                href="#gallery" 
                className="block px-3 py-2 text-gray-700 hover:text-yellow-600 hover:bg-gray-50 rounded-md font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Gallery
              </Link>
              <Link 
                href="#contact" 
                className="block px-3 py-2 text-gray-700 hover:text-yellow-600 hover:bg-gray-50 rounded-md font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </Link>
              
              <div className="border-t border-gray-200 pt-3 mt-3">
                {loading ? (
                  <div className="flex justify-center py-2">
                    <div className="w-6 h-6 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                ) : user ? (
                  <div className="space-y-2">
                    <div className="flex items-center px-3 py-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">
                          {user.telegram_profile?.first_name?.[0] || user.email?.[0]?.toUpperCase() || 'U'}
                        </span>
                      </div>
                      <span className="text-gray-700 font-medium">
                        {user.telegram_profile?.first_name || 'User'}
                      </span>
                    </div>
                    <Link 
                      href="/dashboard" 
                      className="block mx-3"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <EnhancedButton variant="gold" size="sm" fullWidth>
                        Dashboard
                      </EnhancedButton>
                    </Link>
                    <div className="mx-3">
                      <EnhancedButton 
                        variant="outline" 
                        size="sm" 
                        fullWidth
                        onClick={handleSignOut}
                      >
                        Sign Out
                      </EnhancedButton>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2 px-3">
                    <Link href="/login" onClick={() => setIsMenuOpen(false)}>
                      <EnhancedButton variant="outline" size="sm" fullWidth>
                        Sign In
                      </EnhancedButton>
                    </Link>
                    <Link href="/login" onClick={() => setIsMenuOpen(false)}>
                      <EnhancedButton variant="gold" size="sm" fullWidth>
                        Get Started
                      </EnhancedButton>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
