'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { FloatingActionButton } from '@/components/ui/EnhancedButton'

export default function QuickAccessFAB() {
  const { user, loading } = useAuth()
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      // Show FAB after scrolling down 300px
      setIsVisible(window.scrollY > 300)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Don't show on login page or dashboard pages
  useEffect(() => {
    const currentPath = window.location.pathname
    if (currentPath.includes('/login') || currentPath.includes('/dashboard')) {
      setIsVisible(false)
    }
  }, [])

  if (loading || !isVisible) {
    return null
  }

  const fabIcon = user ? (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  ) : (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
    </svg>
  )

  const fabLink = user ? '/dashboard' : '/login'
  const fabTooltip = user ? 'Go to Dashboard' : 'Sign In'

  return (
    <Link href={fabLink}>
      <FloatingActionButton
        icon={fabIcon}
        variant="gold"
        size="lg"
        position="bottom-right"
        tooltip={fabTooltip}
        className={`transition-all duration-300 transform ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'
        }`}
      />
    </Link>
  )
}
