'use client'

import { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/solid'

interface OnboardingStep {
  id: string
  title: string
  description: string
  completed: boolean
  current: boolean
}

interface OnboardingProgressProps {
  steps: OnboardingStep[]
  currentStep: number
  totalSteps: number
}

export default function OnboardingProgress({ steps, currentStep, totalSteps }: OnboardingProgressProps) {
  const progressPercentage = (currentStep / totalSteps) * 100

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold text-gray-800">Setup Progress</h2>
          <span className="text-sm text-gray-600">{currentStep} of {totalSteps} completed</span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Steps List */}
      <div className="space-y-3">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              {step.completed ? (
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
              ) : step.current ? (
                <ClockIcon className="h-5 w-5 text-blue-500" />
              ) : (
                <div className="h-5 w-5 rounded-full border-2 border-gray-300"></div>
              )}
            </div>
            
            <div className="flex-1">
              <h3 className={`text-sm font-medium ${
                step.completed ? 'text-green-700' : 
                step.current ? 'text-blue-700' : 
                'text-gray-500'
              }`}>
                {step.title}
              </h3>
              <p className={`text-xs ${
                step.completed ? 'text-green-600' : 
                step.current ? 'text-blue-600' : 
                'text-gray-400'
              }`}>
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
