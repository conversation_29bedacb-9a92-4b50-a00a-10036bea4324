'use client'

import { useRouter } from 'next/navigation'
import { ArrowLeftIcon, ArrowRightIcon } from '@heroicons/react/24/outline'

interface OnboardingStepNavigationProps {
  currentStep: 'terms' | 'country' | 'kyc'
  canGoBack?: boolean
  canGoNext?: boolean
  isLoading?: boolean
  onNext?: () => void
  onBack?: () => void
  nextLabel?: string
  backLabel?: string
  showSkip?: boolean
  onSkip?: () => void
}

export default function OnboardingStepNavigation({
  currentStep,
  canGoBack = true,
  canGoNext = false,
  isLoading = false,
  onNext,
  onBack,
  nextLabel,
  backLabel,
  showSkip = false,
  onSkip
}: OnboardingStepNavigationProps) {
  const router = useRouter()

  const stepConfig = {
    terms: {
      backRoute: '/login',
      nextRoute: '/onboarding/country',
      defaultNextLabel: 'Accept & Continue',
      defaultBackLabel: 'Back to Login'
    },
    country: {
      backRoute: '/onboarding/terms',
      nextRoute: '/onboarding/kyc',
      defaultNextLabel: 'Continue to KYC',
      defaultBackLabel: 'Back to Terms'
    },
    kyc: {
      backRoute: '/onboarding/country',
      nextRoute: '/dashboard',
      defaultNextLabel: 'Submit & Complete',
      defaultBackLabel: 'Back to Country'
    }
  }

  const config = stepConfig[currentStep]

  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      router.push(config.backRoute)
    }
  }

  const handleNext = () => {
    if (onNext) {
      onNext()
    } else {
      router.push(config.nextRoute)
    }
  }

  const handleSkip = () => {
    if (onSkip) {
      onSkip()
    } else {
      router.push(config.nextRoute)
    }
  }

  return (
    <div className="flex items-center justify-between pt-6 border-t border-gray-200">
      {/* Back Button */}
      <button
        type="button"
        onClick={handleBack}
        disabled={!canGoBack || isLoading}
        className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${
          canGoBack && !isLoading
            ? 'text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-300'
            : 'text-gray-400 bg-gray-50 cursor-not-allowed border border-gray-200'
        }`}
      >
        <ArrowLeftIcon className="w-4 h-4 mr-2" />
        {backLabel || config.defaultBackLabel}
      </button>

      {/* Skip Button (if applicable) */}
      {showSkip && (
        <button
          type="button"
          onClick={handleSkip}
          disabled={isLoading}
          className="text-sm text-gray-500 hover:text-gray-700 underline transition-colors duration-200"
        >
          Skip this step
        </button>
      )}

      {/* Next Button */}
      <button
        type="button"
        onClick={handleNext}
        disabled={!canGoNext || isLoading}
        className={`inline-flex items-center px-6 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          canGoNext && !isLoading
            ? 'text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transform hover:scale-105'
            : 'text-gray-400 bg-gray-200 cursor-not-allowed'
        }`}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Processing...
          </>
        ) : (
          <>
            {nextLabel || config.defaultNextLabel}
            <ArrowRightIcon className="w-4 h-4 ml-2" />
          </>
        )}
      </button>
    </div>
  )
}

// Step indicator component
interface StepIndicatorProps {
  currentStep: 'terms' | 'country' | 'kyc'
  completedSteps: string[]
}

export function StepIndicator({ currentStep, completedSteps }: StepIndicatorProps) {
  const steps = [
    { id: 'terms', label: 'Terms', number: 1 },
    { id: 'country', label: 'Country', number: 2 },
    { id: 'kyc', label: 'KYC', number: 3 }
  ]

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === currentStep)
  }

  const currentStepIndex = getCurrentStepIndex()

  return (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          {/* Step Circle */}
          <div
            className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
              completedSteps.includes(step.id)
                ? 'bg-green-500 border-green-500 text-white'
                : index === currentStepIndex
                ? 'bg-blue-500 border-blue-500 text-white'
                : 'bg-white border-gray-300 text-gray-400'
            }`}
          >
            {completedSteps.includes(step.id) ? (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <span className="text-sm font-medium">{step.number}</span>
            )}
          </div>

          {/* Step Label */}
          <span
            className={`ml-2 text-sm font-medium ${
              completedSteps.includes(step.id)
                ? 'text-green-600'
                : index === currentStepIndex
                ? 'text-blue-600'
                : 'text-gray-400'
            }`}
          >
            {step.label}
          </span>

          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div
              className={`w-12 h-0.5 mx-4 transition-all duration-200 ${
                index < currentStepIndex || completedSteps.includes(steps[index + 1].id)
                  ? 'bg-green-500'
                  : 'bg-gray-300'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  )
}
