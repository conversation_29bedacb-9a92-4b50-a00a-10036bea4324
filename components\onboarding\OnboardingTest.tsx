'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { calculateOnboardingStatus, validateOnboardingCompletion } from '@/lib/onboarding-validation'

interface OnboardingTestResult {
  test: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function OnboardingTest() {
  const { user, loading, checkOnboardingStatus } = useAuth()
  const [testResults, setTestResults] = useState<OnboardingTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const addResult = (result: OnboardingTestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runOnboardingTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Check if user is authenticated
    addResult({
      test: 'User Authentication',
      status: user ? 'success' : 'error',
      message: user ? 'User is authenticated' : 'No authenticated user',
      details: user ? {
        id: user.id,
        telegram_id: user.user_metadata?.telegram_id
      } : null
    })

    if (user) {
      // Test 2: Check onboarding status
      try {
        const onboardingStatus = await checkOnboardingStatus()
        addResult({
          test: 'Onboarding Status Check',
          status: 'success',
          message: 'Onboarding status retrieved successfully',
          details: onboardingStatus
        })

        // Test 3: Calculate comprehensive status
        const comprehensiveStatus = calculateOnboardingStatus(
          onboardingStatus.hasAcceptedTerms,
          onboardingStatus.hasSelectedCountry,
          onboardingStatus.hasCompletedKYC
        )

        addResult({
          test: 'Onboarding Progress Calculation',
          status: 'success',
          message: `Onboarding ${comprehensiveStatus.completionPercentage}% complete`,
          details: comprehensiveStatus
        })

        // Test 4: Validate completion
        const validationResult = validateOnboardingCompletion(comprehensiveStatus)
        addResult({
          test: 'Onboarding Validation',
          status: validationResult.isValid ? 'success' : validationResult.errors.length > 0 ? 'error' : 'warning',
          message: validationResult.isValid 
            ? 'Onboarding is complete and valid' 
            : `Validation issues found: ${validationResult.errors.length} errors, ${validationResult.warnings.length} warnings`,
          details: validationResult
        })

        // Test 5: Check individual steps
        addResult({
          test: 'Terms Acceptance',
          status: onboardingStatus.hasAcceptedTerms ? 'success' : 'error',
          message: onboardingStatus.hasAcceptedTerms ? 'Terms have been accepted' : 'Terms not yet accepted',
          details: { hasAcceptedTerms: onboardingStatus.hasAcceptedTerms }
        })

        addResult({
          test: 'Country Selection',
          status: onboardingStatus.hasSelectedCountry ? 'success' : 'error',
          message: onboardingStatus.hasSelectedCountry 
            ? `Country selected: ${user.telegram_profile?.country || 'Unknown'}` 
            : 'Country not yet selected',
          details: { 
            hasSelectedCountry: onboardingStatus.hasSelectedCountry,
            country: user.telegram_profile?.country
          }
        })

        addResult({
          test: 'KYC Verification',
          status: onboardingStatus.hasCompletedKYC ? 'success' : 'error',
          message: onboardingStatus.hasCompletedKYC ? 'KYC verification completed' : 'KYC verification not completed',
          details: { hasCompletedKYC: onboardingStatus.hasCompletedKYC }
        })

        // Test 6: Test API endpoint
        try {
          const response = await fetch('/api/onboarding/status', {
            credentials: 'include'
          })
          
          if (response.ok) {
            const apiData = await response.json()
            addResult({
              test: 'Onboarding API Endpoint',
              status: 'success',
              message: 'API endpoint working correctly',
              details: apiData
            })
          } else {
            addResult({
              test: 'Onboarding API Endpoint',
              status: 'error',
              message: `API returned ${response.status}`,
              details: await response.text()
            })
          }
        } catch (error) {
          addResult({
            test: 'Onboarding API Endpoint',
            status: 'error',
            message: 'Failed to call onboarding API',
            details: error
          })
        }

        // Test 7: Check database consistency
        if (user.telegram_profile) {
          const dbChecks = []
          
          if (onboardingStatus.hasAcceptedTerms) {
            dbChecks.push('terms_acceptance table has record')
          }
          
          if (onboardingStatus.hasSelectedCountry) {
            dbChecks.push('telegram_users.country is set')
          }
          
          if (onboardingStatus.hasCompletedKYC) {
            dbChecks.push('kyc_information table has record')
          }

          addResult({
            test: 'Database Consistency',
            status: dbChecks.length > 0 ? 'success' : 'warning',
            message: `Database consistency checks: ${dbChecks.length} passed`,
            details: { checks: dbChecks }
          })
        }

      } catch (error) {
        addResult({
          test: 'Onboarding Status Check',
          status: 'error',
          message: 'Failed to check onboarding status',
          details: error
        })
      }
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: OnboardingTestResult['status']) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: OnboardingTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'pending':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-center mt-2 text-gray-600">Loading onboarding state...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Onboarding System Test</h2>
      
      <div className="mb-6">
        <button
          onClick={runOnboardingTests}
          disabled={isRunning}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          {isRunning ? 'Running Tests...' : 'Run Onboarding Tests'}
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results:</h3>
          
          {testResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">
                  {getStatusIcon(result.status)} {result.test}
                </h4>
                <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}

          {/* Summary */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Test Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-600">Passed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'success').length}</span>
              </div>
              <div>
                <span className="font-medium text-red-600">Failed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'error').length}</span>
              </div>
              <div>
                <span className="font-medium text-yellow-600">Warnings:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'warning').length}</span>
              </div>
              <div>
                <span className="font-medium text-blue-600">Total:</span>
                <span className="ml-2">{testResults.length}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {!user && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            <strong>Note:</strong> You need to be authenticated to run onboarding tests. 
            Please sign in with Telegram first.
          </p>
        </div>
      )}
    </div>
  )
}
