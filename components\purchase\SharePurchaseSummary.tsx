'use client'

import { useState, useEffect } from 'react'
import { 
  calculateShares, 
  getPackageInfo, 
  formatCurrency, 
  calculateROIProjections,
  InvestmentPhase 
} from '@/lib/share-calculations'

interface SharePurchaseSummaryProps {
  amount: number
  phase: InvestmentPhase
  paymentMethod: string
  network?: string
  onConfirm?: (data: any) => void
  className?: string
}

export default function SharePurchaseSummary({
  amount,
  phase,
  paymentMethod,
  network,
  onConfirm,
  className
}: SharePurchaseSummaryProps) {
  const [calculation, setCalculation] = useState<any>(null)
  const [packageInfo, setPackageInfo] = useState<any>(null)
  const [roiProjections, setRoiProjections] = useState<any[]>([])

  useEffect(() => {
    if (amount && phase) {
      const calc = calculateShares(amount, phase.price_per_share)
      const pkg = getPackageInfo(amount)
      const roi = calculateROIProjections(calc.sharesAmount, pkg.roi, 5)
      
      setCalculation(calc)
      setPackageInfo(pkg)
      setRoiProjections(roi)
    }
  }, [amount, phase])

  if (!calculation || !packageInfo) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    )
  }

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm({
        amount: calculation.totalCost,
        shares: calculation.sharesAmount,
        package: packageInfo,
        phase: phase,
        paymentMethod,
        network
      })
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 px-6 py-4">
        <h3 className="text-xl font-bold text-white">Purchase Summary</h3>
        <p className="text-yellow-100 text-sm">Review your share purchase details</p>
      </div>

      <div className="p-6 space-y-6">
        {/* Investment Details */}
        <div className="border-b border-gray-200 pb-4">
          <h4 className="font-semibold text-gray-800 mb-3">Investment Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Package:</span>
              <span className="ml-2 font-medium text-gray-800">{packageInfo.name}</span>
            </div>
            <div>
              <span className="text-gray-600">Investment Phase:</span>
              <span className="ml-2 font-medium text-gray-800">{phase.phase_name}</span>
            </div>
            <div>
              <span className="text-gray-600">Shares:</span>
              <span className="ml-2 font-medium text-gray-800">{calculation.sharesAmount.toLocaleString()}</span>
            </div>
            <div>
              <span className="text-gray-600">Price per Share:</span>
              <span className="ml-2 font-medium text-gray-800">{formatCurrency(phase.price_per_share)}</span>
            </div>
            <div>
              <span className="text-gray-600">Total Cost:</span>
              <span className="ml-2 font-medium text-green-600">{formatCurrency(calculation.totalCost)}</span>
            </div>
            <div>
              <span className="text-gray-600">Expected ROI:</span>
              <span className="ml-2 font-medium text-green-600">{packageInfo.roi}% annually</span>
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="border-b border-gray-200 pb-4">
          <h4 className="font-semibold text-gray-800 mb-3">Payment Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Payment Method:</span>
              <span className="ml-2 font-medium text-gray-800">{paymentMethod}</span>
            </div>
            {network && (
              <div>
                <span className="text-gray-600">Network:</span>
                <span className="ml-2 font-medium text-gray-800">{network}</span>
              </div>
            )}
            <div>
              <span className="text-gray-600">Processing Time:</span>
              <span className="ml-2 font-medium text-gray-800">
                {paymentMethod === 'ZAR' ? '1-3 business days' : '30 minutes - 2 hours'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Fees:</span>
              <span className="ml-2 font-medium text-green-600">$0.00</span>
            </div>
          </div>
        </div>

        {/* Refund Information */}
        {calculation.remainingAmount > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-yellow-600 text-lg mr-2">⚠️</span>
              <div>
                <h5 className="font-medium text-yellow-800">Partial Share Refund</h5>
                <p className="text-yellow-700 text-sm">
                  {formatCurrency(calculation.remainingAmount)} will be refunded as it doesn't purchase a full share.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* ROI Projections */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">ROI Projections</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {roiProjections.slice(0, 3).map((projection, index) => (
                <div key={projection.year} className="text-center">
                  <div className="font-medium text-gray-800">Year {projection.year}</div>
                  <div className="text-green-600 font-semibold">
                    {formatCurrency(projection.annualReturn)}
                  </div>
                  <div className="text-gray-600 text-xs">
                    {projection.roi.toFixed(1)}% ROI
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Warnings */}
        {calculation.warnings.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 className="font-medium text-blue-800 mb-2">Important Notes</h5>
            <ul className="text-blue-700 text-sm space-y-1">
              {calculation.warnings.map((warning: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  {warning}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Errors */}
        {calculation.errors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h5 className="font-medium text-red-800 mb-2">Validation Errors</h5>
            <ul className="text-red-700 text-sm space-y-1">
              {calculation.errors.map((error: string, index: number) => (
                <li key={index} className="flex items-start">
                  <span className="text-red-500 mr-2">•</span>
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Action Buttons */}
        {onConfirm && (
          <div className="flex justify-between pt-4 border-t border-gray-200">
            <button
              type="button"
              className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors duration-200"
              onClick={() => window.history.back()}
            >
              ← Back to Purchase
            </button>
            
            <button
              type="button"
              onClick={handleConfirm}
              disabled={!calculation.isValid}
              className={`px-8 py-3 rounded-lg font-semibold transition-all duration-200 ${
                calculation.isValid
                  ? 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {calculation.isValid ? 'Confirm Purchase' : 'Cannot Proceed'}
            </button>
          </div>
        )}

        {/* Disclaimer */}
        <div className="text-xs text-gray-500 pt-4 border-t border-gray-200">
          <p>
            * ROI projections are estimates based on current mining operations and market conditions. 
            Actual returns may vary. This is not financial advice.
          </p>
        </div>
      </div>
    </div>
  )
}
