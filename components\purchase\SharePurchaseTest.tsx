'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { 
  calculateShares, 
  validatePhaseAvailability, 
  validatePurchaseLimits,
  getPackageInfo,
  calculateCommission,
  formatCurrency
} from '@/lib/share-calculations'

interface SharePurchaseTestResult {
  test: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export default function SharePurchaseTest() {
  const { user, loading } = useAuth()
  const [testResults, setTestResults] = useState<SharePurchaseTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentPhase, setCurrentPhase] = useState<any>(null)

  const addResult = (result: SharePurchaseTestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const runSharePurchaseTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: Check if user is authenticated
    addResult({
      test: 'User Authentication',
      status: user ? 'success' : 'error',
      message: user ? 'User is authenticated' : 'No authenticated user',
      details: user ? {
        id: user.id,
        telegram_id: user.user_metadata?.telegram_id
      } : null
    })

    if (user) {
      // Test 2: Get current investment phase
      try {
        const response = await fetch('/api/shares/purchase', {
          credentials: 'include'
        })
        
        if (response.ok) {
          const data = await response.json()
          setCurrentPhase(data.currentPhase)
          
          addResult({
            test: 'Current Investment Phase',
            status: 'success',
            message: `Active phase: ${data.currentPhase.name}`,
            details: data.currentPhase
          })

          // Test 3: Share calculation tests
          const testAmounts = [25, 100, 500, 1000, 5000]
          
          for (const amount of testAmounts) {
            const calculation = calculateShares(amount, data.currentPhase.pricePerShare)
            
            addResult({
              test: `Share Calculation ($${amount})`,
              status: calculation.isValid ? 'success' : 'error',
              message: calculation.isValid 
                ? `${calculation.sharesAmount} shares for ${formatCurrency(calculation.totalCost)}`
                : `Calculation failed: ${calculation.errors.join(', ')}`,
              details: calculation
            })
          }

          // Test 4: Package information tests
          for (const amount of testAmounts) {
            const packageInfo = getPackageInfo(amount)
            
            addResult({
              test: `Package Info ($${amount})`,
              status: 'success',
              message: `Package: ${packageInfo.name} (${packageInfo.roi}% ROI)`,
              details: packageInfo
            })
          }

          // Test 5: Phase availability validation
          const largeShareAmount = 10000
          const phaseValidation = validatePhaseAvailability(largeShareAmount, data.currentPhase)
          
          addResult({
            test: 'Phase Availability Validation',
            status: phaseValidation.isValid ? 'success' : 'warning',
            message: phaseValidation.isValid 
              ? 'Phase has sufficient shares available'
              : `Phase validation issues: ${phaseValidation.errors.join(', ')}`,
            details: phaseValidation
          })

          // Test 6: Purchase limits validation
          const limitsValidation = validatePurchaseLimits(1000, user.id, 0)
          
          addResult({
            test: 'Purchase Limits Validation',
            status: limitsValidation.isValid ? 'success' : 'error',
            message: limitsValidation.isValid 
              ? 'Purchase limits validation passed'
              : `Limits validation failed: ${limitsValidation.errors.join(', ')}`,
            details: limitsValidation
          })

          // Test 7: Commission calculation
          const commissionTest = calculateCommission(1000, 1000, 15)
          
          addResult({
            test: 'Commission Calculation',
            status: 'success',
            message: `USDT: ${formatCurrency(commissionTest.usdtCommission)}, Shares: ${commissionTest.shareCommission}`,
            details: commissionTest
          })

          // Test 8: User's existing shares
          addResult({
            test: 'User Share Portfolio',
            status: 'success',
            message: `Total shares: ${data.userShares.totalShares}, Total invested: ${formatCurrency(data.userShares.totalInvested)}`,
            details: data.userShares
          })

        } else {
          addResult({
            test: 'Share Purchase API',
            status: 'error',
            message: `API returned ${response.status}`,
            details: await response.text()
          })
        }
      } catch (error) {
        addResult({
          test: 'Share Purchase API',
          status: 'error',
          message: 'Failed to call share purchase API',
          details: error
        })
      }

      // Test 9: Edge case validations
      const edgeCases = [
        { amount: 0, description: 'Zero amount' },
        { amount: -100, description: 'Negative amount' },
        { amount: 24, description: 'Below minimum' },
        { amount: 10001, description: 'Above maximum' },
        { amount: 25.5, description: 'Decimal amount' }
      ]

      for (const edgeCase of edgeCases) {
        if (currentPhase) {
          const calculation = calculateShares(edgeCase.amount, currentPhase.pricePerShare)
          
          addResult({
            test: `Edge Case: ${edgeCase.description}`,
            status: calculation.isValid ? 'warning' : 'success',
            message: calculation.isValid 
              ? `Unexpectedly passed validation`
              : `Correctly rejected: ${calculation.errors[0]}`,
            details: { amount: edgeCase.amount, calculation }
          })
        }
      }

      // Test 10: Payment method validation
      const paymentMethods = ['USDT', 'ZAR']
      const country = user.telegram_profile?.country || 'ZA'
      
      for (const method of paymentMethods) {
        const isAvailable = method === 'ZAR' ? country === 'ZA' : true
        
        addResult({
          test: `Payment Method: ${method}`,
          status: isAvailable ? 'success' : 'warning',
          message: isAvailable 
            ? `${method} payment available for ${country}`
            : `${method} payment not available for ${country}`,
          details: { method, country, available: isAvailable }
        })
      }
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: SharePurchaseTestResult['status']) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: SharePurchaseTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'pending':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-center mt-2 text-gray-600">Loading share purchase system...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Share Purchase System Test</h2>
      
      <div className="mb-6">
        <button
          onClick={runSharePurchaseTests}
          disabled={isRunning}
          className="bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          {isRunning ? 'Running Tests...' : 'Run Share Purchase Tests'}
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results:</h3>
          
          {testResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">
                  {getStatusIcon(result.status)} {result.test}
                </h4>
                <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}

          {/* Summary */}
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">Test Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-600">Passed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'success').length}</span>
              </div>
              <div>
                <span className="font-medium text-red-600">Failed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'error').length}</span>
              </div>
              <div>
                <span className="font-medium text-yellow-600">Warnings:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'warning').length}</span>
              </div>
              <div>
                <span className="font-medium text-blue-600">Total:</span>
                <span className="ml-2">{testResults.length}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {!user && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            <strong>Note:</strong> You need to be authenticated to run share purchase tests. 
            Please sign in with Telegram first.
          </p>
        </div>
      )}
    </div>
  )
}
