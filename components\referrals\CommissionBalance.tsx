'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal, ModalBody, ModalFooter, ModalHeader, ModalTitle } from '@/components/ui/Modal'

interface CommissionBalanceProps {
  userId: number
  className?: string
}

interface CommissionData {
  usdtBalance: number
  shareBalance: number
  totalEarned: number
  totalWithdrawn: number
  pendingWithdrawals: number
  availableForWithdrawal: number
  minimumWithdrawal: number
  recentTransactions: {
    id: string
    type: 'earned' | 'withdrawn' | 'converted'
    amount: number
    currency: 'USDT' | 'SHARES'
    description: string
    date: string
    status: 'completed' | 'pending' | 'failed'
  }[]
}

export default function CommissionBalance({ userId, className }: CommissionBalanceProps) {
  const router = useRouter()
  const [commission, setCommission] = useState<CommissionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showConvertModal, setShowConvertModal] = useState(false)
  const [convertAmount, setConvertAmount] = useState('')
  const [converting, setConverting] = useState(false)

  useEffect(() => {
    loadCommissionData()
  }, [userId])

  const loadCommissionData = async () => {
    try {
      setLoading(true)
      
      // This would typically call an API endpoint
      // For now, we'll simulate the data structure
      const mockCommission: CommissionData = {
        usdtBalance: 1250.75,
        shareBalance: 180.5,
        totalEarned: 3420.25,
        totalWithdrawn: 2000.00,
        pendingWithdrawals: 169.50,
        availableForWithdrawal: 1081.25,
        minimumWithdrawal: 25.00,
        recentTransactions: [
          {
            id: '1',
            type: 'earned',
            amount: 150.00,
            currency: 'USDT',
            description: 'Commission from John Doe investment',
            date: '2024-03-15T10:30:00Z',
            status: 'completed'
          },
          {
            id: '2',
            type: 'withdrawn',
            amount: 500.00,
            currency: 'USDT',
            description: 'USDT withdrawal to BSC wallet',
            date: '2024-03-10T14:20:00Z',
            status: 'completed'
          },
          {
            id: '3',
            type: 'converted',
            amount: 25.5,
            currency: 'SHARES',
            description: 'Commission converted to shares',
            date: '2024-03-08T09:15:00Z',
            status: 'completed'
          }
        ]
      }
      
      setCommission(mockCommission)
    } catch (error) {
      console.error('Error loading commission data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleWithdraw = () => {
    router.push('/dashboard/referrals/withdraw')
  }

  const handleConvertToShares = async () => {
    if (!convertAmount || !commission) return

    try {
      setConverting(true)
      
      // This would call an API to convert commission to shares
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Update local state (in real app, refetch data)
      const amount = parseFloat(convertAmount)
      setCommission(prev => prev ? {
        ...prev,
        usdtBalance: prev.usdtBalance - amount,
        shareBalance: prev.shareBalance + amount // 1:1 conversion rate
      } : null)
      
      setShowConvertModal(false)
      setConvertAmount('')
    } catch (error) {
      console.error('Error converting commission:', error)
    } finally {
      setConverting(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'earned':
        return '💰'
      case 'withdrawn':
        return '📤'
      case 'converted':
        return '🔄'
      default:
        return '📊'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50'
      case 'failed':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-24 bg-gray-200 rounded"></div>
            <div className="h-24 bg-gray-200 rounded"></div>
          </div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </Card>
    )
  }

  if (!commission) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">💰</div>
          <p className="text-gray-600">Unable to load commission balance</p>
        </div>
      </Card>
    )
  }

  return (
    <>
      <Card className={`p-6 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800">Commission Balance</h2>
          <div className="flex space-x-2">
            <Button
              onClick={() => setShowConvertModal(true)}
              variant="outline"
              size="sm"
              disabled={commission.usdtBalance < 1}
            >
              🔄 Convert
            </Button>
            <Button
              onClick={handleWithdraw}
              variant="primary"
              size="sm"
              disabled={commission.availableForWithdrawal < commission.minimumWithdrawal}
            >
              📤 Withdraw
            </Button>
          </div>
        </div>

        {/* Balance Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* USDT Balance */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-green-600 text-2xl">💵</span>
              <span className="text-xs text-green-600 font-medium">USDT</span>
            </div>
            <div className="text-2xl font-bold text-green-800 mb-1">
              {formatCurrency(commission.usdtBalance)}
            </div>
            <div className="text-sm text-green-600">
              Available: {formatCurrency(commission.availableForWithdrawal)}
            </div>
          </div>

          {/* Share Balance */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-blue-600 text-2xl">📈</span>
              <span className="text-xs text-blue-600 font-medium">SHARES</span>
            </div>
            <div className="text-2xl font-bold text-blue-800 mb-1">
              {commission.shareBalance.toLocaleString()}
            </div>
            <div className="text-sm text-blue-600">
              Mining shares owned
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-800">
              {formatCurrency(commission.totalEarned)}
            </div>
            <div className="text-sm text-gray-600">Total Earned</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-800">
              {formatCurrency(commission.totalWithdrawn)}
            </div>
            <div className="text-sm text-gray-600">Total Withdrawn</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-yellow-600">
              {formatCurrency(commission.pendingWithdrawals)}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Transactions</h3>
          {commission.recentTransactions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-4">📊</div>
              <p>No transactions yet</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {commission.recentTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{getTransactionIcon(transaction.type)}</span>
                    <div>
                      <div className="font-medium text-gray-800">
                        {transaction.currency === 'USDT' 
                          ? formatCurrency(transaction.amount)
                          : `${transaction.amount} shares`
                        }
                      </div>
                      <div className="text-sm text-gray-600">{transaction.description}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatDate(transaction.date)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Withdrawal Info */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">💡 Withdrawal Information</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li className="flex items-center">
              <span className="mr-2">•</span>
              <span>Minimum withdrawal: {formatCurrency(commission.minimumWithdrawal)}</span>
            </li>
            <li className="flex items-center">
              <span className="mr-2">•</span>
              <span>Processing time: 1-3 business days</span>
            </li>
            <li className="flex items-center">
              <span className="mr-2">•</span>
              <span>Supported networks: BSC, Polygon, TRON, Ethereum</span>
            </li>
            <li className="flex items-center">
              <span className="mr-2">•</span>
              <span>No withdrawal fees (network fees apply)</span>
            </li>
          </ul>
        </div>
      </Card>

      {/* Convert to Shares Modal */}
      <Modal
        isOpen={showConvertModal}
        onClose={() => setShowConvertModal(false)}
        size="md"
      >
        <ModalHeader>
          <ModalTitle>Convert Commission to Shares</ModalTitle>
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-4">
            <div>
              <p className="text-gray-600 mb-4">
                Convert your USDT commission to mining shares for additional passive income.
              </p>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-green-700">Available USDT:</span>
                  <span className="font-semibold text-green-800">
                    {formatCurrency(commission.usdtBalance)}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount to Convert (USD)
              </label>
              <input
                type="number"
                value={convertAmount}
                onChange={(e) => setConvertAmount(e.target.value)}
                placeholder="Enter amount"
                min="1"
                max={commission.usdtBalance}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Conversion rate: 1 USD = 1 Share (current phase pricing)
              </p>
            </div>

            {convertAmount && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">Conversion Preview:</h4>
                <div className="space-y-1 text-sm text-blue-700">
                  <div className="flex justify-between">
                    <span>USDT Amount:</span>
                    <span>{formatCurrency(parseFloat(convertAmount) || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shares Received:</span>
                    <span>{parseFloat(convertAmount) || 0} shares</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Estimated Annual Dividend:</span>
                    <span>{formatCurrency((parseFloat(convertAmount) || 0) * 0.25)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setShowConvertModal(false)}
            disabled={converting}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleConvertToShares}
            disabled={!convertAmount || parseFloat(convertAmount) <= 0 || parseFloat(convertAmount) > commission.usdtBalance || converting}
          >
            {converting ? 'Converting...' : 'Convert to Shares'}
          </Button>
        </ModalFooter>
      </Modal>
    </>
  )
}
