'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useToastActions } from '@/components/ui/Toast'

interface ReferralLinkGeneratorProps {
  referralCode: string
  className?: string
}

export default function ReferralLinkGenerator({ 
  referralCode, 
  className 
}: ReferralLinkGeneratorProps) {
  const { user } = useAuth()
  const { success, error } = useToastActions()
  const [copied, setCopied] = useState(false)

  const botLink = `https://t.me/AureusAllianceBot?start=${referralCode}`
  const webLink = `https://aureus.africa/register?ref=${referralCode}`
  
  const shareText = `🌟 Join me in investing in gold mining shares with Aureus Alliance Holdings!

💰 Earn passive income from South African gold mining operations
🏆 Professional mining company with 250+ hectares of gold-rich land
📈 Multiple investment packages with up to 30% annual ROI

Use my referral code: ${referralCode}

Start here: ${botLink}`

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      success(`${type} copied to clipboard!`)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      error('Failed to copy to clipboard')
    }
  }

  const shareViaWebAPI = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Aureus Alliance Holdings - Gold Mining Investment',
          text: shareText,
          url: botLink
        })
      } catch (err) {
        if ((err as Error).name !== 'AbortError') {
          error('Failed to share')
        }
      }
    } else {
      // Fallback to copying
      copyToClipboard(shareText, 'Share message')
    }
  }

  const socialShareLinks = [
    {
      name: 'Telegram',
      icon: '📱',
      url: `https://t.me/share/url?url=${encodeURIComponent(botLink)}&text=${encodeURIComponent('Join me in investing in gold mining shares with Aureus Alliance Holdings!')}`
    },
    {
      name: 'WhatsApp',
      icon: '💬',
      url: `https://wa.me/?text=${encodeURIComponent(shareText)}`
    },
    {
      name: 'Twitter',
      icon: '🐦',
      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent('🌟 Join me in investing in gold mining shares with Aureus Alliance Holdings!')}&url=${encodeURIComponent(botLink)}`
    },
    {
      name: 'Facebook',
      icon: '📘',
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(botLink)}`
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(botLink)}`
    }
  ]

  return (
    <Card className={`p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Your Referral Link</h2>
        <p className="text-gray-600 text-sm">
          Share your unique referral link to earn 15% commission on all investments made by your referrals
        </p>
      </div>

      {/* Referral Code Display */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-blue-800 mb-1">Your Referral Code:</p>
            <p className="font-mono text-xl font-bold text-blue-600">{referralCode}</p>
          </div>
          <div className="ml-4">
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
              ✅ Active
            </span>
          </div>
        </div>
      </div>

      {/* Bot Link */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Telegram Bot Link:
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={botLink}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm font-mono"
          />
          <Button
            onClick={() => copyToClipboard(botLink, 'Bot link')}
            variant="outline"
            size="sm"
          >
            {copied ? '✅' : '📋'} Copy
          </Button>
        </div>
      </div>

      {/* Web Link */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Web Registration Link:
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={webLink}
            readOnly
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm font-mono"
          />
          <Button
            onClick={() => copyToClipboard(webLink, 'Web link')}
            variant="outline"
            size="sm"
          >
            {copied ? '✅' : '📋'} Copy
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3 mb-6">
        <Button
          onClick={() => copyToClipboard(shareText, 'Share message')}
          variant="primary"
          className="flex items-center space-x-2"
        >
          <span>📋</span>
          <span>Copy Share Message</span>
        </Button>
        
        <Button
          onClick={shareViaWebAPI}
          variant="secondary"
          className="flex items-center space-x-2"
        >
          <span>📤</span>
          <span>Share</span>
        </Button>
      </div>

      {/* Social Media Sharing */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">Share on Social Media:</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {socialShareLinks.map((social) => (
            <a
              key={social.name}
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 text-sm"
            >
              <span>{social.icon}</span>
              <span className="hidden md:inline">{social.name}</span>
            </a>
          ))}
        </div>
      </div>

      {/* Commission Info */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">💰 Commission Structure</h3>
        <ul className="text-sm text-green-700 space-y-1">
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span><strong>15% USDT Commission</strong> on all investments</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span><strong>15% Share Commission</strong> for additional earnings</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span><strong>Instant Payouts</strong> when referrals are approved</span>
          </li>
          <li className="flex items-center">
            <span className="mr-2">•</span>
            <span><strong>No Limits</strong> on referrals or earnings</span>
          </li>
        </ul>
      </div>

      {/* Tips */}
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">💡 Referral Tips</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li className="flex items-start">
            <span className="mr-2 mt-0.5">•</span>
            <span>Share your personal investment experience and results</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 mt-0.5">•</span>
            <span>Explain the gold mining opportunity and company background</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 mt-0.5">•</span>
            <span>Highlight the passive income potential and ROI rates</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 mt-0.5">•</span>
            <span>Be available to answer questions and provide support</span>
          </li>
        </ul>
      </div>
    </Card>
  )
}
