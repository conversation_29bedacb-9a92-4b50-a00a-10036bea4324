'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface ReferralListProps {
  userId: number
  className?: string
}

interface Referral {
  id: string
  firstName: string
  lastName: string
  username: string
  email: string
  country: string
  joinedDate: string
  totalInvested: number
  commissionEarned: number
  status: 'active' | 'inactive' | 'pending'
  lastActivity: string
  investmentCount: number
  averageInvestment: number
}

export default function ReferralList({ userId, className }: ReferralListProps) {
  const [referrals, setReferrals] = useState<Referral[]>([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState<'date' | 'commission' | 'investment'>('date')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'pending'>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadReferrals()
  }, [userId])

  const loadReferrals = async () => {
    try {
      setLoading(true)
      
      // This would typically call an API endpoint
      // For now, we'll simulate the data structure
      const mockReferrals: Referral[] = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          username: 'johndoe',
          email: '<EMAIL>',
          country: 'ZA',
          joinedDate: '2024-03-01T10:00:00Z',
          totalInvested: 2500.00,
          commissionEarned: 375.00,
          status: 'active',
          lastActivity: '2024-03-15T14:30:00Z',
          investmentCount: 3,
          averageInvestment: 833.33
        },
        {
          id: '2',
          firstName: 'Sarah',
          lastName: 'Smith',
          username: 'sarahsmith',
          email: '<EMAIL>',
          country: 'US',
          joinedDate: '2024-02-15T09:15:00Z',
          totalInvested: 1000.00,
          commissionEarned: 150.00,
          status: 'active',
          lastActivity: '2024-03-10T11:20:00Z',
          investmentCount: 1,
          averageInvestment: 1000.00
        },
        {
          id: '3',
          firstName: 'Mike',
          lastName: 'Johnson',
          username: 'mikej',
          email: '<EMAIL>',
          country: 'UK',
          joinedDate: '2024-03-05T16:45:00Z',
          totalInvested: 0,
          commissionEarned: 0,
          status: 'pending',
          lastActivity: '2024-03-05T16:45:00Z',
          investmentCount: 0,
          averageInvestment: 0
        }
      ]
      
      setReferrals(mockReferrals)
    } catch (error) {
      console.error('Error loading referrals:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return '✅'
      case 'inactive':
        return '😴'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50'
      case 'inactive':
        return 'text-gray-600 bg-gray-50'
      case 'pending':
        return 'text-yellow-600 bg-yellow-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getCountryFlag = (countryCode: string) => {
    const flags: { [key: string]: string } = {
      'ZA': '🇿🇦',
      'US': '🇺🇸',
      'UK': '🇬🇧',
      'CA': '🇨🇦',
      'AU': '🇦🇺',
      'DE': '🇩🇪',
      'FR': '🇫🇷'
    }
    return flags[countryCode] || '🌍'
  }

  const filteredAndSortedReferrals = referrals
    .filter(referral => {
      if (filterStatus !== 'all' && referral.status !== filterStatus) return false
      if (searchTerm && !`${referral.firstName} ${referral.lastName} ${referral.username}`.toLowerCase().includes(searchTerm.toLowerCase())) return false
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'commission':
          return b.commissionEarned - a.commissionEarned
        case 'investment':
          return b.totalInvested - a.totalInvested
        case 'date':
        default:
          return new Date(b.joinedDate).getTime() - new Date(a.joinedDate).getTime()
      }
    })

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className={`p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Your Referrals</h2>
        <div className="text-sm text-gray-600">
          {filteredAndSortedReferrals.length} of {referrals.length} referrals
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        {/* Search */}
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search referrals..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Status Filter */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="pending">Pending</option>
        </select>

        {/* Sort By */}
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="date">Sort by Date</option>
          <option value="commission">Sort by Commission</option>
          <option value="investment">Sort by Investment</option>
        </select>
      </div>

      {/* Referrals List */}
      {filteredAndSortedReferrals.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-4xl mb-4">👥</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">
            {referrals.length === 0 ? 'No Referrals Yet' : 'No Matching Referrals'}
          </h3>
          <p className="text-gray-600 text-sm mb-4">
            {referrals.length === 0 
              ? 'Share your referral link to start building your network!'
              : 'Try adjusting your search or filter criteria.'
            }
          </p>
          {referrals.length === 0 && (
            <Button variant="primary" size="sm">
              📤 Share Referral Link
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredAndSortedReferrals.map((referral) => (
            <div
              key={referral.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Avatar */}
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {referral.firstName[0]}{referral.lastName[0]}
                  </div>

                  {/* User Info */}
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-800">
                        {referral.firstName} {referral.lastName}
                      </h3>
                      <span className="text-gray-500">@{referral.username}</span>
                      <span className="text-lg">{getCountryFlag(referral.country)}</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Joined {formatDate(referral.joinedDate)}</span>
                      <span>•</span>
                      <span>{referral.investmentCount} investments</span>
                      <span>•</span>
                      <span>Last active {formatDate(referral.lastActivity)}</span>
                    </div>
                  </div>
                </div>

                {/* Status and Stats */}
                <div className="text-right">
                  <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mb-2 ${getStatusColor(referral.status)}`}>
                    {getStatusIcon(referral.status)} {referral.status}
                  </div>
                  <div className="space-y-1 text-sm">
                    <div className="text-gray-600">
                      Invested: <span className="font-semibold text-gray-800">{formatCurrency(referral.totalInvested)}</span>
                    </div>
                    <div className="text-green-600">
                      Commission: <span className="font-semibold">{formatCurrency(referral.commissionEarned)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Stats */}
              {referral.totalInvested > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="grid grid-cols-3 gap-4 text-center text-sm">
                    <div>
                      <div className="font-semibold text-gray-800">{referral.investmentCount}</div>
                      <div className="text-gray-600">Investments</div>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-800">{formatCurrency(referral.averageInvestment)}</div>
                      <div className="text-gray-600">Average</div>
                    </div>
                    <div>
                      <div className="font-semibold text-green-600">{((referral.commissionEarned / referral.totalInvested) * 100).toFixed(1)}%</div>
                      <div className="text-gray-600">Commission Rate</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Summary Stats */}
      {referrals.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-800">{referrals.length}</div>
              <div className="text-sm text-gray-600">Total Referrals</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {referrals.filter(r => r.status === 'active').length}
              </div>
              <div className="text-sm text-gray-600">Active</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-800">
                {formatCurrency(referrals.reduce((sum, r) => sum + r.totalInvested, 0))}
              </div>
              <div className="text-sm text-gray-600">Total Volume</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {formatCurrency(referrals.reduce((sum, r) => sum + r.commissionEarned, 0))}
              </div>
              <div className="text-sm text-gray-600">Total Commission</div>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
