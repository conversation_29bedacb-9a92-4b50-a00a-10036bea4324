'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'

interface ReferralStatsProps {
  userId: number
  className?: string
}

interface ReferralData {
  totalReferrals: number
  activeReferrals: number
  totalCommissionEarned: number
  pendingCommission: number
  totalInvestmentVolume: number
  averageInvestmentPerReferral: number
  conversionRate: number
  monthlyStats: {
    month: string
    referrals: number
    commission: number
    volume: number
  }[]
}

export default function ReferralStats({ userId, className }: ReferralStatsProps) {
  const [stats, setStats] = useState<ReferralData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<'7d' | '30d' | '90d' | 'all'>('30d')

  useEffect(() => {
    loadReferralStats()
  }, [userId, timeframe])

  const loadReferralStats = async () => {
    try {
      setLoading(true)
      
      // This would typically call an API endpoint
      // For now, we'll simulate the data structure
      const mockStats: ReferralData = {
        totalReferrals: 12,
        activeReferrals: 8,
        totalCommissionEarned: 2450.75,
        pendingCommission: 180.25,
        totalInvestmentVolume: 16338.33,
        averageInvestmentPerReferral: 1361.53,
        conversionRate: 66.7,
        monthlyStats: [
          { month: 'Jan 2024', referrals: 3, commission: 450.25, volume: 3001.67 },
          { month: 'Feb 2024', referrals: 5, commission: 780.50, volume: 5203.33 },
          { month: 'Mar 2024', referrals: 4, commission: 1220.00, volume: 8133.33 }
        ]
      }
      
      setStats(mockStats)
    } catch (error) {
      console.error('Error loading referral stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    )
  }

  if (!stats) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">📊</div>
          <p className="text-gray-600">Unable to load referral statistics</p>
        </div>
      </Card>
    )
  }

  const statCards = [
    {
      title: 'Total Referrals',
      value: stats.totalReferrals.toString(),
      subtitle: `${stats.activeReferrals} active`,
      icon: '👥',
      color: 'blue'
    },
    {
      title: 'Total Commission',
      value: formatCurrency(stats.totalCommissionEarned),
      subtitle: `${formatCurrency(stats.pendingCommission)} pending`,
      icon: '💰',
      color: 'green'
    },
    {
      title: 'Investment Volume',
      value: formatCurrency(stats.totalInvestmentVolume),
      subtitle: `${formatCurrency(stats.averageInvestmentPerReferral)} avg`,
      icon: '📈',
      color: 'purple'
    },
    {
      title: 'Conversion Rate',
      value: formatPercentage(stats.conversionRate),
      subtitle: 'Referrals who invested',
      icon: '🎯',
      color: 'orange'
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 text-blue-800',
      green: 'bg-green-50 border-green-200 text-green-800',
      purple: 'bg-purple-50 border-purple-200 text-purple-800',
      orange: 'bg-orange-50 border-orange-200 text-orange-800'
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <Card className={`p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Referral Statistics</h2>
        
        {/* Timeframe Selector */}
        <div className="flex bg-gray-100 rounded-lg p-1">
          {[
            { key: '7d', label: '7D' },
            { key: '30d', label: '30D' },
            { key: '90d', label: '90D' },
            { key: 'all', label: 'All' }
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setTimeframe(option.key as any)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors duration-200 ${
                timeframe === option.key
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`border rounded-lg p-4 ${getColorClasses(stat.color)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl">{stat.icon}</span>
              <div className="text-right">
                <div className="text-lg font-bold">{stat.value}</div>
                <div className="text-xs opacity-75">{stat.subtitle}</div>
              </div>
            </div>
            <div className="text-sm font-medium">{stat.title}</div>
          </div>
        ))}
      </div>

      {/* Monthly Performance Chart */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Monthly Performance</h3>
        <div className="space-y-3">
          {stats.monthlyStats.map((month, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-800">{month.month}</span>
                <div className="flex items-center space-x-4 text-sm">
                  <span className="text-blue-600">
                    👥 {month.referrals} referrals
                  </span>
                  <span className="text-green-600">
                    💰 {formatCurrency(month.commission)}
                  </span>
                  <span className="text-purple-600">
                    📈 {formatCurrency(month.volume)}
                  </span>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500"
                  style={{
                    width: `${Math.min((month.commission / Math.max(...stats.monthlyStats.map(m => m.commission))) * 100, 100)}%`
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200">
        <h3 className="font-semibold text-blue-800 mb-3">📊 Performance Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-blue-700 mb-2">Top Performing Metrics:</h4>
            <ul className="space-y-1 text-blue-600">
              <li className="flex items-center">
                <span className="mr-2">🏆</span>
                <span>Conversion rate: {formatPercentage(stats.conversionRate)}</span>
              </li>
              <li className="flex items-center">
                <span className="mr-2">💎</span>
                <span>Avg investment: {formatCurrency(stats.averageInvestmentPerReferral)}</span>
              </li>
              <li className="flex items-center">
                <span className="mr-2">🚀</span>
                <span>Active referrals: {stats.activeReferrals}/{stats.totalReferrals}</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-blue-700 mb-2">Growth Opportunities:</h4>
            <ul className="space-y-1 text-blue-600">
              <li className="flex items-center">
                <span className="mr-2">📈</span>
                <span>Share more on social media</span>
              </li>
              <li className="flex items-center">
                <span className="mr-2">🎯</span>
                <span>Follow up with inactive referrals</span>
              </li>
              <li className="flex items-center">
                <span className="mr-2">💡</span>
                <span>Share success stories</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Commission Breakdown */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-semibold text-green-800 mb-2">💰 Commission Breakdown</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-green-700">USDT Commission:</span>
              <span className="font-medium text-green-800">
                {formatCurrency(stats.totalCommissionEarned * 0.6)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700">Share Commission:</span>
              <span className="font-medium text-green-800">
                {Math.round(stats.totalCommissionEarned * 0.4)} shares
              </span>
            </div>
            <div className="flex justify-between border-t border-green-300 pt-2">
              <span className="text-green-700 font-medium">Total Value:</span>
              <span className="font-bold text-green-800">
                {formatCurrency(stats.totalCommissionEarned)}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-800 mb-2">⏳ Pending Commission</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-yellow-700">Under Review:</span>
              <span className="font-medium text-yellow-800">
                {formatCurrency(stats.pendingCommission)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-yellow-700">Expected Payout:</span>
              <span className="font-medium text-yellow-800">1-3 days</span>
            </div>
            <div className="text-xs text-yellow-600 mt-2">
              Commission is paid when referral investments are approved
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
