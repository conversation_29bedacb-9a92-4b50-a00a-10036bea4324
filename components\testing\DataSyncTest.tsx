'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { DataValidationService, dataSyncService } from '@/lib/data-sync'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface SyncTestResult {
  test: string
  status: 'pending' | 'success' | 'error' | 'warning'
  message: string
  details?: any
  timestamp: Date
}

export default function DataSyncTest() {
  const { user, loading } = useAuth()
  const [testResults, setTestResults] = useState<SyncTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [realTimeStatus, setRealTimeStatus] = useState<'disconnected' | 'connected' | 'error'>('disconnected')

  const addResult = (result: Omit<SyncTestResult, 'timestamp'>) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date() }])
  }

  const runDataSyncTests = async () => {
    setIsRunning(true)
    setTestResults([])

    // Test 1: User Authentication and Profile Sync
    addResult({
      test: 'User Authentication Sync',
      status: user ? 'success' : 'error',
      message: user ? 'User authenticated and profile loaded' : 'No authenticated user',
      details: user ? {
        id: user.id,
        telegram_id: user.user_metadata?.telegram_id,
        profile: user.telegram_profile
      } : null
    })

    if (user?.telegram_profile?.id) {
      const userId = user.telegram_profile.id

      // Test 2: Data Validation Service
      try {
        const validationResult = await DataValidationService.validateUserData(userId)
        
        addResult({
          test: 'User Data Validation',
          status: validationResult.isValid ? 'success' : validationResult.errors.length > 0 ? 'error' : 'warning',
          message: validationResult.isValid 
            ? 'All user data is valid'
            : `${validationResult.errors.length} errors, ${validationResult.warnings.length} warnings`,
          details: validationResult
        })
      } catch (error) {
        addResult({
          test: 'User Data Validation',
          status: 'error',
          message: 'Validation service failed',
          details: error
        })
      }

      // Test 3: Business Logic Validation
      try {
        const businessValidation = await DataValidationService.validateBusinessLogic()
        
        addResult({
          test: 'Business Logic Validation',
          status: businessValidation.isValid ? 'success' : 'warning',
          message: businessValidation.isValid 
            ? 'Business logic is consistent'
            : `${businessValidation.errors.length} errors, ${businessValidation.warnings.length} warnings`,
          details: businessValidation
        })
      } catch (error) {
        addResult({
          test: 'Business Logic Validation',
          status: 'error',
          message: 'Business validation failed',
          details: error
        })
      }

      // Test 4: Real-time Subscription Test
      try {
        let subscriptionWorking = false
        
        const unsubscribe = dataSyncService.subscribeToTable(
          'telegram_users',
          (payload) => {
            subscriptionWorking = true
            addResult({
              test: 'Real-time Update Received',
              status: 'success',
              message: `Received ${payload.eventType} event`,
              details: payload
            })
          },
          { column: 'id', value: userId }
        )

        // Wait a moment to see if subscription works
        setTimeout(() => {
          if (!subscriptionWorking) {
            addResult({
              test: 'Real-time Subscription',
              status: 'warning',
              message: 'Subscription active but no events received',
              details: { userId, table: 'telegram_users' }
            })
          }
          unsubscribe()
        }, 2000)

        addResult({
          test: 'Real-time Subscription Setup',
          status: 'success',
          message: 'Successfully subscribed to real-time updates',
          details: { userId, table: 'telegram_users' }
        })

        setRealTimeStatus('connected')
      } catch (error) {
        addResult({
          test: 'Real-time Subscription Setup',
          status: 'error',
          message: 'Failed to setup real-time subscription',
          details: error
        })
        setRealTimeStatus('error')
      }

      // Test 5: Database Consistency Check
      try {
        const response = await fetch('/api/data-sync/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId }),
          credentials: 'include'
        })

        if (response.ok) {
          const data = await response.json()
          addResult({
            test: 'Database Consistency Check',
            status: data.isConsistent ? 'success' : 'warning',
            message: data.isConsistent 
              ? 'Database is consistent'
              : `${data.issues?.length || 0} consistency issues found`,
            details: data
          })
        } else {
          addResult({
            test: 'Database Consistency Check',
            status: 'error',
            message: `API returned ${response.status}`,
            details: await response.text()
          })
        }
      } catch (error) {
        addResult({
          test: 'Database Consistency Check',
          status: 'error',
          message: 'Failed to check database consistency',
          details: error
        })
      }

      // Test 6: Cross-Platform Data Sync
      try {
        const syncTestData = {
          telegram_id: user.user_metadata?.telegram_id,
          test_timestamp: new Date().toISOString(),
          test_data: 'sync_validation_test'
        }

        // Simulate a data update to test sync
        const response = await fetch('/api/data-sync/test', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(syncTestData),
          credentials: 'include'
        })

        if (response.ok) {
          const result = await response.json()
          addResult({
            test: 'Cross-Platform Sync Test',
            status: result.success ? 'success' : 'warning',
            message: result.success 
              ? 'Cross-platform sync working'
              : 'Sync test completed with warnings',
            details: result
          })
        } else {
          addResult({
            test: 'Cross-Platform Sync Test',
            status: 'error',
            message: 'Sync test API failed',
            details: await response.text()
          })
        }
      } catch (error) {
        addResult({
          test: 'Cross-Platform Sync Test',
          status: 'error',
          message: 'Cross-platform sync test failed',
          details: error
        })
      }

      // Test 7: Data Integrity Validation
      const integrityTests = [
        {
          name: 'Share Purchase Integrity',
          check: async () => {
            const response = await fetch(`/api/shares?userId=${userId}`)
            const shares = await response.json()
            return {
              valid: Array.isArray(shares) && shares.every(s => s.user_id === userId),
              count: shares.length,
              details: shares
            }
          }
        },
        {
          name: 'Payment Transaction Integrity',
          check: async () => {
            const response = await fetch(`/api/payments?userId=${userId}`)
            const payments = await response.json()
            return {
              valid: Array.isArray(payments) && payments.every(p => p.user_id === userId),
              count: payments.length,
              details: payments
            }
          }
        },
        {
          name: 'Commission Balance Integrity',
          check: async () => {
            const response = await fetch(`/api/referrals`)
            const data = await response.json()
            return {
              valid: data.commissionBalance && typeof data.commissionBalance.usdt_balance === 'number',
              balance: data.commissionBalance?.usdt_balance || 0,
              details: data.commissionBalance
            }
          }
        }
      ]

      for (const test of integrityTests) {
        try {
          const result = await test.check()
          addResult({
            test: test.name,
            status: result.valid ? 'success' : 'error',
            message: result.valid 
              ? `Integrity check passed (${result.count || 'N/A'} records)`
              : 'Data integrity issues detected',
            details: result
          })
        } catch (error) {
          addResult({
            test: test.name,
            status: 'error',
            message: 'Integrity check failed',
            details: error
          })
        }
      }

      // Test 8: Conflict Resolution Test
      try {
        const conflictTestResult = await fetch('/api/data-sync/conflict-test', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId }),
          credentials: 'include'
        })

        if (conflictTestResult.ok) {
          const data = await conflictTestResult.json()
          addResult({
            test: 'Conflict Resolution Test',
            status: data.success ? 'success' : 'warning',
            message: data.success 
              ? 'Conflict resolution working correctly'
              : 'Conflict resolution needs attention',
            details: data
          })
        } else {
          addResult({
            test: 'Conflict Resolution Test',
            status: 'warning',
            message: 'Conflict resolution test endpoint not available',
            details: 'API endpoint may not be implemented'
          })
        }
      } catch (error) {
        addResult({
          test: 'Conflict Resolution Test',
          status: 'warning',
          message: 'Conflict resolution test not available',
          details: error
        })
      }
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: SyncTestResult['status']) => {
    switch (status) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      case 'pending':
        return '⏳'
      default:
        return '❓'
    }
  }

  const getStatusColor = (status: SyncTestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'warning':
        return 'text-yellow-600'
      case 'pending':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  const getRealTimeStatusColor = () => {
    switch (realTimeStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50'
      case 'error':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="text-center mt-2 text-gray-600">Loading data sync system...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Data Synchronization & Validation Test</h2>
      
      {/* Real-time Status */}
      <div className="mb-6">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getRealTimeStatusColor()}`}>
          <span className="mr-2">🔄</span>
          Real-time Status: {realTimeStatus}
        </div>
      </div>

      <div className="mb-6">
        <Button
          onClick={runDataSyncTests}
          disabled={isRunning}
          className="bg-green-600 hover:bg-green-700 disabled:bg-green-300 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
        >
          {isRunning ? 'Running Tests...' : 'Run Data Sync Tests'}
        </Button>
      </div>

      {testResults.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Test Results:</h3>
          
          {testResults.map((result, index) => (
            <Card key={index} className="p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">
                  {getStatusIcon(result.status)} {result.test}
                </h4>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${getStatusColor(result.status)}`}>
                    {result.status.toUpperCase()}
                  </span>
                  <span className="text-xs text-gray-500">
                    {result.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
              
              <p className="text-gray-600 mb-2">{result.message}</p>
              
              {result.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-blue-600 hover:text-blue-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(result.details, null, 2)}
                  </pre>
                </details>
              )}
            </Card>
          ))}

          {/* Summary */}
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">Test Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-green-600">Passed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'success').length}</span>
              </div>
              <div>
                <span className="font-medium text-red-600">Failed:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'error').length}</span>
              </div>
              <div>
                <span className="font-medium text-yellow-600">Warnings:</span>
                <span className="ml-2">{testResults.filter(r => r.status === 'warning').length}</span>
              </div>
              <div>
                <span className="font-medium text-blue-600">Total:</span>
                <span className="ml-2">{testResults.length}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {!user && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            <strong>Note:</strong> You need to be authenticated to run data synchronization tests. 
            Please sign in with Telegram first.
          </p>
        </div>
      )}
    </div>
  )
}
