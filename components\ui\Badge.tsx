'use client'

import { ReactNode, HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'

interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  children: ReactNode
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'gold' | 'outline'
  size?: 'xs' | 'sm' | 'md' | 'lg'
  rounded?: boolean
  dot?: boolean
  pulse?: boolean
  removable?: boolean
  onRemove?: () => void
}

export default function Badge({
  children,
  variant = 'default',
  size = 'sm',
  rounded = true,
  dot = false,
  pulse = false,
  removable = false,
  onRemove,
  className,
  ...props
}: BadgeProps) {
  const baseClasses = 'inline-flex items-center font-medium transition-all duration-200'
  
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
    primary: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700',
    success: 'bg-green-100 text-green-800 hover:bg-green-200',
    warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    error: 'bg-red-100 text-red-800 hover:bg-red-200',
    gold: 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 hover:from-yellow-200 hover:to-yellow-300',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50'
  }
  
  const sizeClasses = {
    xs: 'px-1.5 py-0.5 text-xs gap-1',
    sm: 'px-2 py-1 text-xs gap-1',
    md: 'px-2.5 py-1.5 text-sm gap-1.5',
    lg: 'px-3 py-2 text-sm gap-2'
  }
  
  const roundedClass = rounded ? 'rounded-full' : 'rounded-md'
  const pulseClass = pulse ? 'animate-pulse' : ''
  
  const dotSizeClasses = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3'
  }
  
  const dotColorClasses = {
    default: 'bg-gray-400',
    primary: 'bg-blue-500',
    secondary: 'bg-gray-400',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    gold: 'bg-yellow-500',
    outline: 'bg-gray-400'
  }

  return (
    <span
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        roundedClass,
        pulseClass,
        className
      )}
      {...props}
    >
      {dot && (
        <span
          className={cn(
            'rounded-full flex-shrink-0',
            dotSizeClasses[size],
            dotColorClasses[variant],
            pulse && 'animate-ping'
          )}
        />
      )}
      
      <span className="flex-1">{children}</span>
      
      {removable && onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="flex-shrink-0 ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors duration-200"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </span>
  )
}

// Status Badge Component
interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'approved' | 'rejected' | 'processing'
  size?: 'xs' | 'sm' | 'md' | 'lg'
  showDot?: boolean
  className?: string
}

export function StatusBadge({ 
  status, 
  size = 'sm', 
  showDot = true, 
  className 
}: StatusBadgeProps) {
  const statusConfig = {
    active: {
      variant: 'success' as const,
      label: 'Active',
      pulse: false
    },
    inactive: {
      variant: 'default' as const,
      label: 'Inactive',
      pulse: false
    },
    pending: {
      variant: 'warning' as const,
      label: 'Pending',
      pulse: true
    },
    approved: {
      variant: 'success' as const,
      label: 'Approved',
      pulse: false
    },
    rejected: {
      variant: 'error' as const,
      label: 'Rejected',
      pulse: false
    },
    processing: {
      variant: 'primary' as const,
      label: 'Processing',
      pulse: true
    }
  }
  
  const config = statusConfig[status]

  return (
    <Badge
      variant={config.variant}
      size={size}
      dot={showDot}
      pulse={config.pulse}
      className={className}
    >
      {config.label}
    </Badge>
  )
}

// Notification Badge Component
interface NotificationBadgeProps {
  count: number
  max?: number
  size?: 'xs' | 'sm' | 'md'
  variant?: 'primary' | 'error' | 'warning'
  className?: string
}

export function NotificationBadge({ 
  count, 
  max = 99, 
  size = 'xs', 
  variant = 'error',
  className 
}: NotificationBadgeProps) {
  if (count <= 0) return null
  
  const displayCount = count > max ? `${max}+` : count.toString()
  
  const sizeClasses = {
    xs: 'min-w-[16px] h-4 text-xs',
    sm: 'min-w-[20px] h-5 text-xs',
    md: 'min-w-[24px] h-6 text-sm'
  }

  return (
    <span
      className={cn(
        'absolute -top-1 -right-1 flex items-center justify-center rounded-full font-bold text-white',
        sizeClasses[size],
        variant === 'primary' && 'bg-blue-500',
        variant === 'error' && 'bg-red-500',
        variant === 'warning' && 'bg-yellow-500',
        className
      )}
    >
      {displayCount}
    </span>
  )
}

// Badge Group Component
interface BadgeGroupProps {
  children: ReactNode
  className?: string
  spacing?: 'tight' | 'normal' | 'loose'
  wrap?: boolean
}

export function BadgeGroup({ 
  children, 
  className, 
  spacing = 'normal',
  wrap = true 
}: BadgeGroupProps) {
  const spacingClasses = {
    tight: 'gap-1',
    normal: 'gap-2',
    loose: 'gap-3'
  }
  
  const wrapClass = wrap ? 'flex-wrap' : 'flex-nowrap'

  return (
    <div className={cn(
      'flex items-center',
      spacingClasses[spacing],
      wrapClass,
      className
    )}>
      {children}
    </div>
  )
}

// Interactive Badge Component
interface InteractiveBadgeProps extends BadgeProps {
  selected?: boolean
  onClick?: () => void
}

export function InteractiveBadge({
  selected = false,
  onClick,
  className,
  ...props
}: InteractiveBadgeProps) {
  const interactiveClasses = onClick ? 'cursor-pointer hover:scale-105 active:scale-95' : ''
  const selectedClasses = selected ? 'ring-2 ring-blue-500 ring-offset-1' : ''

  return (
    <Badge
      className={cn(
        interactiveClasses,
        selectedClasses,
        className
      )}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      {...props}
    />
  )
}

// Progress Badge Component
interface ProgressBadgeProps {
  current: number
  total: number
  size?: 'xs' | 'sm' | 'md'
  variant?: 'primary' | 'success' | 'warning'
  showPercentage?: boolean
  className?: string
}

export function ProgressBadge({
  current,
  total,
  size = 'sm',
  variant = 'primary',
  showPercentage = false,
  className
}: ProgressBadgeProps) {
  const percentage = Math.round((current / total) * 100)
  const displayText = showPercentage ? `${percentage}%` : `${current}/${total}`
  
  const variantClasses = {
    primary: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800'
  }

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <Badge variant="outline" size={size} className={variantClasses[variant]}>
        {displayText}
      </Badge>
      <div 
        className={cn(
          'absolute inset-y-0 left-0 transition-all duration-300',
          variant === 'primary' && 'bg-blue-200',
          variant === 'success' && 'bg-green-200',
          variant === 'warning' && 'bg-yellow-200'
        )}
        style={{ width: `${percentage}%` }}
      />
    </div>
  )
}
