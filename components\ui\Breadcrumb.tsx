'use client'

import { ReactNode } from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

// Breadcrumb Item Interface
export interface BreadcrumbItem {
  label: string
  href?: string
  icon?: ReactNode
  current?: boolean
}

// Breadcrumb Component Props
interface BreadcrumbProps {
  items: BreadcrumbItem[]
  separator?: ReactNode
  className?: string
  maxItems?: number
  showHome?: boolean
  homeIcon?: ReactNode
  homeHref?: string
}

export function Breadcrumb({
  items,
  separator,
  className,
  maxItems,
  showHome = true,
  homeIcon,
  homeHref = '/dashboard'
}: BreadcrumbProps) {
  // Default separator
  const defaultSeparator = (
    <svg
      className="w-4 h-4 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 5l7 7-7 7"
      />
    </svg>
  )

  // Default home icon
  const defaultHomeIcon = (
    <svg
      className="w-4 h-4"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
      />
    </svg>
  )

  // Process items with truncation if needed
  let processedItems = [...items]
  
  if (maxItems && processedItems.length > maxItems) {
    const firstItems = processedItems.slice(0, 1)
    const lastItems = processedItems.slice(-(maxItems - 2))
    processedItems = [
      ...firstItems,
      { label: '...', href: undefined },
      ...lastItems
    ]
  }

  // Add home item if requested
  if (showHome) {
    processedItems.unshift({
      label: 'Dashboard',
      href: homeHref,
      icon: homeIcon || defaultHomeIcon
    })
  }

  return (
    <nav
      className={cn('flex items-center space-x-2 text-sm', className)}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-2">
        {processedItems.map((item, index) => (
          <li key={index} className="flex items-center">
            {/* Separator (not for first item) */}
            {index > 0 && (
              <span className="mx-2 flex-shrink-0">
                {separator || defaultSeparator}
              </span>
            )}

            {/* Breadcrumb Item */}
            <div className="flex items-center">
              {item.href && !item.current ? (
                <Link
                  href={item.href}
                  className="flex items-center text-gray-500 hover:text-gray-700 transition-colors duration-200"
                >
                  {item.icon && (
                    <span className="mr-1.5 flex-shrink-0">{item.icon}</span>
                  )}
                  <span className="truncate">{item.label}</span>
                </Link>
              ) : (
                <span
                  className={cn(
                    'flex items-center',
                    item.current
                      ? 'text-gray-900 font-medium'
                      : 'text-gray-500'
                  )}
                  aria-current={item.current ? 'page' : undefined}
                >
                  {item.icon && (
                    <span className="mr-1.5 flex-shrink-0">{item.icon}</span>
                  )}
                  <span className="truncate">{item.label}</span>
                </span>
              )}
            </div>
          </li>
        ))}
      </ol>
    </nav>
  )
}

// Breadcrumb Builder Hook
export function useBreadcrumb() {
  const buildBreadcrumb = (path: string): BreadcrumbItem[] => {
    const segments = path.split('/').filter(Boolean)
    const items: BreadcrumbItem[] = []

    // Build breadcrumb items from path segments
    segments.forEach((segment, index) => {
      const href = '/' + segments.slice(0, index + 1).join('/')
      const isLast = index === segments.length - 1

      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      items.push({
        label,
        href: isLast ? undefined : href,
        current: isLast
      })
    })

    return items
  }

  return { buildBreadcrumb }
}

// Page Header with Breadcrumb
interface PageHeaderProps {
  title: string
  subtitle?: string
  breadcrumbItems?: BreadcrumbItem[]
  actions?: ReactNode
  className?: string
}

export function PageHeader({
  title,
  subtitle,
  breadcrumbItems = [],
  actions,
  className
}: PageHeaderProps) {
  return (
    <div className={cn('mb-8', className)}>
      {/* Breadcrumb */}
      {breadcrumbItems.length > 0 && (
        <div className="mb-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
      )}

      {/* Header Content */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex-1 min-w-0">
          <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl sm:truncate">
            {title}
          </h1>
          {subtitle && (
            <p className="mt-1 text-sm text-gray-500 sm:truncate">
              {subtitle}
            </p>
          )}
        </div>

        {/* Actions */}
        {actions && (
          <div className="mt-4 sm:mt-0 sm:ml-4 flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

// Breadcrumb with Back Button
interface BreadcrumbWithBackProps {
  items: BreadcrumbItem[]
  onBack?: () => void
  backLabel?: string
  className?: string
}

export function BreadcrumbWithBack({
  items,
  onBack,
  backLabel = 'Back',
  className
}: BreadcrumbWithBackProps) {
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else if (typeof window !== 'undefined') {
      window.history.back()
    }
  }

  return (
    <div className={cn('flex items-center space-x-4', className)}>
      {/* Back Button */}
      <button
        onClick={handleBack}
        className="flex items-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
      >
        <svg
          className="w-4 h-4 mr-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
        {backLabel}
      </button>

      {/* Separator */}
      <span className="text-gray-300">|</span>

      {/* Breadcrumb */}
      <Breadcrumb items={items} showHome={false} />
    </div>
  )
}
