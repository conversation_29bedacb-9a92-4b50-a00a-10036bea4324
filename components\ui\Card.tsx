'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface CardProps {
  children: ReactNode
  className?: string
  variant?: 'default' | 'elevated' | 'outlined' | 'gradient'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  clickable?: boolean
  onClick?: () => void
}

export function Card({
  children,
  className,
  variant = 'default',
  padding = 'md',
  hover = false,
  clickable = false,
  onClick
}: CardProps) {
  const baseClasses = 'rounded-lg transition-all duration-200'
  
  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm',
    elevated: 'bg-white shadow-lg border border-gray-100',
    outlined: 'bg-white border-2 border-gray-300',
    gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200'
  }
  
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  }
  
  const hoverClasses = hover ? 'hover:shadow-md hover:-translate-y-1' : ''
  const clickableClasses = clickable ? 'cursor-pointer hover:shadow-lg' : ''
  
  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        paddingClasses[padding],
        hoverClasses,
        clickableClasses,
        className
      )}
      onClick={onClick}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          onClick?.()
        }
      } : undefined}
    >
      {children}
    </div>
  )
}

interface CardHeaderProps {
  children: ReactNode
  className?: string
}

export function CardHeader({ children, className }: CardHeaderProps) {
  return (
    <div className={cn('mb-4 pb-2 border-b border-gray-100', className)}>
      {children}
    </div>
  )
}

interface CardTitleProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function CardTitle({ children, className, size = 'lg' }: CardTitleProps) {
  const sizeClasses = {
    sm: 'text-sm font-medium',
    md: 'text-base font-semibold',
    lg: 'text-lg font-semibold',
    xl: 'text-xl font-bold'
  }
  
  return (
    <h3 className={cn(sizeClasses[size], 'text-gray-800', className)}>
      {children}
    </h3>
  )
}

interface CardContentProps {
  children: ReactNode
  className?: string
}

export function CardContent({ children, className }: CardContentProps) {
  return (
    <div className={cn('text-gray-600', className)}>
      {children}
    </div>
  )
}

interface CardFooterProps {
  children: ReactNode
  className?: string
}

export function CardFooter({ children, className }: CardFooterProps) {
  return (
    <div className={cn('mt-4 pt-4 border-t border-gray-100', className)}>
      {children}
    </div>
  )
}

// Stat Card Component
interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon?: ReactNode
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  color?: 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'gray'
  className?: string
}

export function StatCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
  className
}: StatCardProps) {
  const colorClasses = {
    blue: 'border-t-blue-500 text-blue-600',
    green: 'border-t-green-500 text-green-600',
    yellow: 'border-t-yellow-500 text-yellow-600',
    purple: 'border-t-purple-500 text-purple-600',
    red: 'border-t-red-500 text-red-600',
    gray: 'border-t-gray-500 text-gray-600'
  }
  
  const trendColors = {
    up: 'text-green-600 bg-green-50',
    down: 'text-red-600 bg-red-50',
    neutral: 'text-gray-600 bg-gray-50'
  }
  
  const trendIcons = {
    up: '↗',
    down: '↘',
    neutral: '→'
  }
  
  return (
    <Card className={cn('border-t-4', colorClasses[color].split(' ')[0], className)} padding="lg">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={cn('text-3xl font-bold mb-1', colorClasses[color].split(' ')[1])}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {subtitle && (
            <p className="text-sm text-gray-500">{subtitle}</p>
          )}
        </div>
        
        {icon && (
          <div className={cn('text-2xl', colorClasses[color].split(' ')[1])}>
            {icon}
          </div>
        )}
      </div>
      
      {trend && (
        <div className="mt-4 flex items-center">
          <span className={cn(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            trendColors[trend.direction]
          )}>
            <span className="mr-1">{trendIcons[trend.direction]}</span>
            {trend.value}%
          </span>
          <span className="ml-2 text-sm text-gray-500">{trend.label}</span>
        </div>
      )}
    </Card>
  )
}

// Loading Card Component
export function LoadingCard({ className }: { className?: string }) {
  return (
    <Card className={cn('animate-pulse', className)} padding="lg">
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded w-1/4"></div>
        <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        <div className="h-3 bg-gray-200 rounded w-1/3"></div>
      </div>
    </Card>
  )
}

// Error Card Component
interface ErrorCardProps {
  title?: string
  message: string
  onRetry?: () => void
  className?: string
}

export function ErrorCard({
  title = 'Error',
  message,
  onRetry,
  className
}: ErrorCardProps) {
  return (
    <Card className={cn('border-red-200 bg-red-50', className)} padding="lg">
      <div className="text-center">
        <div className="text-red-500 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-red-800 mb-2">{title}</h3>
        <p className="text-red-700 mb-4">{message}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Try Again
          </button>
        )}
      </div>
    </Card>
  )
}
