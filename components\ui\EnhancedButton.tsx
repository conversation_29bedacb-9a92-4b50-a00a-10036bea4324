'use client'

import { ReactNode, ButtonHTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { designTokens } from '@/lib/design-system'

interface EnhancedButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success' | 'gold' | 'gradient'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: boolean
  pulse?: boolean
  glow?: boolean
}

export const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  rounded = 'lg',
  shadow = false,
  pulse = false,
  glow = false,
  className,
  disabled,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden group'
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white focus:ring-blue-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5',
    secondary: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 hover:border-gray-400 focus:ring-gray-500 shadow-sm hover:shadow-md',
    outline: 'border-2 border-primary-500 hover:bg-primary-50 text-primary-600 hover:text-primary-700 focus:ring-primary-500 hover:border-primary-600',
    ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500 hover:shadow-sm',
    danger: 'bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white focus:ring-red-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5',
    success: 'bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white focus:ring-green-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5',
    gold: 'bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black focus:ring-yellow-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5',
    gradient: 'bg-gradient-to-r from-yellow-400 via-blue-500 to-yellow-600 hover:from-yellow-500 hover:via-blue-600 hover:to-yellow-700 text-white focus:ring-blue-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
  }
  
  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs gap-1 min-h-[28px]',
    sm: 'px-3 py-2 text-sm gap-1.5 min-h-[32px]',
    md: 'px-4 py-2.5 text-sm gap-2 min-h-[40px]',
    lg: 'px-6 py-3 text-base gap-2 min-h-[44px]',
    xl: 'px-8 py-4 text-lg gap-3 min-h-[52px]'
  }
  
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full'
  }
  
  const shadowClass = shadow ? 'shadow-lg hover:shadow-xl' : ''
  const pulseClass = pulse ? 'animate-pulse' : ''
  const glowClass = glow ? 'before:absolute before:inset-0 before:rounded-lg before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700' : ''
  const widthClass = fullWidth ? 'w-full' : ''
  
  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  return (
    <button
      ref={ref}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        roundedClasses[rounded],
        shadowClass,
        pulseClass,
        glowClass,
        widthClass,
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {/* Background animation overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
      
      {/* Content */}
      <div className="relative flex items-center justify-center gap-2">
        {loading && <LoadingSpinner />}
        
        {!loading && icon && iconPosition === 'left' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        
        <span className="flex-1">{children}</span>
        
        {!loading && icon && iconPosition === 'right' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </div>
    </button>
  )
})

EnhancedButton.displayName = 'EnhancedButton'

// Button Group Component
interface ButtonGroupProps {
  children: ReactNode
  className?: string
  orientation?: 'horizontal' | 'vertical'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'outline'
}

export function ButtonGroup({
  children,
  className,
  orientation = 'horizontal',
  size = 'md',
  variant = 'secondary'
}: ButtonGroupProps) {
  const orientationClasses = {
    horizontal: 'flex flex-row',
    vertical: 'flex flex-col'
  }
  
  const spacingClasses = {
    horizontal: 'space-x-0 [&>*:not(:first-child)]:ml-[-1px] [&>*:first-child]:rounded-r-none [&>*:last-child]:rounded-l-none [&>*:not(:first-child):not(:last-child)]:rounded-none',
    vertical: 'space-y-0 [&>*:not(:first-child)]:mt-[-1px] [&>*:first-child]:rounded-b-none [&>*:last-child]:rounded-t-none [&>*:not(:first-child):not(:last-child)]:rounded-none'
  }

  return (
    <div
      className={cn(
        orientationClasses[orientation],
        spacingClasses[orientation],
        'inline-flex',
        className
      )}
      role="group"
    >
      {children}
    </div>
  )
}

// Floating Action Button
interface FABProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: ReactNode
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  size?: 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'gold'
  tooltip?: string
}

export function FloatingActionButton({
  icon,
  position = 'bottom-right',
  size = 'md',
  variant = 'primary',
  tooltip,
  className,
  ...props
}: FABProps) {
  const positionClasses = {
    'bottom-right': 'fixed bottom-6 right-6',
    'bottom-left': 'fixed bottom-6 left-6',
    'top-right': 'fixed top-6 right-6',
    'top-left': 'fixed top-6 left-6'
  }
  
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-14 h-14',
    lg: 'w-16 h-16'
  }
  
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white shadow-lg hover:shadow-xl',
    gold: 'bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black shadow-lg hover:shadow-xl'
  }

  return (
    <button
      className={cn(
        'rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 z-50',
        positionClasses[position],
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      title={tooltip}
      {...props}
    >
      {icon}
    </button>
  )
}

// Icon Button
interface IconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon: ReactNode
  size?: 'xs' | 'sm' | 'md' | 'lg'
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline'
  rounded?: boolean
  tooltip?: string
}

export function IconButton({
  icon,
  size = 'md',
  variant = 'ghost',
  rounded = true,
  tooltip,
  className,
  ...props
}: IconButtonProps) {
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  }
  
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900',
    ghost: 'hover:bg-gray-100 text-gray-600 hover:text-gray-900',
    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'
  }
  
  const roundedClass = rounded ? 'rounded-full' : 'rounded-md'

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed',
        sizeClasses[size],
        variantClasses[variant],
        roundedClass,
        className
      )}
      title={tooltip}
      {...props}
    >
      {icon}
    </button>
  )
}
