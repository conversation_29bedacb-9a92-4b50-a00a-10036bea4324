'use client'

import { ReactNode, HTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'
import { designTokens } from '@/lib/design-system'

interface EnhancedCardProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode
  variant?: 'default' | 'outlined' | 'elevated' | 'gold' | 'gradient' | 'glass' | 'dark'
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
  interactive?: boolean
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  border?: boolean
  glow?: boolean
  loading?: boolean
}

export const EnhancedCard = forwardRef<HTMLDivElement, EnhancedCardProps>(({
  children,
  variant = 'default',
  padding = 'md',
  hover = false,
  interactive = false,
  rounded = 'lg',
  shadow = 'sm',
  border = false,
  glow = false,
  loading = false,
  className,
  onClick,
  ...props
}, ref) => {
  const baseClasses = 'relative transition-all duration-300 ease-out'
  
  const variantClasses = {
    default: 'bg-white border border-gray-200',
    outlined: 'bg-white border-2 border-gray-300',
    elevated: 'bg-white border-0',
    gold: 'bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200',
    gradient: 'bg-gradient-to-br from-blue-50 via-white to-yellow-50 border border-blue-200',
    glass: 'bg-white/80 backdrop-blur-sm border border-white/20',
    dark: 'bg-gray-900 border border-gray-700 text-white'
  }
  
  const paddingClasses = {
    none: 'p-0',
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  }
  
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl'
  }
  
  const shadowClasses = {
    none: 'shadow-none',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  }
  
  const hoverClasses = hover ? {
    default: 'hover:shadow-md hover:border-gray-300',
    outlined: 'hover:shadow-lg hover:border-gray-400',
    elevated: 'hover:shadow-xl hover:-translate-y-1',
    gold: 'hover:shadow-lg hover:shadow-yellow-200/50 hover:border-yellow-300',
    gradient: 'hover:shadow-lg hover:shadow-blue-200/50',
    glass: 'hover:bg-white/90 hover:shadow-lg',
    dark: 'hover:shadow-xl hover:shadow-gray-900/50 hover:border-gray-600'
  }[variant] : ''
  
  const interactiveClasses = interactive || onClick ? 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' : ''
  
  const glowClasses = glow ? {
    default: 'ring-1 ring-blue-500/20 shadow-blue-500/20',
    outlined: 'ring-1 ring-blue-500/20 shadow-blue-500/20',
    elevated: 'ring-1 ring-blue-500/20 shadow-blue-500/20',
    gold: 'ring-1 ring-yellow-500/30 shadow-yellow-500/30',
    gradient: 'ring-1 ring-blue-500/20 shadow-blue-500/20',
    glass: 'ring-1 ring-white/30 shadow-white/20',
    dark: 'ring-1 ring-gray-500/30 shadow-gray-500/30'
  }[variant] : ''
  
  const borderClass = border ? 'border-2' : ''

  if (loading) {
    return (
      <div
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          paddingClasses[padding],
          roundedClasses[rounded],
          shadowClasses[shadow],
          'animate-pulse',
          className
        )}
        {...props}
      >
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    )
  }

  return (
    <div
      ref={ref}
      className={cn(
        baseClasses,
        variantClasses[variant],
        paddingClasses[padding],
        roundedClasses[rounded],
        shadowClasses[shadow],
        hoverClasses,
        interactiveClasses,
        glowClasses,
        borderClass,
        className
      )}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      {...props}
    >
      {glow && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out rounded-lg" />
      )}
      {children}
    </div>
  )
})

EnhancedCard.displayName = 'EnhancedCard'

// Card Header Component
interface CardHeaderProps {
  children: ReactNode
  className?: string
  divider?: boolean
}

export function CardHeader({ children, className, divider = false }: CardHeaderProps) {
  return (
    <div className={cn(
      'flex items-center justify-between',
      divider && 'border-b border-gray-200 pb-4 mb-4',
      className
    )}>
      {children}
    </div>
  )
}

// Card Title Component
interface CardTitleProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function CardTitle({ children, className, size = 'lg' }: CardTitleProps) {
  const sizeClasses = {
    sm: 'text-sm font-medium',
    md: 'text-base font-semibold',
    lg: 'text-lg font-semibold',
    xl: 'text-xl font-bold'
  }
  
  return (
    <h3 className={cn(sizeClasses[size], 'text-gray-900', className)}>
      {children}
    </h3>
  )
}

// Card Content Component
interface CardContentProps {
  children: ReactNode
  className?: string
}

export function CardContent({ children, className }: CardContentProps) {
  return (
    <div className={cn('text-gray-600', className)}>
      {children}
    </div>
  )
}

// Card Footer Component
interface CardFooterProps {
  children: ReactNode
  className?: string
  divider?: boolean
}

export function CardFooter({ children, className, divider = false }: CardFooterProps) {
  return (
    <div className={cn(
      'flex items-center justify-between',
      divider && 'border-t border-gray-200 pt-4 mt-4',
      className
    )}>
      {children}
    </div>
  )
}

// Stat Card Component
interface StatCardProps {
  title: string
  value: string | number
  change?: {
    value: string | number
    type: 'increase' | 'decrease' | 'neutral'
  }
  icon?: ReactNode
  variant?: 'default' | 'gold' | 'success' | 'warning' | 'error'
  className?: string
}

export function StatCard({ 
  title, 
  value, 
  change, 
  icon, 
  variant = 'default',
  className 
}: StatCardProps) {
  const variantClasses = {
    default: 'border-gray-200',
    gold: 'border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100',
    success: 'border-green-200 bg-gradient-to-br from-green-50 to-green-100',
    warning: 'border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100',
    error: 'border-red-200 bg-gradient-to-br from-red-50 to-red-100'
  }
  
  const changeClasses = {
    increase: 'text-green-600',
    decrease: 'text-red-600',
    neutral: 'text-gray-600'
  }
  
  const changeIcons = {
    increase: '↗',
    decrease: '↘',
    neutral: '→'
  }

  return (
    <EnhancedCard 
      variant={variant === 'default' ? 'default' : 'gold'}
      className={cn(variantClasses[variant], className)}
      hover
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={cn('text-sm font-medium flex items-center gap-1 mt-1', changeClasses[change.type])}>
              <span>{changeIcons[change.type]}</span>
              {change.value}
            </p>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 text-2xl opacity-60">
            {icon}
          </div>
        )}
      </div>
    </EnhancedCard>
  )
}

// Feature Card Component
interface FeatureCardProps {
  title: string
  description: string
  icon?: ReactNode
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export function FeatureCard({ 
  title, 
  description, 
  icon, 
  action,
  className 
}: FeatureCardProps) {
  return (
    <EnhancedCard 
      variant="elevated" 
      hover 
      interactive={!!action}
      onClick={action?.onClick}
      className={cn('group', className)}
    >
      {icon && (
        <div className="text-3xl mb-4 text-blue-600 group-hover:scale-110 transition-transform duration-200">
          {icon}
        </div>
      )}
      <CardTitle size="md" className="mb-2">{title}</CardTitle>
      <CardContent className="mb-4">{description}</CardContent>
      {action && (
        <CardFooter divider>
          <span className="text-blue-600 font-medium group-hover:text-blue-700">
            {action.label} →
          </span>
        </CardFooter>
      )}
    </EnhancedCard>
  )
}
