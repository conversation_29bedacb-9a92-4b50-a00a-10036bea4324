'use client'

import { forwardRef, InputHTMLAttributes, ReactNode, useState } from 'react'
import { cn } from '@/lib/utils'
import { designTokens } from '@/lib/design-system'

interface EnhancedInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  variant?: 'default' | 'filled' | 'outlined' | 'underlined'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  loading?: boolean
  success?: boolean
  clearable?: boolean
  onClear?: () => void
}

export const EnhancedInput = forwardRef<HTMLInputElement, EnhancedInputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  inputSize = 'md',
  fullWidth = true,
  loading = false,
  success = false,
  clearable = false,
  onClear,
  className,
  disabled,
  value,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false)
  
  const baseClasses = 'transition-all duration-200 focus:outline-none'
  
  const variantClasses = {
    default: 'border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
    filled: 'border-0 rounded-lg bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:bg-white',
    outlined: 'border-2 border-gray-300 rounded-lg bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
    underlined: 'border-0 border-b-2 border-gray-300 rounded-none bg-transparent focus:border-blue-500 focus:ring-0'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg'
  }
  
  const stateClasses = {
    error: 'border-red-500 focus:ring-red-500 focus:border-red-500',
    success: 'border-green-500 focus:ring-green-500 focus:border-green-500',
    disabled: 'bg-gray-50 text-gray-500 cursor-not-allowed opacity-60'
  }
  
  const getStateClass = () => {
    if (error) return stateClasses.error
    if (success) return stateClasses.success
    if (disabled) return stateClasses.disabled
    return ''
  }
  
  const widthClass = fullWidth ? 'w-full' : ''
  const iconPaddingLeft = leftIcon ? 'pl-10' : ''
  const iconPaddingRight = rightIcon || clearable || loading ? 'pr-10' : ''

  const LoadingSpinner = () => (
    <svg
      className="animate-spin h-4 w-4 text-gray-400"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  const ClearButton = () => (
    <button
      type="button"
      onClick={onClear}
      className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
      tabIndex={-1}
    >
      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
  )

  return (
    <div className={cn('relative', widthClass)}>
      {/* Label */}
      {label && (
        <label className={cn(
          'block text-sm font-medium mb-2 transition-colors duration-200',
          error ? 'text-red-700' : success ? 'text-green-700' : 'text-gray-700',
          disabled && 'text-gray-500'
        )}>
          {label}
        </label>
      )}
      
      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className={cn(
              'text-gray-400 transition-colors duration-200',
              isFocused && !error && !disabled && 'text-blue-500',
              error && 'text-red-500',
              success && 'text-green-500'
            )}>
              {leftIcon}
            </div>
          </div>
        )}
        
        {/* Input Field */}
        <input
          ref={ref}
          className={cn(
            baseClasses,
            variantClasses[variant],
            sizeClasses[inputSize],
            getStateClass(),
            iconPaddingLeft,
            iconPaddingRight,
            widthClass,
            'placeholder-gray-400',
            className
          )}
          disabled={disabled}
          value={value}
          onFocus={(e) => {
            setIsFocused(true)
            props.onFocus?.(e)
          }}
          onBlur={(e) => {
            setIsFocused(false)
            props.onBlur?.(e)
          }}
          {...props}
        />
        
        {/* Right Icons */}
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
          {loading && <LoadingSpinner />}
          {!loading && clearable && value && <ClearButton />}
          {!loading && !clearable && rightIcon && (
            <div className={cn(
              'text-gray-400 transition-colors duration-200',
              isFocused && !error && !disabled && 'text-blue-500',
              error && 'text-red-500',
              success && 'text-green-500'
            )}>
              {rightIcon}
            </div>
          )}
          {success && !loading && (
            <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          )}
        </div>
      </div>
      
      {/* Helper Text / Error Message */}
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-sm transition-colors duration-200',
          error ? 'text-red-600' : 'text-gray-500'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  )
})

EnhancedInput.displayName = 'EnhancedInput'

// Textarea Component
interface EnhancedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
  variant?: 'default' | 'filled' | 'outlined'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

export const EnhancedTextarea = forwardRef<HTMLTextAreaElement, EnhancedTextareaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  inputSize = 'md',
  fullWidth = true,
  resize = 'vertical',
  className,
  disabled,
  ...props
}, ref) => {
  const baseClasses = 'transition-all duration-200 focus:outline-none'
  
  const variantClasses = {
    default: 'border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
    filled: 'border-0 rounded-lg bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:bg-white',
    outlined: 'border-2 border-gray-300 rounded-lg bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg'
  }
  
  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  }
  
  const stateClasses = {
    error: 'border-red-500 focus:ring-red-500 focus:border-red-500',
    disabled: 'bg-gray-50 text-gray-500 cursor-not-allowed opacity-60'
  }
  
  const getStateClass = () => {
    if (error) return stateClasses.error
    if (disabled) return stateClasses.disabled
    return ''
  }
  
  const widthClass = fullWidth ? 'w-full' : ''

  return (
    <div className={cn('relative', widthClass)}>
      {/* Label */}
      {label && (
        <label className={cn(
          'block text-sm font-medium mb-2 transition-colors duration-200',
          error ? 'text-red-700' : 'text-gray-700',
          disabled && 'text-gray-500'
        )}>
          {label}
        </label>
      )}
      
      {/* Textarea */}
      <textarea
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[inputSize],
          resizeClasses[resize],
          getStateClass(),
          widthClass,
          'placeholder-gray-400 min-h-[100px]',
          className
        )}
        disabled={disabled}
        {...props}
      />
      
      {/* Helper Text / Error Message */}
      {(error || helperText) && (
        <p className={cn(
          'mt-2 text-sm transition-colors duration-200',
          error ? 'text-red-600' : 'text-gray-500'
        )}>
          {error || helperText}
        </p>
      )}
    </div>
  )
})

EnhancedTextarea.displayName = 'EnhancedTextarea'

// Search Input Component
interface SearchInputProps extends Omit<EnhancedInputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void
  searchDelay?: number
}

export function SearchInput({ 
  onSearch, 
  searchDelay = 300, 
  placeholder = 'Search...', 
  ...props 
}: SearchInputProps) {
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>()
  
  const handleSearch = (value: string) => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    
    const timeout = setTimeout(() => {
      onSearch?.(value)
    }, searchDelay)
    
    setSearchTimeout(timeout)
  }

  return (
    <EnhancedInput
      type="search"
      placeholder={placeholder}
      leftIcon={
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      }
      onChange={(e) => {
        props.onChange?.(e)
        handleSearch(e.target.value)
      }}
      {...props}
    />
  )
}
