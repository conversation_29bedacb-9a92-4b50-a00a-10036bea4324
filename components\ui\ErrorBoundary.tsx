'use client'

import { Component, ReactNode, ErrorInfo } from 'react'
import { Error<PERSON>ogger, AppError, ErrorType, ErrorSeverity } from '@/lib/error-handling'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo })

    // Log error
    const appError = new AppError(
      `React Error Boundary: ${error.message}`,
      ErrorType.SYSTEM,
      ErrorSeverity.HIGH,
      {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        originalError: error
      }
    )

    ErrorLogger.logError(appError)

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          showDetails={this.props.showDetails}
          onRetry={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
        />
      )
    }

    return this.props.children
  }
}

// Default Error Fallback Component
interface ErrorFallbackProps {
  error?: Error
  errorInfo?: ErrorInfo
  showDetails?: boolean
  onRetry?: () => void
}

function ErrorFallback({ error, errorInfo, showDetails = false, onRetry }: ErrorFallbackProps) {
  const handleReload = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  const handleRetry = () => {
    if (onRetry) {
      onRetry()
    } else {
      handleReload()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        {/* Error Icon */}
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900 mb-6">
          <svg
            className="h-8 w-8 text-red-600 dark:text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        {/* Error Message */}
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Something went wrong
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          We're sorry, but something unexpected happened. Please try again.
        </p>

        {/* Error Details (Development) */}
        {showDetails && error && (
          <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-left">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Error Details:
            </h3>
            <pre className="text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap break-words">
              {error.message}
            </pre>
            {errorInfo && (
              <details className="mt-2">
                <summary className="text-xs text-gray-600 dark:text-gray-400 cursor-pointer">
                  Component Stack
                </summary>
                <pre className="text-xs text-gray-500 dark:text-gray-500 mt-1 whitespace-pre-wrap break-words">
                  {errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleRetry}
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Try Again
          </button>
          <button
            onClick={handleReload}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
          >
            Reload Page
          </button>
        </div>

        {/* Support Link */}
        <p className="mt-6 text-sm text-gray-500 dark:text-gray-400">
          If this problem persists, please{' '}
          <a
            href="https://t.me/AureusAllianceBot"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            contact support
          </a>
        </p>
      </div>
    </div>
  )
}

// Hook for error boundary
export function useErrorBoundary() {
  const throwError = (error: Error) => {
    throw error
  }

  return { throwError }
}

// Higher-order component for error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  return WrappedComponent
}

// Async Error Boundary for handling async errors
export function AsyncErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        // Handle async errors specifically
        const appError = new AppError(
          `Async Error: ${error.message}`,
          ErrorType.SYSTEM,
          ErrorSeverity.HIGH,
          {
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            isAsync: true
          }
        )
        
        ErrorLogger.logError(appError)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}
