'use client'

import { HTMLAttributes } from 'react'
import { cn } from '@/lib/utils'

interface SkeletonProps extends HTMLAttributes<HTMLDivElement> {
  width?: string | number
  height?: string | number
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  variant?: 'default' | 'text' | 'circular' | 'rectangular'
  animation?: 'pulse' | 'wave' | 'none'
}

export default function Skeleton({
  width,
  height,
  rounded = 'md',
  variant = 'default',
  animation = 'pulse',
  className,
  style,
  ...props
}: SkeletonProps) {
  const baseClasses = 'bg-gray-200 dark:bg-gray-700'
  
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full'
  }
  
  const variantClasses = {
    default: 'h-4',
    text: 'h-4',
    circular: 'rounded-full',
    rectangular: 'rounded-md'
  }
  
  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-pulse', // Could be enhanced with custom wave animation
    none: ''
  }
  
  const styles = {
    width: width ? (typeof width === 'number' ? `${width}px` : width) : undefined,
    height: height ? (typeof height === 'number' ? `${height}px` : height) : undefined,
    ...style
  }

  return (
    <div
      className={cn(
        baseClasses,
        roundedClasses[rounded],
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={styles}
      {...props}
    />
  )
}

// Text Skeleton Component
interface TextSkeletonProps {
  lines?: number
  width?: string[]
  className?: string
}

export function TextSkeleton({ 
  lines = 3, 
  width = ['100%', '80%', '60%'],
  className 
}: TextSkeletonProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          width={width[index] || width[width.length - 1]}
        />
      ))}
    </div>
  )
}

// Avatar Skeleton Component
interface AvatarSkeletonProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export function AvatarSkeleton({ size = 'md', className }: AvatarSkeletonProps) {
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  return (
    <Skeleton
      variant="circular"
      className={cn(sizeClasses[size], className)}
    />
  )
}

// Card Skeleton Component
interface CardSkeletonProps {
  showAvatar?: boolean
  showImage?: boolean
  lines?: number
  className?: string
}

export function CardSkeleton({ 
  showAvatar = false, 
  showImage = false, 
  lines = 3,
  className 
}: CardSkeletonProps) {
  return (
    <div className={cn('p-6 bg-white rounded-lg border border-gray-200', className)}>
      {/* Header with avatar */}
      {showAvatar && (
        <div className="flex items-center space-x-3 mb-4">
          <AvatarSkeleton size="md" />
          <div className="flex-1">
            <Skeleton width="40%" height={16} className="mb-1" />
            <Skeleton width="60%" height={14} />
          </div>
        </div>
      )}
      
      {/* Image */}
      {showImage && (
        <Skeleton height={200} className="mb-4" rounded="lg" />
      )}
      
      {/* Content */}
      <div className="space-y-3">
        <Skeleton width="80%" height={20} />
        <TextSkeleton lines={lines} />
      </div>
      
      {/* Footer */}
      <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
        <Skeleton width="30%" height={16} />
        <Skeleton width="20%" height={16} />
      </div>
    </div>
  )
}

// Table Skeleton Component
interface TableSkeletonProps {
  rows?: number
  columns?: number
  showHeader?: boolean
  className?: string
}

export function TableSkeleton({ 
  rows = 5, 
  columns = 4, 
  showHeader = true,
  className 
}: TableSkeletonProps) {
  return (
    <div className={cn('w-full', className)}>
      {/* Table Header */}
      {showHeader && (
        <div className="grid gap-4 p-4 border-b border-gray-200" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <Skeleton key={index} height={16} width="60%" />
          ))}
        </div>
      )}
      
      {/* Table Rows */}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div 
            key={rowIndex} 
            className="grid gap-4 p-4" 
            style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
          >
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} height={16} width={`${60 + Math.random() * 30}%`} />
            ))}
          </div>
        ))}
      </div>
    </div>
  )
}

// Dashboard Skeleton Component
export function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Skeleton width={200} height={32} className="mb-2" />
          <Skeleton width={300} height={16} />
        </div>
        <Skeleton width={120} height={40} rounded="lg" />
      </div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="p-6 bg-white rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <Skeleton width="60%" height={16} />
              <Skeleton width={24} height={24} rounded="full" />
            </div>
            <Skeleton width="40%" height={32} className="mb-2" />
            <Skeleton width="80%" height={14} />
          </div>
        ))}
      </div>
      
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          <CardSkeleton showImage lines={4} />
          <CardSkeleton lines={3} />
        </div>
        
        {/* Right Column */}
        <div className="space-y-6">
          <CardSkeleton showAvatar lines={2} />
          <CardSkeleton lines={3} />
        </div>
      </div>
    </div>
  )
}

// List Skeleton Component
interface ListSkeletonProps {
  items?: number
  showAvatar?: boolean
  showImage?: boolean
  className?: string
}

export function ListSkeleton({ 
  items = 5, 
  showAvatar = false, 
  showImage = false,
  className 
}: ListSkeletonProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200">
          {showAvatar && <AvatarSkeleton size="md" />}
          {showImage && <Skeleton width={80} height={80} rounded="lg" />}
          <div className="flex-1 space-y-2">
            <Skeleton width="60%" height={16} />
            <Skeleton width="80%" height={14} />
            <Skeleton width="40%" height={12} />
          </div>
          <Skeleton width={80} height={32} rounded="lg" />
        </div>
      ))}
    </div>
  )
}

// Form Skeleton Component
export function FormSkeleton() {
  return (
    <div className="space-y-6">
      {/* Form Title */}
      <div>
        <Skeleton width="40%" height={24} className="mb-2" />
        <Skeleton width="60%" height={16} />
      </div>
      
      {/* Form Fields */}
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index}>
            <Skeleton width="30%" height={16} className="mb-2" />
            <Skeleton width="100%" height={40} rounded="lg" />
          </div>
        ))}
      </div>
      
      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <Skeleton width={80} height={40} rounded="lg" />
        <Skeleton width={100} height={40} rounded="lg" />
      </div>
    </div>
  )
}
