// Enhanced UI Component Library for Aureus Alliance Holdings
// Professional gold mining investment platform components

// Core Components
export { Button, IconButton, ButtonGroup } from './Button'
export { EnhancedButton, FloatingActionButton } from './EnhancedButton'
export { Card } from './Card'
export { 
  EnhancedCard, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardFooter,
  StatCard,
  FeatureCard 
} from './EnhancedCard'
export { Input } from './Input'
export { 
  EnhancedInput, 
  EnhancedTextarea, 
  SearchInput 
} from './EnhancedInput'
export { Modal } from './Modal'
export { Progress } from './Progress'
export { Toast, ToastProvider, useToast } from './Toast'
export { ErrorBoundary } from './ErrorBoundary'
export { Breadcrumb } from './Breadcrumb'

// Layout Components
export { default as Layout } from './Layout'
export { default as Header } from './Header'
export { default as Footer } from './Footer'
export { default as Sidebar } from './Sidebar'

// Form Components
export { default as FormField } from './FormField'
export { default as Select } from './Select'
export { default as Checkbox } from './Checkbox'
export { default as Radio } from './Radio'
export { default as Switch } from './Switch'
export { default as FileUpload } from './FileUpload'

// Data Display Components
export { default as Table } from './Table'
export { default as Badge } from './Badge'
export { default as Avatar } from './Avatar'
export { default as Tooltip } from './Tooltip'
export { default as Popover } from './Popover'
export { default as Tabs } from './Tabs'
export { default as Accordion } from './Accordion'

// Feedback Components
export { default as Alert } from './Alert'
export { default as Skeleton } from './Skeleton'
export { default as Spinner } from './Spinner'
export { default as EmptyState } from './EmptyState'

// Navigation Components
export { default as Pagination } from './Pagination'
export { default as Steps } from './Steps'
export { default as Menu } from './Menu'
export { default as Dropdown } from './Dropdown'

// Overlay Components
export { default as Dialog } from './Dialog'
export { default as Sheet } from './Sheet'
export { default as Drawer } from './Drawer'

// Specialized Components
export { default as Chart } from './Chart'
export { default as Calendar } from './Calendar'
export { default as DatePicker } from './DatePicker'
export { default as ColorPicker } from './ColorPicker'

// Design System
export { 
  designTokens, 
  componentVariants, 
  animations, 
  layouts, 
  typography,
  getColorValue,
  getSpacingValue,
  getFontSize,
  responsive,
  a11y
} from '../lib/design-system'

// Component Types
export type { ButtonProps } from './Button'
export type { CardProps } from './Card'
export type { InputProps } from './Input'
export type { ModalProps } from './Modal'
export type { ProgressProps } from './Progress'
export type { ToastProps } from './Toast'

// Utility Types
export interface ComponentBaseProps {
  className?: string
  children?: React.ReactNode
}

export interface ComponentVariantProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

export interface ComponentStateProps {
  loading?: boolean
  disabled?: boolean
  error?: boolean
  success?: boolean
}

// Theme Configuration
export const themeConfig = {
  // Brand colors
  brand: {
    primary: '#fbbf24', // Gold
    secondary: '#06b6d4', // Cyber blue
    accent: '#8b5cf6' // Purple
  },
  
  // Component defaults
  defaults: {
    button: {
      variant: 'primary' as const,
      size: 'md' as const,
      rounded: 'lg' as const
    },
    card: {
      variant: 'default' as const,
      padding: 'md' as const,
      rounded: 'lg' as const
    },
    input: {
      variant: 'default' as const,
      size: 'md' as const
    }
  },
  
  // Animation settings
  animation: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500
    },
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
}

// Accessibility helpers
export const accessibility = {
  // Screen reader only text
  srOnly: 'sr-only',
  
  // Focus management
  focusRing: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
  focusVisible: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500',
  
  // Skip links
  skipLink: 'absolute left-[-10000px] top-auto w-1 h-1 overflow-hidden focus:left-6 focus:top-7 focus:w-auto focus:h-auto focus:overflow-visible',
  
  // ARIA helpers
  aria: {
    expanded: (expanded: boolean) => ({ 'aria-expanded': expanded }),
    selected: (selected: boolean) => ({ 'aria-selected': selected }),
    checked: (checked: boolean) => ({ 'aria-checked': checked }),
    disabled: (disabled: boolean) => ({ 'aria-disabled': disabled }),
    hidden: (hidden: boolean) => ({ 'aria-hidden': hidden }),
    label: (label: string) => ({ 'aria-label': label }),
    labelledby: (id: string) => ({ 'aria-labelledby': id }),
    describedby: (id: string) => ({ 'aria-describedby': id })
  }
}

// Responsive utilities
export const responsive = {
  // Breakpoint helpers
  mobile: (classes: string) => `sm:${classes}`,
  tablet: (classes: string) => `md:${classes}`,
  desktop: (classes: string) => `lg:${classes}`,
  wide: (classes: string) => `xl:${classes}`,
  
  // Common responsive patterns
  stack: 'flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4',
  grid: 'grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3',
  center: 'flex flex-col items-center justify-center text-center',
  
  // Typography scaling
  text: {
    xs: 'text-xs sm:text-sm',
    sm: 'text-sm sm:text-base',
    base: 'text-base sm:text-lg',
    lg: 'text-lg sm:text-xl',
    xl: 'text-xl sm:text-2xl',
    '2xl': 'text-2xl sm:text-3xl',
    '3xl': 'text-3xl sm:text-4xl'
  }
}

// Color utilities
export const colors = {
  // Status colors
  status: {
    success: 'text-green-600 bg-green-50 border-green-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    error: 'text-red-600 bg-red-50 border-red-200',
    info: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  
  // Interactive states
  interactive: {
    hover: 'hover:bg-gray-50 hover:text-gray-900',
    active: 'active:bg-gray-100',
    focus: 'focus:bg-gray-50 focus:text-gray-900',
    disabled: 'disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed'
  },
  
  // Brand variations
  brand: {
    gold: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      900: '#78350f'
    },
    blue: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
      900: '#1e3a8a'
    }
  }
}

// Layout patterns
export const patterns = {
  // Common layouts
  dashboard: 'min-h-screen bg-gray-50',
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-12 sm:py-16 lg:py-20',
  
  // Card layouts
  cardGrid: 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3',
  cardList: 'space-y-6',
  
  // Form layouts
  form: 'space-y-6',
  formGroup: 'space-y-4',
  formRow: 'grid grid-cols-1 gap-4 sm:grid-cols-2',
  
  // Navigation
  nav: 'flex items-center space-x-4',
  navVertical: 'flex flex-col space-y-2',
  
  // Content
  prose: 'prose prose-gray max-w-none',
  sidebar: 'w-64 flex-shrink-0',
  main: 'flex-1 min-w-0'
}

// Export everything as default for convenience
export default {
  designTokens,
  componentVariants,
  animations,
  layouts,
  typography,
  themeConfig,
  accessibility,
  responsive,
  colors,
  patterns
}
