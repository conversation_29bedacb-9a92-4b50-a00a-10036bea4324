import { CalculatorInputs } from './types';

export const PLANT_CAPACITY_TPH = 200;
export const EFFECTIVE_HOURS_PER_DAY = 20;
export const OPERATING_DAYS_PER_YEAR = 330;
export const BULK_DENSITY_T_PER_M3 = 1.8;
export const HA_PER_PLANT = 25;
export const TOTAL_SHARES = 1400000;
export const PROJECT_TOTAL_HA = 250;

export const DEFAULT_VALUES: CalculatorInputs = {
  landHa: 25,
  avgGravelThickness: 0.8,
  inSituGrade: 0.9,
  recoveryFactor: 70, // as percentage
  goldPriceUsdPerKg: 100000,
  opexPercent: 45, // as percentage
  userShares: 1000,
  dividendPayoutPercent: 50, // as percentage
};

export const LAND_SIZE_OPTIONS = Array.from({ length: 40 }, (_, i) => (i + 1) * 25);