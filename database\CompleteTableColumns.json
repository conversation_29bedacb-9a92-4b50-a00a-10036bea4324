[{"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "instance_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "payload", "data_type": "json"}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "audit_log_entries", "column_name": "ip_address", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "user_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "auth_code", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "code_challenge_method", "data_type": "USER-DEFINED"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "code_challenge", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_type", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_access_token", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "provider_refresh_token", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "authentication_method", "data_type": "text"}, {"table_schema": "auth", "table_name": "flow_state", "column_name": "auth_code_issued_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "identities", "column_name": "provider_id", "data_type": "text"}, {"table_schema": "auth", "table_name": "identities", "column_name": "user_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "identities", "column_name": "identity_data", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "identities", "column_name": "provider", "data_type": "text"}, {"table_schema": "auth", "table_name": "identities", "column_name": "last_sign_in_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "identities", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "identities", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "identities", "column_name": "email", "data_type": "text"}, {"table_schema": "auth", "table_name": "identities", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "instances", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "instances", "column_name": "uuid", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "instances", "column_name": "raw_base_config", "data_type": "text"}, {"table_schema": "auth", "table_name": "instances", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "instances", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "session_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "authentication_method", "data_type": "text"}, {"table_schema": "auth", "table_name": "mfa_amr_claims", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "factor_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "verified_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "ip_address", "data_type": "inet"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "otp_code", "data_type": "text"}, {"table_schema": "auth", "table_name": "mfa_challenges", "column_name": "web_authn_session_data", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "user_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "friendly_name", "data_type": "text"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "factor_type", "data_type": "USER-DEFINED"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "status", "data_type": "USER-DEFINED"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "secret", "data_type": "text"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "phone", "data_type": "text"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "last_challenged_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "web_authn_credential", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "mfa_factors", "column_name": "web_authn_aaguid", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "user_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "token_type", "data_type": "USER-DEFINED"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "token_hash", "data_type": "text"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "relates_to", "data_type": "text"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "created_at", "data_type": "timestamp without time zone"}, {"table_schema": "auth", "table_name": "one_time_tokens", "column_name": "updated_at", "data_type": "timestamp without time zone"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "instance_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "id", "data_type": "bigint"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "token", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "user_id", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "revoked", "data_type": "boolean"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "parent", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "refresh_tokens", "column_name": "session_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "sso_provider_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "entity_id", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "metadata_xml", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "metadata_url", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "attribute_mapping", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "saml_providers", "column_name": "name_id_format", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "sso_provider_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "request_id", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "for_email", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "redirect_to", "data_type": "text"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "saml_relay_states", "column_name": "flow_state_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "schema_migrations", "column_name": "version", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "user_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "factor_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "aal", "data_type": "USER-DEFINED"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "not_after", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "refreshed_at", "data_type": "timestamp without time zone"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "user_agent", "data_type": "text"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "ip", "data_type": "inet"}, {"table_schema": "auth", "table_name": "sessions", "column_name": "tag", "data_type": "text"}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "sso_provider_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "domain", "data_type": "text"}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sso_domains", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "resource_id", "data_type": "text"}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "sso_providers", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "instance_id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "users", "column_name": "id", "data_type": "uuid"}, {"table_schema": "auth", "table_name": "users", "column_name": "aud", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "role", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "encrypted_password", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_confirmed_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "invited_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmation_token", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmation_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "recovery_token", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "recovery_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_token_new", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "last_sign_in_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "raw_app_meta_data", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "users", "column_name": "raw_user_meta_data", "data_type": "jsonb"}, {"table_schema": "auth", "table_name": "users", "column_name": "is_super_admin", "data_type": "boolean"}, {"table_schema": "auth", "table_name": "users", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone", "data_type": "text"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_confirmed_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change", "data_type": "text"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change_token", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "phone_change_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "confirmed_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_token_current", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "email_change_confirm_status", "data_type": "smallint"}, {"table_schema": "auth", "table_name": "users", "column_name": "banned_until", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "reauthentication_token", "data_type": "character varying"}, {"table_schema": "auth", "table_name": "users", "column_name": "reauthentication_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "is_sso_user", "data_type": "boolean"}, {"table_schema": "auth", "table_name": "users", "column_name": "deleted_at", "data_type": "timestamp with time zone"}, {"table_schema": "auth", "table_name": "users", "column_name": "is_anonymous", "data_type": "boolean"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "userid", "data_type": "oid"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "dbid", "data_type": "oid"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "toplevel", "data_type": "boolean"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "queryid", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "query", "data_type": "text"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "plans", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "total_plan_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "min_plan_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "max_plan_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "mean_plan_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stddev_plan_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "calls", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "total_exec_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "min_exec_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "max_exec_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "mean_exec_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stddev_exec_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "rows", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_hit", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_read", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_dirtied", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blks_written", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_hit", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_read", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_dirtied", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blks_written", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blks_read", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blks_written", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blk_read_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "shared_blk_write_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blk_read_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "local_blk_write_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blk_read_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "temp_blk_write_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_records", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_fpi", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "wal_bytes", "data_type": "numeric"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_functions", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_generation_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_inlining_count", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_inlining_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_optimization_count", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_optimization_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_emission_count", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_emission_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_deform_count", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "jit_deform_time", "data_type": "double precision"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "stats_since", "data_type": "timestamp with time zone"}, {"table_schema": "extensions", "table_name": "pg_stat_statements", "column_name": "minmax_stats_since", "data_type": "timestamp with time zone"}, {"table_schema": "extensions", "table_name": "pg_stat_statements_info", "column_name": "dealloc", "data_type": "bigint"}, {"table_schema": "extensions", "table_name": "pg_stat_statements_info", "column_name": "stats_reset", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "admin_telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "admin_username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "action", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "target_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "target_id", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "details", "data_type": "jsonb"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "ip_address", "data_type": "inet"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "user_agent", "data_type": "text"}, {"table_schema": "public", "table_name": "admin_audit_logs", "column_name": "timestamp", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "admin_user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "audio_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "notification_volume", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "new_payment_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "new_withdrawal_request_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "new_commission_conversion_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "system_error_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "user_registration_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "high_value_transaction_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "critical_alerts_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "high_priority_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "medium_priority_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "low_priority_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "high_value_threshold", "data_type": "numeric"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "quiet_hours_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "quiet_hours_start", "data_type": "time without time zone"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "quiet_hours_end", "data_type": "time without time zone"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "timezone", "data_type": "character varying"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "escalation_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "escalation_delay_minutes", "data_type": "integer"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "admin_notification_preferences", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "admin_users", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "admin_users", "column_name": "email", "data_type": "text"}, {"table_schema": "public", "table_name": "admin_users", "column_name": "role", "data_type": "text"}, {"table_schema": "public", "table_name": "admin_users", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "admin_users", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "package_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "package_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "shares_purchased", "data_type": "integer"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "total_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "commission_used", "data_type": "numeric"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "remaining_payment", "data_type": "numeric"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "payment_method", "data_type": "character varying"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "aureus_share_purchases", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "usd_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "zar_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "proof_file_id", "data_type": "character varying"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "bank_account", "data_type": "character varying"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "approved_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "bank_transfer_payments", "column_name": "approved_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "kyc_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "full_legal_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "email_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "kyc_completed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "certificate_requested", "data_type": "boolean"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "certificate_generated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "certificate_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "telegram_username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "total_shares", "data_type": "bigint"}, {"table_schema": "public", "table_name": "certificate_generation_queue", "column_name": "latest_purchase_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "usdt_balance", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "share_balance", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "total_earned_usdt", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "total_earned_shares", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "total_withdrawn", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "last_updated", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_balances", "column_name": "escrowed_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "shares_requested", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "usdt_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "share_price", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "phase_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "phase_number", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "approved_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "approved_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "rejected_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "rejected_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "rejection_reason", "data_type": "text"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_conversions", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "referrer_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "referred_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "share_purchase_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "commission_rate", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "share_purchase_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "usdt_commission", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "share_commission", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "payment_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_transactions", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "share_purchase_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "commission_amount_used", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "remaining_payment_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_usage", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "withdrawal_amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "wallet_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "network", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "currency", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "admin_notes", "data_type": "text"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "transaction_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "processed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawal_requests", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "withdrawal_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "wallet_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "admin_notes", "data_type": "text"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "processed_by", "data_type": "integer"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "processed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "rejected_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "rejected_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "approved_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "approved_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "transaction_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "commission_withdrawals", "column_name": "completed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "network", "data_type": "character varying"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "currency", "data_type": "character varying"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "wallet_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "company_wallets", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "old_country_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "old_country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "new_country_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "new_country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "changed_by_telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "changed_by_username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "change_reason", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_change_log", "column_name": "changed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "country_of_residence", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "flag_emoji", "data_type": "character varying"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "user_count", "data_type": "bigint"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "new_users_30d", "data_type": "bigint"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "first_selection_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "country_statistics", "column_name": "latest_selection_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "investment_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "amount", "data_type": "numeric"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "currency", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "network", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "sender_wallet", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "receiver_wallet", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "transaction_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "screenshot_url", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "admin_notes", "data_type": "text"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "approved_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "approved_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "rejected_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "rejected_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "verification_status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "rejection_reason", "data_type": "text"}, {"table_schema": "public", "table_name": "crypto_payment_transactions", "column_name": "transaction_notes", "data_type": "text"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "document_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "document_url", "data_type": "text"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "accessed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "telegram_user_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "has_nda_acceptance", "data_type": "boolean"}, {"table_schema": "public", "table_name": "document_access_logs", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "name", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "description", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "slug", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "display_order", "data_type": "integer"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "gallery_categories", "column_name": "updated_by", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "title", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "description", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "image_url", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "thumbnail_url", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "category_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "alt_text", "data_type": "text"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "file_size", "data_type": "integer"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "width", "data_type": "integer"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "height", "data_type": "integer"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "display_order", "data_type": "integer"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "is_featured", "data_type": "boolean"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "gallery_images", "column_name": "updated_by", "data_type": "text"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "phase_number", "data_type": "integer"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "phase_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "price_per_share", "data_type": "numeric"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "total_shares_available", "data_type": "numeric"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "shares_sold", "data_type": "numeric"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "start_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "end_date", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "investment_phases", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "kyc_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "action", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "field_changed", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "old_value_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "new_value_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "performed_by_telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "performed_by_username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "ip_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "user_agent", "data_type": "text"}, {"table_schema": "public", "table_name": "kyc_audit_log", "column_name": "performed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "first_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "last_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "full_legal_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "id_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "id_number_encrypted", "data_type": "text"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "id_number_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "phone_number", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "email_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "street_address", "data_type": "text"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "city", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "postal_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "country_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "data_consent_given", "data_type": "boolean"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "privacy_policy_accepted", "data_type": "boolean"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "kyc_completed_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "kyc_status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "created_by_telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "last_modified_by", "data_type": "character varying"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "certificate_requested", "data_type": "boolean"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "certificate_generated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "kyc_information", "column_name": "certificate_sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "accepted_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "ip_address", "data_type": "character varying"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "user_agent", "data_type": "text"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "telegram_user_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "full_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "nda_acceptances", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "notification_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "audio_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "message_preview", "data_type": "text"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "audio_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "delivery_status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "error_message", "data_type": "text"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "sent_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "delivered_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "user_read", "data_type": "boolean"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "user_interacted", "data_type": "boolean"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "is_admin_notification", "data_type": "boolean"}, {"table_schema": "public", "table_name": "notification_log", "column_name": "priority_level", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "sound_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "sound_emoji", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "sound_description", "data_type": "text"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "category", "data_type": "character varying"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "notification_sound_types", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "payment_id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "admin_telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "admin_username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "note_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "note_text", "data_type": "text"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "is_internal", "data_type": "boolean"}, {"table_schema": "public", "table_name": "payment_admin_notes", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "referrals", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "referrals", "column_name": "referrer_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "referrals", "column_name": "referred_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "referrals", "column_name": "referral_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "referrals", "column_name": "commission_rate", "data_type": "numeric"}, {"table_schema": "public", "table_name": "referrals", "column_name": "total_commission", "data_type": "numeric"}, {"table_schema": "public", "table_name": "referrals", "column_name": "status", "data_type": "character varying"}, {"table_schema": "public", "table_name": "referrals", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "referrals", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "site_content", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "site_content", "column_name": "section", "data_type": "text"}, {"table_schema": "public", "table_name": "site_content", "column_name": "key", "data_type": "text"}, {"table_schema": "public", "table_name": "site_content", "column_name": "value", "data_type": "jsonb"}, {"table_schema": "public", "table_name": "site_content", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "site_content", "column_name": "updated_by", "data_type": "text"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "country_code", "data_type": "character varying"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "flag_emoji", "data_type": "character varying"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "is_primary", "data_type": "boolean"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "display_order", "data_type": "integer"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "supported_countries", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "setting_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "setting_value", "data_type": "text"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "description", "data_type": "text"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "updated_by_admin_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "system_settings", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "first_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "last_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "is_registered", "data_type": "boolean"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "registration_step", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "registration_mode", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "temp_email", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "temp_password", "data_type": "character varying"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "telegram_users", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "user_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "terms_type", "data_type": "character varying"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "version", "data_type": "character varying"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "accepted_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "terms_acceptance", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "test_connection", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "test_connection", "column_name": "name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "test_connection", "column_name": "description", "data_type": "text"}, {"table_schema": "public", "table_name": "test_connection", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "test_connection", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "user_id", "data_type": "integer"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "audio_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "notification_volume", "data_type": "character varying"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "payment_approval_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "payment_rejection_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "withdrawal_approval_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "withdrawal_rejection_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "commission_update_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "referral_bonus_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "system_announcement_audio", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "quiet_hours_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "quiet_hours_start", "data_type": "time without time zone"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "quiet_hours_end", "data_type": "time without time zone"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "timezone", "data_type": "character varying"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "custom_sound_enabled", "data_type": "boolean"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "notification_frequency", "data_type": "character varying"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "user_notification_preferences", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "id", "data_type": "uuid"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "session_state", "data_type": "character varying"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "session_data", "data_type": "jsonb"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "expires_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "user_sessions", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "id", "data_type": "integer"}, {"table_schema": "public", "table_name": "users", "column_name": "username", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "email", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "password_hash", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "full_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "phone", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "address", "data_type": "text"}, {"table_schema": "public", "table_name": "users", "column_name": "is_active", "data_type": "boolean"}, {"table_schema": "public", "table_name": "users", "column_name": "is_verified", "data_type": "boolean"}, {"table_schema": "public", "table_name": "users", "column_name": "verification_token", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "reset_token", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "reset_token_expires", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "telegram_id", "data_type": "bigint"}, {"table_schema": "public", "table_name": "users", "column_name": "country_of_residence", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "country_name", "data_type": "character varying"}, {"table_schema": "public", "table_name": "users", "column_name": "country_selected_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "country_updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "public", "table_name": "users", "column_name": "country_selection_completed", "data_type": "boolean"}, {"table_schema": "public", "table_name": "users", "column_name": "role", "data_type": "text"}, {"table_schema": "public", "table_name": "users", "column_name": "sponsor_user_id", "data_type": "integer"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "topic", "data_type": "text"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "extension", "data_type": "text"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "payload", "data_type": "jsonb"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "event", "data_type": "text"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "private", "data_type": "boolean"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "updated_at", "data_type": "timestamp without time zone"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "inserted_at", "data_type": "timestamp without time zone"}, {"table_schema": "realtime", "table_name": "messages", "column_name": "id", "data_type": "uuid"}, {"table_schema": "realtime", "table_name": "schema_migrations", "column_name": "version", "data_type": "bigint"}, {"table_schema": "realtime", "table_name": "schema_migrations", "column_name": "inserted_at", "data_type": "timestamp without time zone"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "id", "data_type": "bigint"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "subscription_id", "data_type": "uuid"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "entity", "data_type": "regclass"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "filters", "data_type": "ARRAY"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "claims", "data_type": "jsonb"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "claims_role", "data_type": "regrole"}, {"table_schema": "realtime", "table_name": "subscription", "column_name": "created_at", "data_type": "timestamp without time zone"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "id", "data_type": "text"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "name", "data_type": "text"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "owner", "data_type": "uuid"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "public", "data_type": "boolean"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "avif_autodetection", "data_type": "boolean"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "file_size_limit", "data_type": "bigint"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "allowed_mime_types", "data_type": "ARRAY"}, {"table_schema": "storage", "table_name": "buckets", "column_name": "owner_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "migrations", "column_name": "id", "data_type": "integer"}, {"table_schema": "storage", "table_name": "migrations", "column_name": "name", "data_type": "character varying"}, {"table_schema": "storage", "table_name": "migrations", "column_name": "hash", "data_type": "character varying"}, {"table_schema": "storage", "table_name": "migrations", "column_name": "executed_at", "data_type": "timestamp without time zone"}, {"table_schema": "storage", "table_name": "objects", "column_name": "id", "data_type": "uuid"}, {"table_schema": "storage", "table_name": "objects", "column_name": "bucket_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "objects", "column_name": "name", "data_type": "text"}, {"table_schema": "storage", "table_name": "objects", "column_name": "owner", "data_type": "uuid"}, {"table_schema": "storage", "table_name": "objects", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "objects", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "objects", "column_name": "last_accessed_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "objects", "column_name": "metadata", "data_type": "jsonb"}, {"table_schema": "storage", "table_name": "objects", "column_name": "path_tokens", "data_type": "ARRAY"}, {"table_schema": "storage", "table_name": "objects", "column_name": "version", "data_type": "text"}, {"table_schema": "storage", "table_name": "objects", "column_name": "owner_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "objects", "column_name": "user_metadata", "data_type": "jsonb"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "in_progress_size", "data_type": "bigint"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "upload_signature", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "bucket_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "key", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "version", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "owner_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads", "column_name": "user_metadata", "data_type": "jsonb"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "id", "data_type": "uuid"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "upload_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "size", "data_type": "bigint"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "part_number", "data_type": "integer"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "bucket_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "key", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "etag", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "owner_id", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "version", "data_type": "text"}, {"table_schema": "storage", "table_name": "s3_multipart_uploads_parts", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "id", "data_type": "uuid"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "name", "data_type": "text"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "description", "data_type": "text"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "secret", "data_type": "text"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "decrypted_secret", "data_type": "text"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "key_id", "data_type": "uuid"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "nonce", "data_type": "bytea"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "vault", "table_name": "decrypted_secrets", "column_name": "updated_at", "data_type": "timestamp with time zone"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "id", "data_type": "uuid"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "name", "data_type": "text"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "description", "data_type": "text"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "secret", "data_type": "text"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "key_id", "data_type": "uuid"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "nonce", "data_type": "bytea"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "created_at", "data_type": "timestamp with time zone"}, {"table_schema": "vault", "table_name": "secrets", "column_name": "updated_at", "data_type": "timestamp with time zone"}]