# 1.1 Requirements Gathering - Aureus Alliance Web Dashboard

## Executive Summary
This document outlines the comprehensive requirements for developing a web dashboard that mirrors the existing Telegram bot functionality while maintaining perfect synchronization with the current Supabase database. The web platform will provide users with an enhanced interface for share purchases, KYC management, referral tracking, and commission management.

## Project Context

### Current System Overview
- **Existing Platform**: Telegram bot (`aureus-bot-new.js`) with 13,551 lines of production code
- **Database**: Supabase PostgreSQL with established schema (4,142 table columns documented)
- **Current Tech Stack**: React + Vite frontend, Telegram bot backend
- **Authentication**: Telegram-based user system
- **Deployment**: Bot running on Railway, frontend needs migration to Next.js + Vercel

### Critical Constraints
1. **ZERO modifications** to existing bot code (`aureus-bot-new.js`)
2. **NO database schema changes** - use existing Supabase structure
3. **Perfect data synchronization** between bot and web platform
4. **Exact business logic replication** from bot implementation
5. **Homepage preservation** - maintain existing landing page

## Stakeholder Analysis

### Primary Stakeholders
1. **Existing Bot Users (2,000+ active)**
   - Need: Seamless transition from bot to web interface
   - Pain Point: Limited document management capabilities in Telegram
   - Goal: Enhanced user experience without losing existing functionality

2. **System Administrator (TTTFOUNDER)**
   - Need: Unified admin panel for both bot and web users
   - Pain Point: Managing approvals across multiple platforms
   - Goal: Streamlined administrative workflows

3. **Development Team**
   - Need: Clear technical specifications and constraints
   - Pain Point: Working with established legacy systems
   - Goal: Successful delivery without breaking existing functionality

4. **New Web Users**
   - Need: Intuitive onboarding and investment process
   - Pain Point: Unfamiliarity with crypto payments and KYC requirements
   - Goal: Complete investment journey through web interface

## Functional Requirements

### 1. Authentication System
**Priority: CRITICAL**
- **Telegram Login Widget Integration**
  - Must use official Telegram OAuth
  - Link with existing `telegram_users` table
  - Preserve user ID consistency with bot
  - Session management with proper security

- **User Registration Flow**
  - Auto-detect existing bot users via Telegram ID
  - Create new records for web-only users
  - Maintain referral link integrity
  - Sync with bot user creation process

### 2. User Onboarding Process
**Priority: HIGH**
- **Country Selection**
  - SA/ZA users → ZAR payment options
  - International users → USDT payment options
  - Store in existing user country fields
  - Match bot's country logic exactly

- **Terms & Conditions**
  - Display current terms from database
  - Record acceptance in `terms_acceptance` table
  - Version tracking for legal compliance
  - Mandatory before share purchases

### 3. Share Purchase System
**Priority: CRITICAL**
- **Share Phase Display**
  - Current phase pricing and availability
  - Real-time share count updates
  - Phase progression indicators
  - Match bot's share calculation: `amount ÷ phase_price = shares`

- **Payment Processing**
  - **USDT Payments**: Crypto wallet integration, transaction verification
  - **ZAR Payments**: Bank transfer details, manual verification
  - Store in `crypto_payment_transactions` table
  - Admin approval workflow integration
  - Payment status tracking and notifications

### 4. KYC Document Management
**Priority: HIGH**
- **Document Upload Interface**
  - File type validation (PDF, JPG, PNG)
  - File size limits (max 10MB per file)
  - Document categorization (ID, Proof of Address, etc.)
  - Preview functionality before submission

- **KYC Status Tracking**
  - Store documents in `user_kyc` table structure
  - Status: pending/approved/rejected/resubmission_required
  - Admin review interface integration
  - Email/bot notifications for status changes

### 5. Referral System
**Priority: HIGH**
- **Referral Link Management**
  - Generate unique referral codes
  - Track referral conversions
  - Commission calculations from `commission_accounts`
  - Real-time earnings display

- **Commission Management**
  - Available commission balance display
  - Withdrawal request system
  - Commission-to-shares conversion
  - Transaction history and audit trail

### 6. Dashboard Interface
**Priority: MEDIUM**
- **Personal Portfolio**
  - Total shares owned across all phases
  - Purchase history with payment details
  - Investment performance tracking
  - Document repository access

- **Navigation & Layout**
  - Responsive mobile-first design
  - Intuitive menu structure
  - Quick access to key functions
  - Consistent with existing brand identity

## Non-Functional Requirements

### Performance Requirements
- **Page Load Time**: < 2 seconds for dashboard pages
- **API Response Time**: < 500ms for data operations
- **Concurrent Users**: Support 500+ simultaneous users
- **Database Sync**: Real-time with 99.9% consistency
- **Mobile Performance**: Optimized for 3G networks

### Security Requirements
- **Authentication Security**
  - Telegram OAuth with token validation
  - Secure session management (JWT tokens)
  - CSRF protection on all forms
  - Rate limiting on API endpoints

- **Data Protection**
  - End-to-end encryption for sensitive data
  - Secure file upload handling
  - PII data anonymization in logs
  - GDPR/POPIA compliance for EU/SA users

- **Financial Security**
  - Payment data encryption
  - Escrow system for commission management
  - Audit trail for all financial transactions
  - Admin approval workflows

### Technical Requirements
- **Framework**: Next.js 13+ with App Router
- **Styling**: Tailwind CSS with existing design system
- **Database**: Existing Supabase instance (no schema changes)
- **Deployment**: Vercel with automatic deployments
- **Browser Support**: Chrome, Firefox, Safari, Edge (latest 2 versions)

### Scalability Requirements
- **User Growth**: Support 10,000+ users within 6 months
- **Data Volume**: Handle increasing transaction volumes
- **Geographic Distribution**: Global user base support
- **API Scaling**: Horizontal scaling capabilities

## User Stories & Use Cases

### Epic 1: User Onboarding
**User Story**: As a new user, I want to register using my Telegram account so that I can access the investment platform.

**Acceptance Criteria**:
- [ ] User clicks "Login with Telegram" button
- [ ] Telegram OAuth completes successfully
- [ ] System checks for existing bot user account
- [ ] User redirected to appropriate onboarding step
- [ ] Session created with proper security

### Epic 2: Share Investment
**User Story**: As an investor, I want to purchase shares using my preferred payment method so that I can grow my investment portfolio.

**Acceptance Criteria**:
- [ ] User selects share quantity within available limits
- [ ] Payment method matches user's country (USDT/ZAR)
- [ ] Payment details display correctly
- [ ] Transaction recorded in database
- [ ] Admin approval workflow triggered
- [ ] User receives confirmation and tracking info

### Epic 3: Document Management
**User Story**: As a compliance-conscious investor, I want to upload my KYC documents so that I can complete the verification process.

**Acceptance Criteria**:
- [ ] User uploads required documents (ID, Proof of Address)
- [ ] Files validated for type and size
- [ ] Documents stored securely in system
- [ ] KYC status updated to "pending review"
- [ ] Admin notified for review
- [ ] User receives status updates

### Epic 4: Referral Earnings
**User Story**: As a referral partner, I want to track my referral earnings and request withdrawals so that I can monetize my network.

**Acceptance Criteria**:
- [ ] User generates unique referral link
- [ ] Referral conversions tracked automatically
- [ ] Commission calculated correctly
- [ ] Earnings dashboard shows real-time data
- [ ] Withdrawal requests processed securely
- [ ] Commission-to-shares conversion available

## Technical Architecture Requirements

### Frontend Architecture
- **Component Structure**: Atomic design methodology
- **State Management**: React Context + useReducer for complex state
- **Data Fetching**: SWR or TanStack Query for server state
- **Form Management**: React Hook Form with Zod validation
- **File Uploads**: Secure upload with progress indicators
- **Real-time Updates**: Supabase subscriptions for live data

### API Integration
- **Supabase Client**: Next.js optimized configuration
- **Authentication**: Telegram OAuth + Supabase auth
- **Database Operations**: Row Level Security (RLS) policies
- **File Storage**: Supabase Storage for KYC documents
- **Real-time**: WebSocket connections for live updates

### Database Integration
- **Existing Tables**: Use current schema without modifications
- **Key Tables**:
  - `telegram_users`: User accounts and profiles
  - `crypto_payment_transactions`: Payment records
  - `commission_accounts`: Referral earnings
  - `user_kyc`: Document management
  - `terms_acceptance`: Legal compliance

## Risk Assessment & Mitigation

### High-Risk Areas
1. **Data Synchronization**
   - Risk: Inconsistencies between bot and web data
   - Mitigation: Comprehensive testing, real-time monitoring
   
2. **Authentication Integration**
   - Risk: Telegram OAuth implementation issues
   - Mitigation: Thorough testing, fallback mechanisms
   
3. **Payment Processing**
   - Risk: Financial transaction errors
   - Mitigation: Extensive validation, admin approval workflow

4. **Performance Impact**
   - Risk: Web dashboard affecting bot performance
   - Mitigation: Database optimization, connection pooling

### Mitigation Strategies
- **Comprehensive Testing**: Unit, integration, and E2E tests
- **Phased Rollout**: Gradual user migration strategy
- **Monitoring**: Real-time performance and error tracking
- **Rollback Plan**: Ability to disable web features if needed

## Success Criteria

### Functional Success
- [ ] 100% feature parity with Telegram bot functionality
- [ ] Zero data inconsistencies between platforms
- [ ] Seamless user experience across bot and web
- [ ] Complete user journey from registration to investment

### Technical Success
- [ ] Page load times under 2 seconds
- [ ] 99.9% uptime and availability
- [ ] Zero security vulnerabilities
- [ ] Mobile-responsive design on all devices

### Business Success
- [ ] 50% increase in user engagement
- [ ] 30% improvement in conversion rates
- [ ] Reduced support queries by 40%
- [ ] Enhanced user satisfaction scores

## Dependencies & Constraints

### External Dependencies
- Telegram OAuth API availability
- Supabase service reliability
- Vercel deployment platform
- Payment processor integrations

### Internal Dependencies
- Existing bot code stability
- Database performance
- Admin approval workflows
- User migration strategy

### Resource Constraints
- Development timeline: 8-12 weeks
- No database schema modifications
- Existing Supabase rate limits
- Team size and expertise

## Next Steps

### Immediate Actions (Week 1)
1. **Stakeholder Review**: Get approval on requirements
2. **Technical Feasibility**: Validate Telegram OAuth integration
3. **Database Analysis**: Deep dive into existing schema
4. **Project Setup**: Initialize Next.js project structure

### Phase 1 Deliverables (Weeks 2-3)
1. **System Analysis**: Complete bot code analysis
2. **Architecture Design**: Technical architecture document
3. **UI/UX Design**: Wireframes and mockups
4. **Development Environment**: Setup and configuration

## Approval & Sign-off

**Requirements Approved By**:
- [ ] Project Stakeholders
- [ ] Technical Team Lead
- [ ] System Administrator
- [ ] Business Owner

**Date**: _______________

**Next Phase**: Proceed to 1.2 Current System Analysis

---

*This document serves as the foundation for the Aureus Alliance Web Dashboard project and should be referenced throughout the development lifecycle to ensure alignment with business objectives and technical constraints.*
