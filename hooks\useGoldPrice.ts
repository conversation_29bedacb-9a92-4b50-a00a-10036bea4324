import { useState, useEffect } from 'react';

interface GoldPriceData {
  price: number;
  lastUpdated: string;
  isLoading: boolean;
  error: string | null;
}

export const useGoldPrice = () => {
  const [goldPrice, setGoldPrice] = useState<GoldPriceData>({
    price: 100000, // Default fallback price
    lastUpdated: '',
    isLoading: false,
    error: null,
  });

  const fetchGoldPrice = async () => {
    setGoldPrice(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // Using a free gold price API (metals-api.com alternative)
      // Note: This is a simplified example - you may need to sign up for an API key
      const response = await fetch('https://api.metals.live/v1/spot/gold');
      
      if (!response.ok) {
        throw new Error('Failed to fetch gold price');
      }
      
      const data = await response.json();
      
      // Convert from USD per troy ounce to USD per kg
      // 1 troy ounce = 31.1035 grams, so 1 kg = 32.15 troy ounces
      const pricePerKg = Math.round(data.price * 32.15);
      
      setGoldPrice({
        price: pricePerKg,
        lastUpdated: new Date().toLocaleString(),
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.warn('Failed to fetch live gold price, using fallback:', error);
      setGoldPrice(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to fetch live price',
      }));
    }
  };

  useEffect(() => {
    // Fetch gold price on component mount
    fetchGoldPrice();
    
    // Set up interval to fetch every 5 minutes
    const interval = setInterval(fetchGoldPrice, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return {
    ...goldPrice,
    refetch: fetchGoldPrice,
  };
};
