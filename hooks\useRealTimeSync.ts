'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { dataSyncService } from '@/lib/data-sync'
import { ErrorLogger, AppError, ErrorType, ErrorSeverity } from '@/lib/error-handling'

// Real-time synchronization hook for React components
export function useRealTimeSync<T>(
  tableName: string,
  initialData: T | null = null,
  filter?: { column: string; value: any }
) {
  const [data, setData] = useState<T | null>(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Handle real-time updates
  const handleUpdate = useCallback((payload: any) => {
    try {
      setLastUpdate(new Date())
      
      switch (payload.eventType) {
        case 'INSERT':
          setData(payload.new as T)
          break
        case 'UPDATE':
          setData(payload.new as T)
          break
        case 'DELETE':
          setData(null)
          break
        default:
          console.warn('Unknown event type:', payload.eventType)
      }
      
      setError(null)
    } catch (err) {
      const error = new AppError(
        `Real-time update failed for table ${tableName}: ${err}`,
        ErrorType.SYSTEM,
        ErrorSeverity.MEDIUM,
        { tableName, payload, originalError: err }
      )
      
      ErrorLogger.logError(error)
      setError('Failed to process real-time update')
    }
  }, [tableName])

  // Subscribe to real-time updates
  useEffect(() => {
    setLoading(true)
    
    try {
      const unsubscribe = dataSyncService.subscribeToTable(
        tableName,
        handleUpdate,
        filter
      )
      
      unsubscribeRef.current = unsubscribe
      setLoading(false)
      
      return () => {
        if (unsubscribeRef.current) {
          unsubscribeRef.current()
          unsubscribeRef.current = null
        }
      }
    } catch (err) {
      const error = new AppError(
        `Failed to subscribe to real-time updates for table ${tableName}: ${err}`,
        ErrorType.SYSTEM,
        ErrorSeverity.HIGH,
        { tableName, filter, originalError: err }
      )
      
      ErrorLogger.logError(error)
      setError('Failed to setup real-time synchronization')
      setLoading(false)
    }
  }, [tableName, handleUpdate, filter])

  // Manual refresh function
  const refresh = useCallback(() => {
    setLastUpdate(new Date())
  }, [])

  return {
    data,
    loading,
    error,
    lastUpdate,
    refresh
  }
}

// Hook for user-specific real-time updates
export function useUserRealTimeSync(userId: number | null) {
  const [updates, setUpdates] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Handle user-specific updates
  const handleUserUpdate = useCallback((payload: any) => {
    try {
      setUpdates(prev => [
        {
          id: Date.now(),
          timestamp: new Date(),
          table: payload.table,
          eventType: payload.eventType,
          data: payload.new || payload.old,
          payload
        },
        ...prev.slice(0, 49) // Keep last 50 updates
      ])
      
      setError(null)
    } catch (err) {
      const error = new AppError(
        `User real-time update failed for user ${userId}: ${err}`,
        ErrorType.SYSTEM,
        ErrorSeverity.MEDIUM,
        { userId, payload, originalError: err }
      )
      
      ErrorLogger.logError(error)
      setError('Failed to process user update')
    }
  }, [userId])

  // Subscribe to user-specific updates
  useEffect(() => {
    if (!userId) {
      setUpdates([])
      return
    }

    setLoading(true)
    
    try {
      const unsubscribe = dataSyncService.subscribeToUserUpdates(
        userId,
        handleUserUpdate
      )
      
      unsubscribeRef.current = unsubscribe
      setLoading(false)
      
      return () => {
        if (unsubscribeRef.current) {
          unsubscribeRef.current()
          unsubscribeRef.current = null
        }
      }
    } catch (err) {
      const error = new AppError(
        `Failed to subscribe to user updates for user ${userId}: ${err}`,
        ErrorType.SYSTEM,
        ErrorSeverity.HIGH,
        { userId, originalError: err }
      )
      
      ErrorLogger.logError(error)
      setError('Failed to setup user synchronization')
      setLoading(false)
    }
  }, [userId, handleUserUpdate])

  // Clear updates
  const clearUpdates = useCallback(() => {
    setUpdates([])
  }, [])

  return {
    updates,
    loading,
    error,
    clearUpdates
  }
}

// Hook for connection status monitoring
export function useConnectionStatus() {
  const [isOnline, setIsOnline] = useState(true)
  const [lastOnline, setLastOnline] = useState<Date | null>(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true)
      setLastOnline(new Date())
      setReconnectAttempts(0)
      
      ErrorLogger.logSystemEvent('connection_restored', {
        timestamp: new Date().toISOString()
      })
    }

    const handleOffline = () => {
      setIsOnline(false)
      setLastOnline(new Date())
      
      ErrorLogger.logSystemEvent('connection_lost', {
        timestamp: new Date().toISOString()
      })
    }

    // Listen for online/offline events
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Initial status
    setIsOnline(navigator.onLine)
    setLastOnline(new Date())

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Manual reconnection attempt
  const attemptReconnect = useCallback(() => {
    setReconnectAttempts(prev => prev + 1)
    
    // Simple connectivity test
    fetch('/api/health', { method: 'HEAD' })
      .then(() => {
        setIsOnline(true)
        setReconnectAttempts(0)
      })
      .catch(() => {
        setIsOnline(false)
      })
  }, [])

  return {
    isOnline,
    lastOnline,
    reconnectAttempts,
    attemptReconnect
  }
}

// Hook for data validation status
export function useDataValidation(userId: number | null) {
  const [validationStatus, setValidationStatus] = useState<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    lastCheck: Date | null
  }>({
    isValid: true,
    errors: [],
    warnings: [],
    lastCheck: null
  })
  const [loading, setLoading] = useState(false)

  // Run validation check
  const runValidation = useCallback(async () => {
    if (!userId) return

    setLoading(true)
    
    try {
      // Import validation service dynamically to avoid SSR issues
      const { DataValidationService } = await import('@/lib/data-sync')
      
      const result = await DataValidationService.validateUserData(userId)
      
      setValidationStatus({
        isValid: result.isValid,
        errors: result.errors,
        warnings: result.warnings,
        lastCheck: new Date()
      })
      
      // Log validation results if there are issues
      if (!result.isValid || result.warnings.length > 0) {
        await ErrorLogger.logSystemEvent('data_validation_issues', {
          userId,
          errors: result.errors,
          warnings: result.warnings
        }, result.isValid ? ErrorSeverity.LOW : ErrorSeverity.MEDIUM)
      }
      
    } catch (err) {
      const error = new AppError(
        `Data validation failed for user ${userId}: ${err}`,
        ErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { userId, originalError: err }
      )
      
      ErrorLogger.logError(error)
      
      setValidationStatus({
        isValid: false,
        errors: ['Validation check failed'],
        warnings: [],
        lastCheck: new Date()
      })
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Auto-run validation on mount and user change
  useEffect(() => {
    if (userId) {
      runValidation()
    }
  }, [userId, runValidation])

  return {
    validationStatus,
    loading,
    runValidation
  }
}
