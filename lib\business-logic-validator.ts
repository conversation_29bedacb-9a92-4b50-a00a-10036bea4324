import { supabase } from './supabase-client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AppError, ErrorType, ErrorSeverity } from './error-handling'

// Business logic validation service to ensure consistency between web and bot
export class BusinessLogicValidator {
  
  // Validate share purchase business logic
  static async validateSharePurchase(
    userId: number,
    shareAmount: number,
    totalAmount: number,
    phaseId: number
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Get current phase information
      const { data: phase, error: phaseError } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('id', phaseId)
        .eq('is_active', true)
        .single()

      if (phaseError || !phase) {
        errors.push('Invalid or inactive investment phase')
        return { isValid: false, errors, warnings }
      }

      // Validate share amount
      if (shareAmount <= 0) {
        errors.push('Share amount must be greater than zero')
      }

      if (shareAmount !== Math.floor(shareAmount)) {
        errors.push('Share amount must be a whole number')
      }

      // Validate total amount
      const expectedAmount = shareAmount * phase.price_per_share
      if (Math.abs(totalAmount - expectedAmount) > 0.01) {
        errors.push(`Total amount mismatch: expected ${expectedAmount.toFixed(2)}, got ${totalAmount.toFixed(2)}`)
      }

      // Check phase availability
      const remainingShares = phase.shares_available - phase.shares_sold
      if (shareAmount > remainingShares) {
        errors.push(`Insufficient shares available: requested ${shareAmount}, available ${remainingShares}`)
      }

      // Check user's existing purchases for this phase
      const { data: existingPurchases } = await supabase
        .from('share_purchases')
        .select('shares_purchased')
        .eq('user_id', userId)

      if (existingPurchases) {
        const totalUserShares = existingPurchases.reduce((sum, p) => sum + (p.shares_purchased || 0), 0)
        
        // Warn if this is a large purchase relative to user's history
        if (shareAmount > totalUserShares * 2 && totalUserShares > 0) {
          warnings.push('Purchase amount is significantly larger than user\'s historical purchases')
        }
      }

      // Validate minimum purchase amount (business rule)
      const minPurchaseAmount = 10 // $10 minimum
      if (totalAmount < minPurchaseAmount) {
        errors.push(`Minimum purchase amount is $${minPurchaseAmount}`)
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
      
      await ErrorLogger.logError(new AppError(
        `Share purchase validation failed: ${error}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.HIGH,
        { userId, shareAmount, totalAmount, phaseId }
      ))
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  // Validate commission withdrawal business logic
  static async validateCommissionWithdrawal(
    userId: number,
    withdrawalAmount: number,
    walletAddress: string,
    network: string
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Get user's commission balance
      const { data: balance, error: balanceError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (balanceError || !balance) {
        errors.push('Commission balance not found')
        return { isValid: false, errors, warnings }
      }

      // Validate withdrawal amount
      if (withdrawalAmount <= 0) {
        errors.push('Withdrawal amount must be greater than zero')
      }

      const minWithdrawal = 10 // $10 minimum
      if (withdrawalAmount < minWithdrawal) {
        errors.push(`Minimum withdrawal amount is $${minWithdrawal}`)
      }

      if (withdrawalAmount > balance.usdt_balance) {
        errors.push(`Insufficient balance: requested ${withdrawalAmount}, available ${balance.usdt_balance}`)
      }

      // Validate wallet address format (basic validation)
      if (!walletAddress || walletAddress.length < 20) {
        errors.push('Invalid wallet address format')
      }

      // Validate network
      const supportedNetworks = ['ETH', 'BSC', 'POLYGON', 'TRON']
      if (!supportedNetworks.includes(network)) {
        errors.push(`Unsupported network: ${network}`)
      }

      // Check for recent withdrawals (rate limiting)
      const oneDayAgo = new Date()
      oneDayAgo.setDate(oneDayAgo.getDate() - 1)

      const { data: recentWithdrawals } = await supabase
        .from('commission_withdrawal_requests')
        .select('withdrawal_amount')
        .eq('user_id', userId)
        .gte('created_at', oneDayAgo.toISOString())

      if (recentWithdrawals && recentWithdrawals.length > 0) {
        const dailyTotal = recentWithdrawals.reduce((sum, w) => sum + w.withdrawal_amount, 0)
        const dailyLimit = 1000 // $1000 daily limit
        
        if (dailyTotal + withdrawalAmount > dailyLimit) {
          errors.push(`Daily withdrawal limit exceeded: ${dailyTotal + withdrawalAmount} > ${dailyLimit}`)
        }
      }

      // Check for pending withdrawals
      const { data: pendingWithdrawals } = await supabase
        .from('commission_withdrawal_requests')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'pending')

      if (pendingWithdrawals && pendingWithdrawals.length >= 3) {
        errors.push('Too many pending withdrawal requests. Please wait for approval.')
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
      
      await ErrorLogger.logError(new AppError(
        `Commission withdrawal validation failed: ${error}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.HIGH,
        { userId, withdrawalAmount, walletAddress, network }
      ))
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  // Validate referral business logic
  static async validateReferral(
    referrerUserId: number,
    referredUserId: number
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Check if users exist
      const { data: referrer } = await supabase
        .from('telegram_users')
        .select('id')
        .eq('id', referrerUserId)
        .single()

      const { data: referred } = await supabase
        .from('telegram_users')
        .select('id')
        .eq('id', referredUserId)
        .single()

      if (!referrer) {
        errors.push('Referrer user not found')
      }

      if (!referred) {
        errors.push('Referred user not found')
      }

      // Check for self-referral
      if (referrerUserId === referredUserId) {
        errors.push('Users cannot refer themselves')
      }

      // Check if referral already exists
      const { data: existingReferral } = await supabase
        .from('referrals')
        .select('id')
        .eq('referred_user_id', referredUserId)
        .single()

      if (existingReferral) {
        errors.push('User already has a referrer')
      }

      // Check for circular referrals (basic check)
      const { data: reverseReferral } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_user_id', referredUserId)
        .eq('referred_user_id', referrerUserId)
        .single()

      if (reverseReferral) {
        errors.push('Circular referral detected')
      }

      // Check referrer's referral count (business limit)
      const { data: referrerCount } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_user_id', referrerUserId)

      const maxReferrals = 1000 // Maximum referrals per user
      if (referrerCount && referrerCount.length >= maxReferrals) {
        errors.push(`Referrer has reached maximum referral limit (${maxReferrals})`)
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
      
      await ErrorLogger.logError(new AppError(
        `Referral validation failed: ${error}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.MEDIUM,
        { referrerUserId, referredUserId }
      ))
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  // Validate KYC submission business logic
  static async validateKYCSubmission(
    userId: number,
    kycData: {
      firstName: string
      lastName: string
      idType: string
      idNumber: string
      phoneNumber: string
      emailAddress: string
    }
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Check if user already has KYC
      const { data: existingKYC } = await supabase
        .from('kyc_information')
        .select('id, kyc_status')
        .eq('user_id', userId)
        .single()

      if (existingKYC) {
        if (existingKYC.kyc_status === 'approved') {
          errors.push('KYC already approved for this user')
        } else if (existingKYC.kyc_status === 'pending') {
          warnings.push('KYC submission already pending review')
        }
      }

      // Validate required fields
      if (!kycData.firstName || kycData.firstName.trim().length < 2) {
        errors.push('First name must be at least 2 characters')
      }

      if (!kycData.lastName || kycData.lastName.trim().length < 2) {
        errors.push('Last name must be at least 2 characters')
      }

      // Validate ID type
      const validIdTypes = ['national_id', 'passport', 'drivers_license']
      if (!validIdTypes.includes(kycData.idType)) {
        errors.push('Invalid ID type')
      }

      // Validate ID number (basic format check)
      if (!kycData.idNumber || kycData.idNumber.trim().length < 5) {
        errors.push('ID number must be at least 5 characters')
      }

      // Validate phone number (basic format)
      const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
      if (!phoneRegex.test(kycData.phoneNumber)) {
        errors.push('Invalid phone number format')
      }

      // Validate email address
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(kycData.emailAddress)) {
        errors.push('Invalid email address format')
      }

      // Check for duplicate ID numbers (fraud prevention)
      const { data: duplicateId } = await supabase
        .from('kyc_information')
        .select('user_id')
        .eq('id_number_hash', kycData.idNumber) // In production, this should be hashed
        .neq('user_id', userId)

      if (duplicateId && duplicateId.length > 0) {
        errors.push('ID number already registered by another user')
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
      
      await ErrorLogger.logError(new AppError(
        `KYC validation failed: ${error}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.HIGH,
        { userId, kycData: { ...kycData, idNumber: '[REDACTED]' } }
      ))
    }

    return { isValid: errors.length === 0, errors, warnings }
  }

  // Validate payment transaction business logic
  static async validatePaymentTransaction(
    userId: number,
    amount: number,
    currency: string,
    network: string,
    senderWallet?: string
  ): Promise<{ isValid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Validate amount
      if (amount <= 0) {
        errors.push('Payment amount must be greater than zero')
      }

      const minPayment = 1 // $1 minimum
      if (amount < minPayment) {
        errors.push(`Minimum payment amount is $${minPayment}`)
      }

      const maxPayment = 100000 // $100k maximum
      if (amount > maxPayment) {
        errors.push(`Maximum payment amount is $${maxPayment}`)
      }

      // Validate currency
      const supportedCurrencies = ['USDT', 'ZAR']
      if (!supportedCurrencies.includes(currency)) {
        errors.push(`Unsupported currency: ${currency}`)
      }

      // Validate network for crypto payments
      if (currency === 'USDT') {
        const supportedNetworks = ['ETH', 'BSC', 'POLYGON', 'TRON']
        if (!supportedNetworks.includes(network)) {
          errors.push(`Unsupported network for USDT: ${network}`)
        }

        // Validate wallet address for crypto
        if (senderWallet && senderWallet.length < 20) {
          errors.push('Invalid wallet address format')
        }
      } else if (currency === 'ZAR') {
        if (network !== 'BANK_TRANSFER') {
          errors.push('ZAR payments must use BANK_TRANSFER network')
        }
      }

      // Check for duplicate payments (within 5 minutes)
      const fiveMinutesAgo = new Date()
      fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

      const { data: recentPayments } = await supabase
        .from('crypto_payment_transactions')
        .select('amount, currency')
        .eq('user_id', userId)
        .gte('created_at', fiveMinutesAgo.toISOString())

      if (recentPayments) {
        const duplicatePayment = recentPayments.find(p => 
          Math.abs(p.amount - amount) < 0.01 && p.currency === currency
        )
        
        if (duplicatePayment) {
          warnings.push('Similar payment submitted recently - possible duplicate')
        }
      }

      // Check daily payment limits
      const oneDayAgo = new Date()
      oneDayAgo.setDate(oneDayAgo.getDate() - 1)

      const { data: dailyPayments } = await supabase
        .from('crypto_payment_transactions')
        .select('amount')
        .eq('user_id', userId)
        .gte('created_at', oneDayAgo.toISOString())

      if (dailyPayments) {
        const dailyTotal = dailyPayments.reduce((sum, p) => sum + p.amount, 0)
        const dailyLimit = 50000 // $50k daily limit
        
        if (dailyTotal + amount > dailyLimit) {
          errors.push(`Daily payment limit exceeded: ${dailyTotal + amount} > ${dailyLimit}`)
        }
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
      
      await ErrorLogger.logError(new AppError(
        `Payment validation failed: ${error}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.HIGH,
        { userId, amount, currency, network }
      ))
    }

    return { isValid: errors.length === 0, errors, warnings }
  }
}

export default BusinessLogicValidator
