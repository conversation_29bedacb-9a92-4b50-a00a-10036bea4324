// Country groups for business logic
export const AFRICAN_COUNTRIES = ['ZA', 'SZ', 'NA', 'BW', 'ZW', 'MZ', 'LS']
export const WESTERN_COUNTRIES = ['US', 'GB', 'CA', 'AU', 'NZ']
export const ASIAN_COUNTRIES = ['SG', 'AE']

// List of countries matching the bot's country selection
export const COUNTRIES = [
  { code: 'ZA', name: 'South Africa', region: 'africa' },
  { code: 'SZ', name: 'Eswatini', region: 'africa' },
  { code: 'NA', name: 'Namibia', region: 'africa' },
  { code: 'BW', name: 'Botswana', region: 'africa' },
  { code: 'ZW', name: 'Zimbabwe', region: 'africa' },
  { code: 'MZ', name: 'Mozambique', region: 'africa' },
  { code: 'LS', name: 'Lesotho', region: 'africa' },
  { code: 'US', name: 'United States', region: 'western' },
  { code: 'GB', name: 'United Kingdom', region: 'western' },
  { code: 'CA', name: 'Canada', region: 'western' },
  { code: 'AU', name: 'Australia', region: 'western' },
  { code: 'NZ', name: 'New Zealand', region: 'western' },
  { code: 'SG', name: 'Singapore', region: 'asia' },
  { code: 'AE', name: 'United Arab Emirates', region: 'asia' },
  { code: 'OTHER', name: 'Other Country', region: 'other' }
]

// Get country name from code
export const getCountryName = (countryCode: string): string => {
  const country = COUNTRIES.find(c => c.code === countryCode)
  return country ? country.name : 'Unknown Country'
}

// Get country region from code
export const getCountryRegion = (countryCode: string): string => {
  const country = COUNTRIES.find(c => c.code === countryCode)
  return country ? country.region : 'other'
}

// Check if country supports bank transfers (ZAR)
export const supportsZARPayments = (countryCode: string): boolean => {
  return ['ZA', 'SZ', 'NA'].includes(countryCode)
}

// Check if country supports USDT payments
export const supportsUSDTPayments = (countryCode: string): boolean => {
  // All countries support USDT except those that explicitly don't
  return true
}

// Get available payment methods for a country
export const getAvailablePaymentMethods = (countryCode: string): string[] => {
  const methods = []
  
  if (supportsUSDTPayments(countryCode)) {
    methods.push('USDT')
  }
  
  if (supportsZARPayments(countryCode)) {
    methods.push('ZAR')
  }
  
  return methods
}

// Get recommended USDT network for a country
export const getRecommendedUSDTNetwork = (countryCode: string): string => {
  // Default to BSC for most countries
  if (AFRICAN_COUNTRIES.includes(countryCode)) {
    return 'BSC' // Binance Smart Chain is popular in Africa
  } else if (WESTERN_COUNTRIES.includes(countryCode)) {
    return 'ETH' // Ethereum is popular in Western countries
  } else if (ASIAN_COUNTRIES.includes(countryCode)) {
    return 'TRON' // TRON is popular in some Asian countries
  }
  
  return 'BSC' // Default to BSC
}

// Validate if a country requires additional KYC
export const requiresEnhancedKYC = (countryCode: string): boolean => {
  // Some countries might require enhanced KYC due to regulations
  const enhancedKYCCountries = ['US', 'GB', 'CA']
  return enhancedKYCCountries.includes(countryCode)
}

// Get country-specific instructions
export const getCountryInstructions = (countryCode: string): string => {
  if (supportsZARPayments(countryCode)) {
    return 'You can pay using ZAR bank transfer or USDT cryptocurrency.'
  } else {
    return 'You can pay using USDT cryptocurrency on multiple networks.'
  }
}
