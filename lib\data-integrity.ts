import { supabase } from './supabase-client'

// Data integrity monitoring and validation service
export class DataIntegrityService {
  
  // Run comprehensive data integrity checks
  static async runIntegrityChecks(): Promise<{
    passed: boolean
    results: IntegrityCheckResult[]
    summary: {
      total: number
      passed: number
      failed: number
      warnings: number
    }
  }> {
    const results: IntegrityCheckResult[] = []

    // Run all integrity checks
    results.push(await this.checkUserDataIntegrity())
    results.push(await this.checkSharePurchaseIntegrity())
    results.push(await this.checkCommissionIntegrity())
    results.push(await this.checkPaymentIntegrity())
    results.push(await this.checkReferralIntegrity())
    results.push(await this.checkPhaseIntegrity())
    results.push(await this.checkKYCIntegrity())

    // Calculate summary
    const summary = {
      total: results.length,
      passed: results.filter(r => r.status === 'passed').length,
      failed: results.filter(r => r.status === 'failed').length,
      warnings: results.filter(r => r.status === 'warning').length
    }

    return {
      passed: summary.failed === 0,
      results,
      summary
    }
  }

  // Check user data integrity
  private static async checkUserDataIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for duplicate telegram_ids
      const { data: duplicates } = await supabase
        .from('telegram_users')
        .select('telegram_id, count(*)')
        .group('telegram_id')
        .having('count(*)', 'gt', 1)

      if (duplicates && duplicates.length > 0) {
        issues.push(`Found ${duplicates.length} duplicate telegram_ids`)
      }

      // Check for users without required fields
      const { data: incompleteUsers } = await supabase
        .from('telegram_users')
        .select('id, first_name, telegram_id')
        .or('first_name.is.null,telegram_id.is.null')

      if (incompleteUsers && incompleteUsers.length > 0) {
        warnings.push(`Found ${incompleteUsers.length} users with missing required fields`)
      }

      // Check for invalid telegram_ids (should be numeric)
      const { data: invalidIds } = await supabase
        .from('telegram_users')
        .select('id, telegram_id')
        .not('telegram_id', 'like', '%[0-9]%')

      if (invalidIds && invalidIds.length > 0) {
        issues.push(`Found ${invalidIds.length} users with invalid telegram_ids`)
      }

    } catch (error) {
      issues.push(`Error checking user data integrity: ${error}`)
    }

    return {
      checkName: 'User Data Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check share purchase integrity
  private static async checkSharePurchaseIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for negative share amounts
      const { data: negativeShares } = await supabase
        .from('share_purchases')
        .select('id, user_id, shares_purchased')
        .lt('shares_purchased', 0)

      if (negativeShares && negativeShares.length > 0) {
        issues.push(`Found ${negativeShares.length} share purchases with negative amounts`)
      }

      // Check for zero-amount purchases
      const { data: zeroShares } = await supabase
        .from('share_purchases')
        .select('id, user_id, shares_purchased')
        .eq('shares_purchased', 0)

      if (zeroShares && zeroShares.length > 0) {
        warnings.push(`Found ${zeroShares.length} share purchases with zero shares`)
      }

      // Check for orphaned share purchases
      const { data: orphanedShares } = await supabase
        .from('share_purchases')
        .select('id, user_id')
        .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

      if (orphanedShares && orphanedShares.length > 0) {
        issues.push(`Found ${orphanedShares.length} orphaned share purchases`)
      }

      // Check for inconsistent pricing
      const { data: pricingIssues } = await supabase
        .from('share_purchases')
        .select('id, total_amount, shares_purchased')
        .neq('shares_purchased', 0)

      if (pricingIssues) {
        const inconsistentPricing = pricingIssues.filter(purchase => {
          const pricePerShare = purchase.total_amount / purchase.shares_purchased
          return pricePerShare < 0.01 || pricePerShare > 1000 // Reasonable bounds
        })

        if (inconsistentPricing.length > 0) {
          warnings.push(`Found ${inconsistentPricing.length} purchases with unusual pricing`)
        }
      }

    } catch (error) {
      issues.push(`Error checking share purchase integrity: ${error}`)
    }

    return {
      checkName: 'Share Purchase Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check commission system integrity
  private static async checkCommissionIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for negative commission balances
      const { data: negativeBalances } = await supabase
        .from('commission_balances')
        .select('user_id, usdt_balance')
        .lt('usdt_balance', 0)

      if (negativeBalances && negativeBalances.length > 0) {
        issues.push(`Found ${negativeBalances.length} negative commission balances`)
      }

      // Check commission transaction consistency
      const { data: commissionUsers } = await supabase
        .from('commission_balances')
        .select('user_id, usdt_balance')

      if (commissionUsers) {
        for (const user of commissionUsers) {
          const { data: transactions } = await supabase
            .from('commission_transactions')
            .select('amount')
            .eq('user_id', user.user_id)
            .eq('status', 'completed')

          if (transactions) {
            const calculatedBalance = transactions.reduce((sum, tx) => sum + tx.amount, 0)
            const actualBalance = user.usdt_balance

            if (Math.abs(calculatedBalance - actualBalance) > 0.01) {
              issues.push(`Commission balance mismatch for user ${user.user_id}: calculated ${calculatedBalance}, actual ${actualBalance}`)
            }
          }
        }
      }

      // Check for orphaned commission records
      const { data: orphanedCommissions } = await supabase
        .from('commission_balances')
        .select('user_id')
        .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

      if (orphanedCommissions && orphanedCommissions.length > 0) {
        issues.push(`Found ${orphanedCommissions.length} orphaned commission records`)
      }

    } catch (error) {
      issues.push(`Error checking commission integrity: ${error}`)
    }

    return {
      checkName: 'Commission System Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check payment system integrity
  private static async checkPaymentIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for payments without valid users
      const { data: orphanedPayments } = await supabase
        .from('crypto_payment_transactions')
        .select('id, user_id')
        .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

      if (orphanedPayments && orphanedPayments.length > 0) {
        issues.push(`Found ${orphanedPayments.length} orphaned payment transactions`)
      }

      // Check for old pending payments
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { data: oldPendingPayments } = await supabase
        .from('crypto_payment_transactions')
        .select('id, created_at')
        .eq('status', 'pending')
        .lt('created_at', thirtyDaysAgo.toISOString())

      if (oldPendingPayments && oldPendingPayments.length > 0) {
        warnings.push(`Found ${oldPendingPayments.length} payments pending for over 30 days`)
      }

      // Check for duplicate transaction hashes
      const { data: duplicateHashes } = await supabase
        .from('crypto_payment_transactions')
        .select('transaction_hash, count(*)')
        .not('transaction_hash', 'is', null)
        .group('transaction_hash')
        .having('count(*)', 'gt', 1)

      if (duplicateHashes && duplicateHashes.length > 0) {
        warnings.push(`Found ${duplicateHashes.length} duplicate transaction hashes`)
      }

    } catch (error) {
      issues.push(`Error checking payment integrity: ${error}`)
    }

    return {
      checkName: 'Payment System Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check referral system integrity
  private static async checkReferralIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for self-referrals
      const { data: selfReferrals } = await supabase
        .from('referrals')
        .select('id, referrer_user_id, referred_user_id')
        .eq('referrer_user_id', 'referred_user_id')

      if (selfReferrals && selfReferrals.length > 0) {
        issues.push(`Found ${selfReferrals.length} self-referrals`)
      }

      // Check for circular referrals
      const { data: allReferrals } = await supabase
        .from('referrals')
        .select('referrer_user_id, referred_user_id')

      if (allReferrals) {
        const referralMap = new Map<number, number>()
        allReferrals.forEach(ref => {
          referralMap.set(ref.referred_user_id, ref.referrer_user_id)
        })

        let circularCount = 0
        referralMap.forEach((referrer, referred) => {
          const visited = new Set<number>()
          let current = referrer
          
          while (current && !visited.has(current)) {
            visited.add(current)
            current = referralMap.get(current) || 0
            
            if (current === referred) {
              circularCount++
              break
            }
          }
        })

        if (circularCount > 0) {
          issues.push(`Found ${circularCount} circular referral chains`)
        }
      }

      // Check for orphaned referrals
      const { data: orphanedReferrals } = await supabase
        .from('referrals')
        .select('id')
        .or('referrer_user_id.not.in.(SELECT id FROM telegram_users),referred_user_id.not.in.(SELECT id FROM telegram_users)')

      if (orphanedReferrals && orphanedReferrals.length > 0) {
        issues.push(`Found ${orphanedReferrals.length} orphaned referral records`)
      }

    } catch (error) {
      issues.push(`Error checking referral integrity: ${error}`)
    }

    return {
      checkName: 'Referral System Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check investment phase integrity
  private static async checkPhaseIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for multiple active phases
      const { data: activePhases } = await supabase
        .from('investment_phases')
        .select('id, phase_number')
        .eq('is_active', true)

      if (activePhases && activePhases.length > 1) {
        issues.push(`Found ${activePhases.length} active investment phases (should be 1)`)
      } else if (!activePhases || activePhases.length === 0) {
        warnings.push('No active investment phase found')
      }

      // Check for negative pricing
      const { data: negativePricing } = await supabase
        .from('investment_phases')
        .select('id, phase_number, price_per_share')
        .lte('price_per_share', 0)

      if (negativePricing && negativePricing.length > 0) {
        issues.push(`Found ${negativePricing.length} phases with negative or zero pricing`)
      }

      // Check for overlapping phase dates
      const { data: allPhases } = await supabase
        .from('investment_phases')
        .select('id, phase_number, start_date, end_date')
        .order('start_date')

      if (allPhases && allPhases.length > 1) {
        for (let i = 0; i < allPhases.length - 1; i++) {
          const current = allPhases[i]
          const next = allPhases[i + 1]
          
          if (current.end_date && next.start_date) {
            if (new Date(current.end_date) > new Date(next.start_date)) {
              issues.push(`Phase ${current.phase_number} overlaps with phase ${next.phase_number}`)
            }
          }
        }
      }

    } catch (error) {
      issues.push(`Error checking phase integrity: ${error}`)
    }

    return {
      checkName: 'Investment Phase Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }

  // Check KYC data integrity
  private static async checkKYCIntegrity(): Promise<IntegrityCheckResult> {
    const issues: string[] = []
    const warnings: string[] = []

    try {
      // Check for duplicate KYC submissions
      const { data: duplicateKYC } = await supabase
        .from('kyc_information')
        .select('user_id, count(*)')
        .group('user_id')
        .having('count(*)', 'gt', 1)

      if (duplicateKYC && duplicateKYC.length > 0) {
        issues.push(`Found ${duplicateKYC.length} users with multiple KYC submissions`)
      }

      // Check for orphaned KYC records
      const { data: orphanedKYC } = await supabase
        .from('kyc_information')
        .select('id, user_id')
        .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

      if (orphanedKYC && orphanedKYC.length > 0) {
        issues.push(`Found ${orphanedKYC.length} orphaned KYC records`)
      }

      // Check for incomplete KYC data
      const { data: incompleteKYC } = await supabase
        .from('kyc_information')
        .select('id, user_id')
        .or('first_name.is.null,last_name.is.null,id_number_encrypted.is.null')

      if (incompleteKYC && incompleteKYC.length > 0) {
        warnings.push(`Found ${incompleteKYC.length} incomplete KYC submissions`)
      }

    } catch (error) {
      issues.push(`Error checking KYC integrity: ${error}`)
    }

    return {
      checkName: 'KYC Data Integrity',
      status: issues.length > 0 ? 'failed' : warnings.length > 0 ? 'warning' : 'passed',
      issues,
      warnings,
      timestamp: new Date().toISOString()
    }
  }
}

// Interface for integrity check results
interface IntegrityCheckResult {
  checkName: string
  status: 'passed' | 'failed' | 'warning'
  issues: string[]
  warnings: string[]
  timestamp: string
}

// Export the service
export default DataIntegrityService
