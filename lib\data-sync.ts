import { supabase } from './supabase-client'
import type { RealtimeChannel } from '@supabase/supabase-js'

// Data synchronization service for real-time updates between web and bot
export class DataSyncService {
  private channels: Map<string, RealtimeChannel> = new Map()
  private subscribers: Map<string, Set<(data: any) => void>> = new Map()

  // Subscribe to real-time updates for a specific table
  subscribeToTable(
    tableName: string, 
    callback: (data: any) => void,
    filter?: { column: string; value: any }
  ): () => void {
    // Add callback to subscribers
    if (!this.subscribers.has(tableName)) {
      this.subscribers.set(tableName, new Set())
    }
    this.subscribers.get(tableName)!.add(callback)

    // Create channel if it doesn't exist
    if (!this.channels.has(tableName)) {
      const channel = supabase
        .channel(`${tableName}_changes`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: tableName,
            ...(filter && { filter: `${filter.column}=eq.${filter.value}` })
          },
          (payload) => {
            // Notify all subscribers
            const subscribers = this.subscribers.get(tableName)
            if (subscribers) {
              subscribers.forEach(cb => cb(payload))
            }
          }
        )
        .subscribe()

      this.channels.set(tableName, channel)
    }

    // Return unsubscribe function
    return () => {
      const subscribers = this.subscribers.get(tableName)
      if (subscribers) {
        subscribers.delete(callback)
        
        // If no more subscribers, remove channel
        if (subscribers.size === 0) {
          const channel = this.channels.get(tableName)
          if (channel) {
            supabase.removeChannel(channel)
            this.channels.delete(tableName)
            this.subscribers.delete(tableName)
          }
        }
      }
    }
  }

  // Subscribe to user-specific updates
  subscribeToUserUpdates(userId: number, callback: (data: any) => void): () => void {
    const unsubscribeFunctions: (() => void)[] = []

    // Subscribe to user's share purchases
    unsubscribeFunctions.push(
      this.subscribeToTable('share_purchases', callback, { column: 'user_id', value: userId })
    )

    // Subscribe to user's payments
    unsubscribeFunctions.push(
      this.subscribeToTable('crypto_payment_transactions', callback, { column: 'user_id', value: userId })
    )

    // Subscribe to user's commission transactions
    unsubscribeFunctions.push(
      this.subscribeToTable('commission_transactions', callback, { column: 'user_id', value: userId })
    )

    // Subscribe to user's referrals
    unsubscribeFunctions.push(
      this.subscribeToTable('referrals', callback, { column: 'referrer_user_id', value: userId })
    )

    // Subscribe to user's KYC information
    unsubscribeFunctions.push(
      this.subscribeToTable('kyc_information', callback, { column: 'user_id', value: userId })
    )

    // Return function to unsubscribe from all
    return () => {
      unsubscribeFunctions.forEach(unsub => unsub())
    }
  }

  // Clean up all subscriptions
  cleanup(): void {
    this.channels.forEach(channel => {
      supabase.removeChannel(channel)
    })
    this.channels.clear()
    this.subscribers.clear()
  }
}

// Data validation service to ensure consistency
export class DataValidationService {
  // Validate user data consistency
  static async validateUserData(userId: number): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Get user data
      const { data: userData, error: userError } = await supabase
        .from('telegram_users')
        .select('*')
        .eq('id', userId)
        .single()

      if (userError || !userData) {
        errors.push('User not found in telegram_users table')
        return { isValid: false, errors, warnings }
      }

      // Validate share purchases
      const { data: shareData, error: shareError } = await supabase
        .from('share_purchases')
        .select('*')
        .eq('user_id', userId)

      if (shareError) {
        errors.push(`Error fetching share purchases: ${shareError.message}`)
      } else if (shareData) {
        // Validate share purchase totals
        const totalShares = shareData.reduce((sum, purchase) => sum + (purchase.shares_purchased || 0), 0)
        const totalAmount = shareData.reduce((sum, purchase) => sum + (purchase.total_amount || 0), 0)

        if (totalShares < 0) {
          errors.push('Negative total shares detected')
        }
        if (totalAmount < 0) {
          errors.push('Negative total amount detected')
        }
      }

      // Validate commission balance
      const { data: commissionData, error: commissionError } = await supabase
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (commissionError && commissionError.code !== 'PGRST116') {
        warnings.push(`Commission balance check failed: ${commissionError.message}`)
      } else if (commissionData) {
        if (commissionData.usdt_balance < 0) {
          errors.push('Negative commission balance detected')
        }
      }

      // Validate payment transactions
      const { data: paymentData, error: paymentError } = await supabase
        .from('crypto_payment_transactions')
        .select('*')
        .eq('user_id', userId)

      if (paymentError) {
        warnings.push(`Payment transaction check failed: ${paymentError.message}`)
      } else if (paymentData) {
        const pendingPayments = paymentData.filter(p => p.status === 'pending')
        if (pendingPayments.length > 10) {
          warnings.push('High number of pending payments detected')
        }
      }

      // Validate referral data
      const { data: referralData, error: referralError } = await supabase
        .from('referrals')
        .select('*')
        .eq('referrer_user_id', userId)

      if (referralError) {
        warnings.push(`Referral data check failed: ${referralError.message}`)
      }

    } catch (error) {
      errors.push(`Validation error: ${error}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Validate business logic consistency
  static async validateBusinessLogic(): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Check for orphaned records
      const { data: orphanedShares } = await supabase
        .from('share_purchases')
        .select('id, user_id')
        .not('user_id', 'in', `(SELECT id FROM telegram_users)`)

      if (orphanedShares && orphanedShares.length > 0) {
        errors.push(`Found ${orphanedShares.length} orphaned share purchases`)
      }

      // Check for negative balances
      const { data: negativeBalances } = await supabase
        .from('commission_balances')
        .select('user_id, usdt_balance')
        .lt('usdt_balance', 0)

      if (negativeBalances && negativeBalances.length > 0) {
        errors.push(`Found ${negativeBalances.length} negative commission balances`)
      }

      // Check for duplicate referrals
      const { data: duplicateReferrals } = await supabase
        .from('referrals')
        .select('referred_user_id, count(*)')
        .group('referred_user_id')
        .having('count(*)', 'gt', 1)

      if (duplicateReferrals && duplicateReferrals.length > 0) {
        warnings.push(`Found ${duplicateReferrals.length} users with multiple referrers`)
      }

      // Check for inconsistent phase data
      const { data: phaseData } = await supabase
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)

      if (phaseData && phaseData.length > 1) {
        errors.push('Multiple active investment phases detected')
      } else if (!phaseData || phaseData.length === 0) {
        warnings.push('No active investment phase found')
      }

    } catch (error) {
      errors.push(`Business logic validation error: ${error}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Conflict resolution service
export class ConflictResolutionService {
  // Resolve conflicts in concurrent updates
  static async resolveUserDataConflict(
    userId: number,
    conflictType: 'commission' | 'shares' | 'payment',
    webData: any,
    botData: any
  ): Promise<{ resolved: boolean; finalData: any; strategy: string }> {
    
    switch (conflictType) {
      case 'commission':
        // For commission conflicts, use the higher balance (safer approach)
        const finalCommission = Math.max(webData.usdt_balance || 0, botData.usdt_balance || 0)
        return {
          resolved: true,
          finalData: { ...webData, usdt_balance: finalCommission },
          strategy: 'max_balance'
        }

      case 'shares':
        // For share conflicts, use the higher share count (safer approach)
        const finalShares = Math.max(webData.shares_purchased || 0, botData.shares_purchased || 0)
        return {
          resolved: true,
          finalData: { ...webData, shares_purchased: finalShares },
          strategy: 'max_shares'
        }

      case 'payment':
        // For payment conflicts, prefer 'approved' status over others
        const statusPriority = { 'approved': 3, 'pending': 2, 'rejected': 1 }
        const webPriority = statusPriority[webData.status as keyof typeof statusPriority] || 0
        const botPriority = statusPriority[botData.status as keyof typeof statusPriority] || 0
        
        const finalStatus = webPriority >= botPriority ? webData.status : botData.status
        return {
          resolved: true,
          finalData: { ...webData, status: finalStatus },
          strategy: 'status_priority'
        }

      default:
        return {
          resolved: false,
          finalData: webData,
          strategy: 'no_resolution'
        }
    }
  }
}

// Create singleton instance
export const dataSyncService = new DataSyncService()

// Cleanup function for app shutdown
export const cleanupDataSync = () => {
  dataSyncService.cleanup()
}
