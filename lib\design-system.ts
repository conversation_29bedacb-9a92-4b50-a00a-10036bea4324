// Comprehensive Design System for Aureus Alliance Holdings
// Professional gold mining investment platform design tokens

export const designTokens = {
  // Color System - Professional Gold Mining Theme
  colors: {
    // Primary Brand Colors
    primary: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24', // Main gold
      500: '#f59e0b', // Primary gold
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03'
    },
    
    // Secondary Colors
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // Cyber blue
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49'
    },
    
    // Neutral Colors
    neutral: {
      0: '#ffffff',
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712'
    },
    
    // Status Colors
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      500: '#10b981',
      600: '#059669',
      700: '#047857'
    },
    
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309'
    },
    
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c'
    },
    
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8'
    }
  },
  
  // Typography System
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace']
    },
    
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }]
    },
    
    fontWeight: {
      thin: '100',
      extralight: '200',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    },
    
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    }
  },
  
  // Spacing System
  spacing: {
    px: '1px',
    0: '0px',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem'
  },
  
  // Border Radius
  borderRadius: {
    none: '0px',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px'
  },
  
  // Shadows
  boxShadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: 'none',
    
    // Custom gold shadows
    gold: '0 4px 14px 0 rgb(251 191 36 / 0.39)',
    'gold-lg': '0 10px 40px rgb(251 191 36 / 0.3)',
    'gold-xl': '0 20px 60px rgb(251 191 36 / 0.4)'
  },
  
  // Animation & Transitions
  animation: {
    duration: {
      75: '75ms',
      100: '100ms',
      150: '150ms',
      200: '200ms',
      300: '300ms',
      500: '500ms',
      700: '700ms',
      1000: '1000ms'
    },
    
    easing: {
      linear: 'linear',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  },
  
  // Breakpoints
  screens: {
    xs: '475px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },
  
  // Z-Index Scale
  zIndex: {
    0: '0',
    10: '10',
    20: '20',
    30: '30',
    40: '40',
    50: '50',
    auto: 'auto',
    dropdown: '1000',
    sticky: '1020',
    fixed: '1030',
    modal: '1040',
    popover: '1050',
    tooltip: '1060',
    toast: '1070'
  }
}

// Component Variants
export const componentVariants = {
  button: {
    primary: {
      base: 'bg-gradient-to-r from-primary-400 to-primary-500 text-white border-transparent',
      hover: 'hover:from-primary-500 hover:to-primary-600 hover:shadow-gold',
      focus: 'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
      disabled: 'disabled:opacity-50 disabled:cursor-not-allowed'
    },
    secondary: {
      base: 'bg-white text-neutral-900 border-neutral-300',
      hover: 'hover:bg-neutral-50 hover:border-neutral-400',
      focus: 'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
      disabled: 'disabled:opacity-50 disabled:cursor-not-allowed'
    },
    outline: {
      base: 'bg-transparent text-primary-600 border-primary-600',
      hover: 'hover:bg-primary-50 hover:text-primary-700',
      focus: 'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
      disabled: 'disabled:opacity-50 disabled:cursor-not-allowed'
    }
  },
  
  card: {
    default: {
      base: 'bg-white rounded-xl border border-neutral-200 shadow-sm',
      hover: 'hover:shadow-md transition-shadow duration-200'
    },
    elevated: {
      base: 'bg-white rounded-xl shadow-lg border-0',
      hover: 'hover:shadow-xl transition-shadow duration-200'
    },
    gold: {
      base: 'bg-gradient-to-br from-primary-50 to-primary-100 border border-primary-200 rounded-xl',
      hover: 'hover:shadow-gold transition-all duration-200'
    }
  },
  
  input: {
    default: {
      base: 'w-full px-3 py-2 border border-neutral-300 rounded-lg bg-white text-neutral-900',
      focus: 'focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
      error: 'border-error-500 focus:ring-error-500 focus:border-error-500',
      disabled: 'disabled:bg-neutral-50 disabled:text-neutral-500 disabled:cursor-not-allowed'
    }
  }
}

// Utility Functions
export const getColorValue = (colorPath: string) => {
  const keys = colorPath.split('.')
  let value: any = designTokens.colors
  
  for (const key of keys) {
    value = value[key]
    if (value === undefined) return null
  }
  
  return value
}

export const getSpacingValue = (spacing: keyof typeof designTokens.spacing) => {
  return designTokens.spacing[spacing]
}

export const getFontSize = (size: keyof typeof designTokens.typography.fontSize) => {
  return designTokens.typography.fontSize[size]
}

// Responsive Utilities
export const responsive = {
  mobile: (styles: string) => `@media (max-width: 767px) { ${styles} }`,
  tablet: (styles: string) => `@media (min-width: 768px) and (max-width: 1023px) { ${styles} }`,
  desktop: (styles: string) => `@media (min-width: 1024px) { ${styles} }`
}

// Accessibility Helpers
export const a11y = {
  srOnly: 'sr-only',
  focusVisible: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
  skipLink: 'absolute left-[-10000px] top-auto w-1 h-1 overflow-hidden focus:left-6 focus:top-7 focus:w-auto focus:h-auto focus:overflow-visible'
}

// Animation Presets
export const animations = {
  // Entrance animations
  fadeIn: 'animate-in fade-in duration-300',
  slideInFromTop: 'animate-in slide-in-from-top-4 duration-300',
  slideInFromBottom: 'animate-in slide-in-from-bottom-4 duration-300',
  slideInFromLeft: 'animate-in slide-in-from-left-4 duration-300',
  slideInFromRight: 'animate-in slide-in-from-right-4 duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-300',

  // Exit animations
  fadeOut: 'animate-out fade-out duration-200',
  slideOutToTop: 'animate-out slide-out-to-top-4 duration-200',
  slideOutToBottom: 'animate-out slide-out-to-bottom-4 duration-200',
  slideOutToLeft: 'animate-out slide-out-to-left-4 duration-200',
  slideOutToRight: 'animate-out slide-out-to-right-4 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-200',

  // Hover animations
  hoverScale: 'hover:scale-105 transition-transform duration-200',
  hoverLift: 'hover:-translate-y-1 transition-transform duration-200',
  hoverGlow: 'hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-200',
  hoverGoldGlow: 'hover:shadow-lg hover:shadow-yellow-500/25 transition-all duration-200',

  // Loading animations
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
  ping: 'animate-ping'
}

// Layout utilities
export const layouts = {
  container: 'container mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-12 sm:py-16 lg:py-20',
  grid: {
    responsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    dashboard: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4',
    auto: 'grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6'
  },
  flex: {
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    column: 'flex flex-col',
    wrap: 'flex flex-wrap'
  }
}

// Typography utilities
export const typography = {
  heading: {
    h1: 'text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight',
    h2: 'text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight',
    h3: 'text-2xl sm:text-3xl font-bold tracking-tight',
    h4: 'text-xl sm:text-2xl font-semibold',
    h5: 'text-lg sm:text-xl font-semibold',
    h6: 'text-base sm:text-lg font-semibold'
  },
  body: {
    large: 'text-lg leading-relaxed',
    base: 'text-base leading-relaxed',
    small: 'text-sm leading-relaxed',
    xs: 'text-xs leading-relaxed'
  },
  display: {
    hero: 'text-5xl sm:text-6xl lg:text-7xl font-black tracking-tight',
    large: 'text-4xl sm:text-5xl font-black tracking-tight',
    medium: 'text-3xl sm:text-4xl font-bold tracking-tight'
  }
}

export default designTokens
