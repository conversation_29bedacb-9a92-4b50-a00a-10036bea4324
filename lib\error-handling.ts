import { supabase } from './supabase-client'

// Error types for better categorization
export enum ErrorType {
  DATABASE = 'database',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NETWORK = 'network',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Custom error class for application errors
export class AppError extends Error {
  public readonly type: ErrorType
  public readonly severity: ErrorSeverity
  public readonly context: Record<string, any>
  public readonly timestamp: Date
  public readonly userId?: number
  public readonly userAgent?: string
  public readonly url?: string

  constructor(
    message: string,
    type: ErrorType = ErrorType.SYSTEM,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Record<string, any> = {},
    userId?: number
  ) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.severity = severity
    this.context = context
    this.timestamp = new Date()
    this.userId = userId

    // Capture browser context if available
    if (typeof window !== 'undefined') {
      this.userAgent = window.navigator.userAgent
      this.url = window.location.href
    }

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError)
    }
  }
}

// Error logging service
export class ErrorLogger {
  // Log error to database and console
  static async logError(error: AppError | Error, additionalContext?: Record<string, any>): Promise<void> {
    try {
      // Prepare error data
      const errorData = {
        message: error.message,
        stack: error.stack,
        type: error instanceof AppError ? error.type : ErrorType.SYSTEM,
        severity: error instanceof AppError ? error.severity : ErrorSeverity.MEDIUM,
        context: {
          ...(error instanceof AppError ? error.context : {}),
          ...additionalContext
        },
        user_id: error instanceof AppError ? error.userId : null,
        user_agent: error instanceof AppError ? error.userAgent : null,
        url: error instanceof AppError ? error.url : null,
        timestamp: new Date().toISOString()
      }

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Application Error:', errorData)
      }

      // Log to database (create error_logs table if needed)
      await supabase
        .from('error_logs')
        .insert(errorData)

    } catch (logError) {
      // Fallback to console if database logging fails
      console.error('Failed to log error to database:', logError)
      console.error('Original error:', error)
    }
  }

  // Log user action for audit trail
  static async logUserAction(
    userId: number,
    action: string,
    details: Record<string, any> = {},
    success: boolean = true
  ): Promise<void> {
    try {
      await supabase
        .from('user_action_logs')
        .insert({
          user_id: userId,
          action,
          details,
          success,
          timestamp: new Date().toISOString(),
          user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : null,
          url: typeof window !== 'undefined' ? window.location.href : null
        })
    } catch (error) {
      console.error('Failed to log user action:', error)
    }
  }

  // Log system event
  static async logSystemEvent(
    event: string,
    details: Record<string, any> = {},
    severity: ErrorSeverity = ErrorSeverity.LOW
  ): Promise<void> {
    try {
      await supabase
        .from('system_event_logs')
        .insert({
          event,
          details,
          severity,
          timestamp: new Date().toISOString()
        })
    } catch (error) {
      console.error('Failed to log system event:', error)
    }
  }
}

// Database operation wrapper with error handling
export class DatabaseOperations {
  // Safe database query with error handling
  static async safeQuery<T>(
    operation: () => Promise<{ data: T | null; error: any }>,
    context: Record<string, any> = {}
  ): Promise<{ data: T | null; error: AppError | null }> {
    try {
      const result = await operation()
      
      if (result.error) {
        const appError = new AppError(
          `Database operation failed: ${result.error.message}`,
          ErrorType.DATABASE,
          ErrorSeverity.HIGH,
          { ...context, originalError: result.error }
        )
        
        await ErrorLogger.logError(appError)
        return { data: null, error: appError }
      }
      
      return { data: result.data, error: null }
    } catch (error) {
      const appError = new AppError(
        `Database operation exception: ${error}`,
        ErrorType.DATABASE,
        ErrorSeverity.CRITICAL,
        { ...context, originalError: error }
      )
      
      await ErrorLogger.logError(appError)
      return { data: null, error: appError }
    }
  }

  // Transaction wrapper with rollback
  static async safeTransaction<T>(
    operations: (() => Promise<any>)[],
    context: Record<string, any> = {}
  ): Promise<{ success: boolean; results: T[]; error: AppError | null }> {
    const results: T[] = []
    
    try {
      // Execute all operations
      for (const operation of operations) {
        const result = await operation()
        results.push(result)
      }
      
      return { success: true, results, error: null }
    } catch (error) {
      const appError = new AppError(
        `Transaction failed: ${error}`,
        ErrorType.DATABASE,
        ErrorSeverity.HIGH,
        { ...context, originalError: error, completedOperations: results.length }
      )
      
      await ErrorLogger.logError(appError)
      
      // Log transaction rollback
      await ErrorLogger.logSystemEvent('transaction_rollback', {
        context,
        completedOperations: results.length,
        totalOperations: operations.length
      }, ErrorSeverity.MEDIUM)
      
      return { success: false, results: [], error: appError }
    }
  }
}

// Validation helper with error handling
export class ValidationHelper {
  // Validate required fields
  static validateRequired(
    data: Record<string, any>,
    requiredFields: string[],
    context: Record<string, any> = {}
  ): { isValid: boolean; errors: string[]; appError?: AppError } {
    const errors: string[] = []
    
    requiredFields.forEach(field => {
      if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
        errors.push(`${field} is required`)
      }
    })
    
    if (errors.length > 0) {
      const appError = new AppError(
        `Validation failed: ${errors.join(', ')}`,
        ErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { ...context, validationErrors: errors, data }
      )
      
      return { isValid: false, errors, appError }
    }
    
    return { isValid: true, errors: [] }
  }

  // Validate data types
  static validateTypes(
    data: Record<string, any>,
    typeMap: Record<string, 'string' | 'number' | 'boolean' | 'object'>,
    context: Record<string, any> = {}
  ): { isValid: boolean; errors: string[]; appError?: AppError } {
    const errors: string[] = []
    
    Object.entries(typeMap).forEach(([field, expectedType]) => {
      if (data[field] !== undefined && typeof data[field] !== expectedType) {
        errors.push(`${field} must be of type ${expectedType}`)
      }
    })
    
    if (errors.length > 0) {
      const appError = new AppError(
        `Type validation failed: ${errors.join(', ')}`,
        ErrorType.VALIDATION,
        ErrorSeverity.MEDIUM,
        { ...context, typeErrors: errors, data }
      )
      
      return { isValid: false, errors, appError }
    }
    
    return { isValid: true, errors: [] }
  }

  // Validate business rules
  static validateBusinessRules(
    data: Record<string, any>,
    rules: ((data: Record<string, any>) => { isValid: boolean; error?: string })[],
    context: Record<string, any> = {}
  ): { isValid: boolean; errors: string[]; appError?: AppError } {
    const errors: string[] = []
    
    rules.forEach(rule => {
      const result = rule(data)
      if (!result.isValid && result.error) {
        errors.push(result.error)
      }
    })
    
    if (errors.length > 0) {
      const appError = new AppError(
        `Business rule validation failed: ${errors.join(', ')}`,
        ErrorType.BUSINESS_LOGIC,
        ErrorSeverity.HIGH,
        { ...context, businessRuleErrors: errors, data }
      )
      
      return { isValid: false, errors, appError }
    }
    
    return { isValid: true, errors: [] }
  }
}

// Global error handler for unhandled errors
export const setupGlobalErrorHandling = () => {
  // Handle unhandled promise rejections
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      const error = new AppError(
        `Unhandled promise rejection: ${event.reason}`,
        ErrorType.SYSTEM,
        ErrorSeverity.HIGH,
        { reason: event.reason }
      )
      
      ErrorLogger.logError(error)
      event.preventDefault()
    })

    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      const error = new AppError(
        `Uncaught error: ${event.message}`,
        ErrorType.SYSTEM,
        ErrorSeverity.HIGH,
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        }
      )
      
      ErrorLogger.logError(error)
    })
  }
}

// Error handling utilities are exported above as individual classes
