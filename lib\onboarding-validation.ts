// Onboarding validation utilities

export interface OnboardingStatus {
  hasAcceptedTerms: boolean
  hasSelectedCountry: boolean
  hasCompletedKYC: boolean
  kycStatus?: 'pending' | 'approved' | 'rejected' | 'incomplete'
  completionPercentage: number
  nextStep: 'terms' | 'country' | 'kyc' | 'complete'
  canAccessDashboard: boolean
}

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Terms validation
export function validateTermsAcceptance(accepted: boolean): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!accepted) {
    errors.push('You must accept the terms and conditions to continue')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Country validation
export function validateCountrySelection(countryCode: string): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!countryCode || countryCode.trim() === '') {
    errors.push('Please select your country of residence')
  }

  // Check if country code is valid (basic validation)
  if (countryCode && countryCode.length !== 2) {
    errors.push('Invalid country code format')
  }

  // Add warnings for restricted countries (if any)
  const restrictedCountries = ['XX'] // Add actual restricted country codes
  if (restrictedCountries.includes(countryCode)) {
    warnings.push('This country may have limited payment options available')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// KYC form validation
export interface KYCFormData {
  firstName: string
  lastName: string
  idType: string
  idNumber: string
  phoneNumber: string
  emailAddress: string
  address: string
  city: string
  postalCode: string
  acceptedPrivacy: boolean
}

export function validateKYCForm(formData: KYCFormData): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Required field validation
  if (!formData.firstName?.trim()) {
    errors.push('First name is required')
  }

  if (!formData.lastName?.trim()) {
    errors.push('Last name is required')
  }

  if (!formData.idType) {
    errors.push('ID type is required')
  }

  if (!formData.idNumber?.trim()) {
    errors.push('ID number is required')
  }

  if (!formData.phoneNumber?.trim()) {
    errors.push('Phone number is required')
  }

  if (!formData.emailAddress?.trim()) {
    errors.push('Email address is required')
  }

  if (!formData.address?.trim()) {
    errors.push('Address is required')
  }

  if (!formData.city?.trim()) {
    errors.push('City is required')
  }

  if (!formData.postalCode?.trim()) {
    errors.push('Postal code is required')
  }

  if (!formData.acceptedPrivacy) {
    errors.push('You must accept the privacy policy to continue')
  }

  // Format validation
  if (formData.emailAddress && !isValidEmail(formData.emailAddress)) {
    errors.push('Please enter a valid email address')
  }

  if (formData.phoneNumber && !isValidPhoneNumber(formData.phoneNumber)) {
    errors.push('Please enter a valid phone number')
  }

  // Length validation
  if (formData.firstName && formData.firstName.length < 2) {
    errors.push('First name must be at least 2 characters long')
  }

  if (formData.lastName && formData.lastName.length < 2) {
    errors.push('Last name must be at least 2 characters long')
  }

  if (formData.idNumber && formData.idNumber.length < 5) {
    errors.push('ID number appears to be too short')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Phone number validation (basic)
function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

// Calculate onboarding status
export function calculateOnboardingStatus(
  hasAcceptedTerms: boolean,
  hasSelectedCountry: boolean,
  hasCompletedKYC: boolean,
  kycStatus?: string
): OnboardingStatus {
  const steps = [hasAcceptedTerms, hasSelectedCountry, hasCompletedKYC]
  const completedSteps = steps.filter(Boolean).length
  const totalSteps = steps.length
  const completionPercentage = Math.round((completedSteps / totalSteps) * 100)

  let nextStep: OnboardingStatus['nextStep'] = 'complete'
  let canAccessDashboard = false

  if (!hasAcceptedTerms) {
    nextStep = 'terms'
  } else if (!hasSelectedCountry) {
    nextStep = 'country'
  } else if (!hasCompletedKYC) {
    nextStep = 'kyc'
  } else {
    nextStep = 'complete'
    canAccessDashboard = true
  }

  return {
    hasAcceptedTerms,
    hasSelectedCountry,
    hasCompletedKYC,
    kycStatus: kycStatus as OnboardingStatus['kycStatus'],
    completionPercentage,
    nextStep,
    canAccessDashboard
  }
}

// Get onboarding step URL
export function getOnboardingStepUrl(step: OnboardingStatus['nextStep']): string {
  const stepUrls = {
    terms: '/onboarding/terms',
    country: '/onboarding/country',
    kyc: '/onboarding/kyc',
    complete: '/onboarding/complete'
  }

  return stepUrls[step] || '/onboarding/terms'
}

// Check if user can skip to dashboard
export function canSkipToDashboard(status: OnboardingStatus): boolean {
  return status.hasAcceptedTerms && status.hasSelectedCountry
}

// Get step progress information
export function getStepProgress(currentStep: string, status: OnboardingStatus) {
  const steps = [
    { id: 'terms', completed: status.hasAcceptedTerms },
    { id: 'country', completed: status.hasSelectedCountry },
    { id: 'kyc', completed: status.hasCompletedKYC }
  ]

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const completedSteps = steps.filter(step => step.completed).length

  return {
    currentStepIndex,
    completedSteps,
    totalSteps: steps.length,
    isLastStep: currentStepIndex === steps.length - 1,
    canGoNext: currentStepIndex < steps.length - 1,
    canGoBack: currentStepIndex > 0
  }
}

// Validate onboarding completion
export function validateOnboardingCompletion(status: OnboardingStatus): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!status.hasAcceptedTerms) {
    errors.push('Terms and conditions must be accepted')
  }

  if (!status.hasSelectedCountry) {
    errors.push('Country selection is required')
  }

  if (!status.hasCompletedKYC) {
    errors.push('KYC verification must be completed')
  }

  if (status.kycStatus === 'rejected') {
    errors.push('KYC verification was rejected. Please contact support.')
  }

  if (status.kycStatus === 'pending') {
    warnings.push('KYC verification is still under review')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Get user-friendly step names
export function getStepDisplayName(step: string): string {
  const stepNames = {
    terms: 'Terms & Conditions',
    country: 'Country Selection',
    kyc: 'KYC Verification',
    complete: 'Setup Complete'
  }

  return stepNames[step as keyof typeof stepNames] || 'Unknown Step'
}

// Check if enhanced KYC is required based on country
export function requiresEnhancedKYC(countryCode: string): boolean {
  // Countries that require enhanced KYC (add more as needed)
  const enhancedKYCCountries = ['US', 'GB', 'DE', 'FR', 'AU', 'CA']
  return enhancedKYCCountries.includes(countryCode)
}

// Get country-specific requirements
export function getCountryRequirements(countryCode: string) {
  const requirements = {
    enhancedKYC: requiresEnhancedKYC(countryCode),
    additionalDocuments: [] as string[],
    paymentMethods: [] as string[],
    restrictions: [] as string[]
  }

  // Add country-specific logic here
  switch (countryCode) {
    case 'ZA':
      requirements.paymentMethods = ['bank_transfer', 'usdt']
      break
    case 'US':
      requirements.enhancedKYC = true
      requirements.additionalDocuments = ['proof_of_address', 'tax_id']
      requirements.paymentMethods = ['usdt']
      break
    default:
      requirements.paymentMethods = ['usdt']
  }

  return requirements
}
