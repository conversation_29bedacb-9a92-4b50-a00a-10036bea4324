// Share purchase calculation utilities

export interface InvestmentPhase {
  id: string
  phase_number: number
  phase_name: string
  price_per_share: number
  shares_available: number
  shares_sold: number
  is_active: boolean
  start_date: string
  end_date?: string
  created_at: string
  updated_at: string
}

export interface ShareCalculation {
  amount: number
  sharePrice: number
  sharesAmount: number
  totalCost: number
  remainingAmount: number
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface PaymentMethod {
  id: string
  name: string
  currency: string
  network?: string
  minAmount: number
  maxAmount: number
  processingTime: string
  fees: number
  isActive: boolean
}

// Calculate shares for a given amount
export function calculateShares(
  amount: number, 
  sharePrice: number, 
  allowPartial: boolean = false
): ShareCalculation {
  const errors: string[] = []
  const warnings: string[] = []

  // Validate inputs
  if (!amount || amount <= 0) {
    errors.push('Amount must be greater than 0')
  }

  if (!sharePrice || sharePrice <= 0) {
    errors.push('Share price must be greater than 0')
  }

  if (amount < 25) {
    errors.push('Minimum investment amount is $25')
  }

  if (amount > 10000) {
    warnings.push('Large investment amount - please verify')
  }

  // Calculate shares
  const exactShares = amount / sharePrice
  const sharesAmount = allowPartial ? exactShares : Math.floor(exactShares)
  const totalCost = sharesAmount * sharePrice
  const remainingAmount = amount - totalCost

  // Validation warnings
  if (remainingAmount > 0 && !allowPartial) {
    warnings.push(`$${remainingAmount.toFixed(2)} will be refunded as it doesn't purchase a full share`)
  }

  if (sharesAmount === 0) {
    errors.push(`Amount too small to purchase shares at $${sharePrice} per share`)
  }

  return {
    amount,
    sharePrice,
    sharesAmount,
    totalCost,
    remainingAmount,
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Get package information based on amount
export function getPackageInfo(amount: number) {
  const packages = [
    { name: 'Shovel', minAmount: 25, maxAmount: 74, shares: 25, roi: 12, description: 'Entry-level mining package' },
    { name: 'Miner', minAmount: 75, maxAmount: 249, shares: 75, roi: 15, description: 'Small-scale mining operation' },
    { name: 'Excavator', minAmount: 250, maxAmount: 499, shares: 250, roi: 18, description: 'Medium-scale mining operation' },
    { name: 'Crusher', minAmount: 500, maxAmount: 749, shares: 500, roi: 20, description: 'Large-scale mining operation' },
    { name: 'Refinery', minAmount: 750, maxAmount: 999, shares: 750, roi: 22, description: 'Industrial mining operation' },
    { name: 'Aureus', minAmount: 1000, maxAmount: 2499, shares: 1000, roi: 25, description: 'Premium mining package' },
    { name: 'Titan', minAmount: 2500, maxAmount: 4999, shares: 2500, roi: 28, description: 'Elite mining operation' },
    { name: 'Empire', minAmount: 5000, maxAmount: 10000, shares: 5000, roi: 30, description: 'Ultimate mining empire' }
  ]

  const matchingPackage = packages.find(pkg => amount >= pkg.minAmount && amount <= pkg.maxAmount)
  
  if (matchingPackage) {
    return matchingPackage
  }

  // Custom package for amounts outside predefined ranges
  return {
    name: 'Custom Package',
    minAmount: amount,
    maxAmount: amount,
    shares: Math.floor(amount), // 1 share per dollar as fallback
    roi: 20, // Default 20% ROI
    description: 'Custom investment package'
  }
}

// Validate phase availability
export function validatePhaseAvailability(
  requestedShares: number, 
  phase: InvestmentPhase
): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = []
  const warnings: string[] = []

  if (!phase.is_active) {
    errors.push('Investment phase is not currently active')
  }

  const remainingShares = phase.shares_available - phase.shares_sold
  
  if (requestedShares > remainingShares) {
    errors.push(`Insufficient shares available. Requested: ${requestedShares}, Available: ${remainingShares}`)
  }

  if (remainingShares < 100) {
    warnings.push('Phase is nearly sold out - limited shares remaining')
  }

  if (phase.end_date && new Date() > new Date(phase.end_date)) {
    errors.push('Investment phase has ended')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Calculate commission for referrals
export function calculateCommission(
  amount: number, 
  shares: number, 
  commissionRate: number = 15
): { usdtCommission: number; shareCommission: number } {
  const usdtCommission = amount * (commissionRate / 100)
  const shareCommission = shares * (commissionRate / 100)

  return {
    usdtCommission: Math.round(usdtCommission * 100) / 100, // Round to 2 decimal places
    shareCommission: Math.round(shareCommission * 100) / 100
  }
}

// Get available payment methods based on country
export function getAvailablePaymentMethods(countryCode: string): PaymentMethod[] {
  const allMethods: PaymentMethod[] = [
    {
      id: 'usdt_eth',
      name: 'USDT (Ethereum)',
      currency: 'USDT',
      network: 'ETH',
      minAmount: 25,
      maxAmount: 10000,
      processingTime: '1-2 hours',
      fees: 0,
      isActive: true
    },
    {
      id: 'usdt_bsc',
      name: 'USDT (BSC)',
      currency: 'USDT',
      network: 'BSC',
      minAmount: 25,
      maxAmount: 10000,
      processingTime: '30 minutes',
      fees: 0,
      isActive: true
    },
    {
      id: 'usdt_polygon',
      name: 'USDT (Polygon)',
      currency: 'USDT',
      network: 'POLYGON',
      minAmount: 25,
      maxAmount: 10000,
      processingTime: '15 minutes',
      fees: 0,
      isActive: true
    },
    {
      id: 'usdt_tron',
      name: 'USDT (TRON)',
      currency: 'USDT',
      network: 'TRON',
      minAmount: 25,
      maxAmount: 10000,
      processingTime: '10 minutes',
      fees: 0,
      isActive: true
    }
  ]

  // Add ZAR bank transfer for South African users
  if (countryCode === 'ZA') {
    allMethods.push({
      id: 'zar_bank',
      name: 'ZAR Bank Transfer',
      currency: 'ZAR',
      minAmount: 25,
      maxAmount: 10000,
      processingTime: '1-3 business days',
      fees: 0,
      isActive: true
    })
  }

  return allMethods.filter(method => method.isActive)
}

// Convert USD to other currencies (simplified - use real exchange rates in production)
export function convertCurrency(
  amount: number, 
  fromCurrency: string, 
  toCurrency: string
): number {
  const exchangeRates: { [key: string]: number } = {
    'USD_ZAR': 18.5, // Approximate USD to ZAR rate
    'ZAR_USD': 1 / 18.5,
    'USD_USD': 1,
    'ZAR_ZAR': 1
  }

  const rateKey = `${fromCurrency}_${toCurrency}`
  const rate = exchangeRates[rateKey] || 1

  return Math.round(amount * rate * 100) / 100
}

// Validate purchase limits
export function validatePurchaseLimits(
  amount: number, 
  userId: string, 
  existingPurchases: number = 0
): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = []
  const warnings: string[] = []

  // Minimum purchase validation
  if (amount < 25) {
    errors.push('Minimum purchase amount is $25')
  }

  // Maximum purchase validation
  if (amount > 10000) {
    errors.push('Maximum purchase amount is $10,000 per transaction')
  }

  // Total investment limits (example - adjust as needed)
  const totalInvestment = existingPurchases + amount
  if (totalInvestment > 50000) {
    warnings.push('Large total investment amount - may require additional verification')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Generate purchase summary
export function generatePurchaseSummary(
  amount: number,
  phase: InvestmentPhase,
  paymentMethod: PaymentMethod,
  packageInfo?: any
) {
  const calculation = calculateShares(amount, phase.price_per_share)
  const commission = calculateCommission(amount, calculation.sharesAmount)

  return {
    investment: {
      amount: calculation.totalCost,
      shares: calculation.sharesAmount,
      pricePerShare: phase.price_per_share,
      phase: phase.phase_name,
      package: packageInfo || getPackageInfo(amount)
    },
    payment: {
      method: paymentMethod.name,
      currency: paymentMethod.currency,
      network: paymentMethod.network,
      processingTime: paymentMethod.processingTime,
      fees: paymentMethod.fees
    },
    commission: {
      usdtAmount: commission.usdtCommission,
      shareAmount: commission.shareCommission,
      rate: 15
    },
    refund: {
      amount: calculation.remainingAmount,
      reason: calculation.remainingAmount > 0 ? 'Partial share amount' : null
    },
    validation: {
      isValid: calculation.isValid,
      errors: calculation.errors,
      warnings: calculation.warnings
    }
  }
}

// Format currency display
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  const formatters: { [key: string]: Intl.NumberFormat } = {
    USD: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }),
    ZAR: new Intl.NumberFormat('en-ZA', { style: 'currency', currency: 'ZAR' }),
    USDT: new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', currencyDisplay: 'code' })
  }

  const formatter = formatters[currency] || formatters.USD
  return formatter.format(amount)
}

// Calculate ROI projections
export function calculateROIProjections(
  shares: number, 
  packageROI: number, 
  years: number = 5
) {
  const projections = []
  
  for (let year = 1; year <= years; year++) {
    const annualReturn = shares * (packageROI / 100)
    const cumulativeReturn = annualReturn * year
    
    projections.push({
      year,
      annualReturn,
      cumulativeReturn,
      totalValue: shares + cumulativeReturn,
      roi: (cumulativeReturn / shares) * 100
    })
  }

  return projections
}
