import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
})

// Database types matching the bot's schema
export interface TelegramUser {
  id: number
  telegram_id: bigint
  username?: string
  first_name: string
  last_name?: string
  country?: string
  registration_mode: string
  referral_code?: string
  created_at: string
  updated_at: string
}

export interface SharePurchase {
  id: string
  user_id: number
  package_name: string
  shares_purchased: number
  total_amount: number
  commission_used: number
  remaining_payment: number
  payment_method: string
  status: string
  created_at: string
  updated_at: string
}

export interface CryptoPaymentTransaction {
  id: string
  user_id: number
  amount: number
  currency: string
  network: string
  sender_wallet: string
  receiver_wallet: string
  transaction_hash?: string
  screenshot_url?: string
  status: string
  admin_notes?: string
  created_at: string
  updated_at: string
}

export interface CommissionBalance {
  id: string
  user_id: number
  usdt_balance: number
  shares_balance: number
  total_earned_usdt: number
  total_earned_shares: number
  last_updated: string
}

export interface Referral {
  id: string
  sponsor_id: number
  referred_id: number
  referral_code: string
  status: string
  created_at: string
}

export interface InvestmentPhase {
  id: number
  phase_number: number
  price_per_share: number
  shares_available: number
  shares_sold: number
  is_active: boolean
  start_date: string
  end_date?: string
}

// Helper functions for database operations
export const getUserByTelegramId = async (telegramId: string) => {
  const { data, error } = await supabase
    .from('telegram_users')
    .select('*')
    .eq('telegram_id', telegramId)
    .single()
    
  if (error) {
    console.error('Error fetching user:', error)
    return null
  }
  
  return data
}

export const getUserShares = async (userId: number) => {
  const { data, error } = await supabase
    .from('aureus_share_purchases')
    .select('*')
    .eq('user_id', userId)
    .eq('status', 'active')
    
  if (error) {
    console.error('Error fetching user shares:', error)
    return []
  }
  
  return data || []
}

export const getUserPayments = async (userId: number) => {
  const { data, error } = await supabase
    .from('crypto_payment_transactions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    
  if (error) {
    console.error('Error fetching user payments:', error)
    return []
  }
  
  return data || []
}

export const getUserReferrals = async (userId: number) => {
  const { data, error } = await supabase
    .from('referrals')
    .select(`
      *,
      referred_user:telegram_users!referrals_referred_id_fkey(
        first_name,
        last_name,
        username,
        created_at
      )
    `)
    .eq('sponsor_id', userId)
    
  if (error) {
    console.error('Error fetching user referrals:', error)
    return []
  }
  
  return data || []
}

export const getCommissionBalance = async (userId: number) => {
  const { data, error } = await supabase
    .from('commission_balances')
    .select('*')
    .eq('user_id', userId)
    .single()
    
  if (error) {
    console.error('Error fetching commission balance:', error)
    return null
  }
  
  return data
}

export const getCurrentPhase = async () => {
  const { data, error } = await supabase
    .from('investment_phases')
    .select('*')
    .eq('is_active', true)
    .single()
    
  if (error) {
    console.error('Error fetching current phase:', error)
    return null
  }
  
  return data
}
