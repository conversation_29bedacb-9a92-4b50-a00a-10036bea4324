import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()

  // Create Supabase client for middleware
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  // Get session from cookies
  const accessToken = req.cookies.get('sb-access-token')?.value
  const refreshToken = req.cookies.get('sb-refresh-token')?.value

  let session = null
  if (accessToken) {
    try {
      const { data: { user } } = await supabase.auth.getUser(accessToken)
      if (user) {
        session = { user, access_token: accessToken }
      }
    } catch (error) {
      // Token might be expired, try to refresh if we have refresh token
      if (refreshToken) {
        try {
          const { data: { session: newSession } } = await supabase.auth.refreshSession({
            refresh_token: refreshToken
          })
          if (newSession) {
            session = newSession
            // Update cookies with new tokens
            res.cookies.set('sb-access-token', newSession.access_token, {
              path: '/',
              maxAge: 60 * 60 * 24 * 7, // 7 days
              sameSite: 'lax',
              secure: process.env.NODE_ENV === 'production'
            })
            if (newSession.refresh_token) {
              res.cookies.set('sb-refresh-token', newSession.refresh_token, {
                path: '/',
                maxAge: 60 * 60 * 24 * 30, // 30 days
                sameSite: 'lax',
                secure: process.env.NODE_ENV === 'production'
              })
            }
          }
        } catch (refreshError) {
          // Refresh failed, clear cookies
          res.cookies.delete('sb-access-token')
          res.cookies.delete('sb-refresh-token')
        }
      }
    }
  }

  const { pathname } = req.nextUrl

  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/login',
    '/api/auth/telegram-callback',
    '/api/health',
    '/_next',
    '/favicon.ico',
    '/logo.png',
    '/images',
    '/static'
  ]

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route)
  )

  // If it's a public route, allow access
  if (isPublicRoute) {
    return res
  }

  // If no session and trying to access protected route, redirect to login
  if (!session) {
    const redirectUrl = new URL('/login', req.url)
    redirectUrl.searchParams.set('redirectTo', pathname)
    return NextResponse.redirect(redirectUrl)
  }

  // Get user profile for additional checks
  const { data: telegramUser } = await supabase
    .from('telegram_users')
    .select('*')
    .eq('telegram_id', session.user.user_metadata?.telegram_id)
    .single()

  // Onboarding flow checks
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/onboarding')) {
    // Check if user needs to complete onboarding steps
    if (telegramUser) {
      // Check terms acceptance
      const { data: termsAcceptance } = await supabase
        .from('terms_acceptance')
        .select('*')
        .eq('user_id', telegramUser.id)
        .single()

      // If accessing dashboard but haven't accepted terms, redirect to terms
      if (pathname.startsWith('/dashboard') && !termsAcceptance) {
        return NextResponse.redirect(new URL('/onboarding/terms', req.url))
      }

      // Check country selection
      if (pathname.startsWith('/dashboard') && !telegramUser.country) {
        return NextResponse.redirect(new URL('/onboarding/country', req.url))
      }

      // If trying to access onboarding steps that are already completed
      if (pathname === '/onboarding/terms' && termsAcceptance) {
        if (!telegramUser.country) {
          return NextResponse.redirect(new URL('/onboarding/country', req.url))
        } else {
          return NextResponse.redirect(new URL('/dashboard', req.url))
        }
      }

      if (pathname === '/onboarding/country' && telegramUser.country) {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    }
  }

  // Admin route protection
  if (pathname.startsWith('/admin')) {
    // Check if user has admin privileges
    const { data: adminUser } = await supabase
      .from('admin_users')
      .select('*')
      .eq('telegram_id', session.user.user_metadata?.telegram_id)
      .eq('is_active', true)
      .single()

    if (!adminUser) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|logo.png|images|static).*)',
  ],
}
