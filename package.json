{"name": "aureus-alliance-holdings", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vite-dev": "vite", "vite-build": "vite build", "vite-preview": "vite preview --host 0.0.0.0 --port 8000", "validate:css": "node scripts/validate-css-classes.js", "validate:all": "npm run validate:css && npm run lint", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:integration": "vitest run --config vitest.integration.config.ts", "test:unit": "vitest run --config vitest.unit.config.ts", "test:security": "vitest run tests/security", "test:accessibility": "vitest run tests/accessibility", "test:performance": "vitest run tests/performance", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "qa:audit": "npm audit && npm run lint && npm run test:coverage", "qa:security": "npm audit --audit-level moderate && npm run test:security", "qa:performance": "npm run test:performance && npm run build", "qa:accessibility": "npm run test:accessibility", "qa:full": "npm run qa:audit && npm run qa:security && npm run qa:performance && npm run qa:accessibility"}, "dependencies": {"@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.50.3", "clsx": "^2.0.0", "dotenv": "^17.1.0", "next": "^14.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "serve": "^14.2.4", "tailwindcss": "^4.1.11", "typescript": "~5.7.2", "vite": "^6.2.0", "@testing-library/react": "^16.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "@playwright/test": "^1.49.1", "axe-core": "^4.10.2", "jsdom": "^26.0.0", "lighthouse": "^12.2.1", "msw": "^2.6.8", "vitest": "^3.2.4", "vitest-axe": "^1.0.0"}}