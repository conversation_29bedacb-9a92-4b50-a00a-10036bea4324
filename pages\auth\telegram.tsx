import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function TelegramAuth() {
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Authenticating...');
  const [userInfo, setUserInfo] = useState<any>(null);

  useEffect(() => {
    const authenticateUser = async () => {
      try {
        const { token, user_id } = router.query;

        if (!token || !user_id) {
          setStatus('error');
          setMessage('Invalid authentication link. Please try again from Telegram.');
          return;
        }

        console.log('🔐 Processing Telegram authentication...', { token, user_id });

        // Validate token format
        const tokenParts = (token as string).split('_');
        if (tokenParts.length !== 3 || tokenParts[0] !== user_id) {
          setStatus('error');
          setMessage('Invalid authentication token. Please try again.');
          return;
        }

        const timestamp = parseInt(tokenParts[1]);
        const currentTime = Date.now();
        const tokenAge = currentTime - timestamp;

        // Check if token is expired (10 minutes = 600,000 ms)
        if (tokenAge > 600000) {
          setStatus('error');
          setMessage('Authentication link has expired. Please request a new one from Telegram.');
          return;
        }

        // Find user in database
        const { data: telegramUser, error: findError } = await supabase
          .from('telegram_users')
          .select('*')
          .eq('telegram_id', user_id)
          .single();

        if (findError) {
          console.error('Database error:', findError);
          setStatus('error');
          setMessage('User not found. Please start the registration process in Telegram first.');
          return;
        }

        setUserInfo(telegramUser);

        // Create or sign in Supabase auth user
        const email = `telegram_${user_id}@aureus.africa`;
        const password = `telegram_${user_id}_auth_${Date.now()}`;

        // Try to sign in first
        let authResult = await supabase.auth.signInWithPassword({
          email,
          password: `telegram_${user_id}_auth`
        });

        if (authResult.error) {
          // If sign in fails, create new auth user
          authResult = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: {
                telegram_id: telegramUser.telegram_id,
                telegram_username: telegramUser.username,
                telegram_first_name: telegramUser.first_name,
                telegram_last_name: telegramUser.last_name,
                is_telegram_user: true
              }
            }
          });

          if (authResult.error) {
            console.error('Auth error:', authResult.error);
            setStatus('error');
            setMessage('Authentication failed. Please try again.');
            return;
          }
        }

        // Update user's last login
        await supabase
          .from('telegram_users')
          .update({
            last_login: new Date().toISOString(),
            web_authenticated: true
          })
          .eq('telegram_id', user_id);

        setStatus('success');
        setMessage('Authentication successful! Redirecting to dashboard...');

        // Redirect based on user status
        setTimeout(() => {
          if (telegramUser.is_registered) {
            router.push('/dashboard');
          } else {
            router.push('/onboarding');
          }
        }, 2000);

      } catch (error) {
        console.error('Authentication error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    if (router.isReady) {
      authenticateUser();
    }
  }, [router.isReady, router.query]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white flex items-center justify-center p-4">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '1s'}}></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        <div className="bg-gray-900/50 backdrop-blur-xl rounded-3xl p-8 border border-gray-700/30 shadow-2xl text-center">
          {/* Logo */}
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-6 shadow-2xl">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo.png?v=2"
              alt="Aureus Alliance Holdings Logo"
              className="w-12 h-12 object-contain"
            />
          </div>

          <h1 className="text-2xl font-bold text-white mb-6">
            Telegram Authentication
          </h1>

          {/* Status Display */}
          {status === 'loading' && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
              </div>
              <p className="text-gray-300">{message}</p>
            </div>
          )}

          {status === 'success' && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <svg className="w-12 h-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-green-400 font-semibold">{message}</p>
              {userInfo && (
                <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mt-4">
                  <p className="text-sm text-green-300">
                    Welcome, {userInfo.first_name} {userInfo.last_name || ''}!
                  </p>
                  <p className="text-xs text-green-400 mt-1">
                    @{userInfo.username || 'No username'}
                  </p>
                </div>
              )}
            </div>
          )}

          {status === 'error' && (
            <div className="space-y-4">
              <div className="flex justify-center">
                <svg className="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <p className="text-red-400 font-semibold">{message}</p>
              <div className="space-y-2">
                <button
                  onClick={() => router.push('/')}
                  className="w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                >
                  Return to Homepage
                </button>
                <a
                  href="https://t.me/AureusAllianceBot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 text-center"
                >
                  Try Again in Telegram
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
