-- Gallery Tables Setup Script for Supabase
-- Run this in your Supabase SQL Editor to create the required tables

-- Create gallery_categories table
CREATE TABLE IF NOT EXISTS gallery_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by VARCHAR(255) DEFAULT 'system'
);

-- Create gallery_images table
CREATE TABLE IF NOT EXISTS gallery_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    alt_text VARCHAR(255),
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    category_id UUID REFERENCES gallery_categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by VARCHAR(255) DEFAULT 'system'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_gallery_images_category_id ON gallery_images(category_id);
CREATE INDEX IF NOT EXISTS idx_gallery_images_is_active ON gallery_images(is_active);
CREATE INDEX IF NOT EXISTS idx_gallery_images_is_featured ON gallery_images(is_featured);
CREATE INDEX IF NOT EXISTS idx_gallery_images_display_order ON gallery_images(display_order);
CREATE INDEX IF NOT EXISTS idx_gallery_categories_is_active ON gallery_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_gallery_categories_display_order ON gallery_categories(display_order);

-- Insert default categories
INSERT INTO gallery_categories (id, name, description, display_order) VALUES
    ('mining-ops', 'Mining Operations', 'On-site mining activities and operations', 1),
    ('equipment', 'Equipment', 'Mining equipment and machinery', 2),
    ('locations', 'Locations', 'Mining site locations and terrain', 3),
    ('team', 'Team', 'Our professional mining team', 4),
    ('products', 'Products', 'Final gold products and results', 5)
ON CONFLICT (id) DO NOTHING;

-- Insert sample gallery images using the static URLs
INSERT INTO gallery_images (title, description, image_url, alt_text, is_featured, category_id, display_order) VALUES
    ('Mining Operations Overview', 'Comprehensive view of our mining operations and infrastructure', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-1.jpg', 'Mining operations overview', true, 'mining-ops', 1),
    ('Equipment and Machinery', 'State-of-the-art mining equipment in action', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-2.jpg', 'Mining equipment and machinery', true, 'equipment', 2),
    ('Site Location', 'Strategic mining site location and terrain', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-3.jpg', 'Mining site location', true, 'locations', 3),
    ('Team Collaboration', 'Our professional team working together on-site', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-4.jpg', 'Team collaboration on-site', true, 'team', 4),
    ('Processing Facility', 'Advanced gold processing and refinement facility', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-5.jpg', 'Gold processing facility', true, 'mining-ops', 5),
    ('Extraction Process', 'Gold extraction and processing in progress', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-6.jpg', 'Gold extraction process', true, 'mining-ops', 6),
    ('Quality Control', 'Rigorous quality control and testing procedures', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-7.jpg', 'Quality control procedures', true, 'mining-ops', 7),
    ('Safety Standards', 'Maintaining highest safety standards in all operations', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-8.jpg', 'Safety standards implementation', true, 'team', 8),
    ('Final Product', 'Refined gold ready for market distribution', 'https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/gallery-9.jpg', 'Refined gold final product', true, 'products', 9)
ON CONFLICT DO NOTHING;

-- Enable Row Level Security (RLS)
ALTER TABLE gallery_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE gallery_images ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY IF NOT EXISTS "Allow public read access to active categories" ON gallery_categories
    FOR SELECT USING (is_active = true);

CREATE POLICY IF NOT EXISTS "Allow public read access to active images" ON gallery_images
    FOR SELECT USING (is_active = true);

-- Create policies for authenticated users (admin access)
CREATE POLICY IF NOT EXISTS "Allow authenticated users full access to categories" ON gallery_categories
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY IF NOT EXISTS "Allow authenticated users full access to images" ON gallery_images
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER IF NOT EXISTS update_gallery_categories_updated_at 
    BEFORE UPDATE ON gallery_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_gallery_images_updated_at 
    BEFORE UPDATE ON gallery_images 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT ON gallery_categories TO anon, authenticated;
GRANT SELECT ON gallery_images TO anon, authenticated;
GRANT ALL ON gallery_categories TO authenticated;
GRANT ALL ON gallery_images TO authenticated;
