# 🛠️ AUREUS ALLIANCE WEB DASHBOARD - DEVELOPMENT TASKS

## PROJECT OVERVIEW
Creating a web dashboard that mirrors the Telegram bot functionality using the same Supabase database. Users can authenticate via Telegram, purchase shares, upload KYC documents, and manage their referrals - all synchronized with the eisting bot.

## TECHNOLOGY STACK CONFIRMED
- **Database**: Eisting Supabase instance
- **Authentication**: Telegram Login Widget
- **Framework**: Net.js 13+
- **Styling**: Tailwind CSS
- **Deployment**: Vercel

---

## PHASE 1: PROJECT ANALYSIS & SETUP ✅

### Analysis Tasks
- [] **1.1** Eamine eisting bot code (`aureus_bot/aureus-bot-new.js`) for complete user flow
- [] **1.2** Analyze database schema (`database/CompleteTableColumns.json`)
- [] **1.3** Review eisting Supabase RLS policies and functions
- [] **1.4** Document all bot commands and their corresponding database operations
- [] **1.5** Map user journey from registration to share purchase completion

### Project Setup
- [] **1.6** Convert current React+Vite project to Net.js 13+ structure
- [] **1.7** Setup project folder structure for dashboard integration
- [] **1.8** Configure Supabase client for Net.js
- [] **1.9** Setup Tailwind CSS configuration
- [] **1.10** Configure TypeScript for Net.js environment
- [] **1.11** Setup environment variables for development and production

---

## PHASE 2: AUTHENTICATION SYSTEM 🔐

### Telegram Authentication
- [] **2.1** Implement Telegram Login Widget component
- [] **2.2** Create authentication contet and session management
- [] **2.3** Build login/signup flow matching bot's user registration
- [] **2.4** Handle eisting user lookup via `telegram_users` table
- [] **2.5** Create new user registration with proper table linking

### Session Management
- [] **2.6** Implement protected route wrapper for dashboard
- [] **2.7** Add logout functionality and session cleanup
- [] **2.8** Handle authentication state persistence
- [] **2.9** Implement session timeout and refresh logic
- [] **2.10** Add authentication error handling and user feedback

---

## PHASE 3: USER ONBOARDING FLOW 📋

### Country Selection & Terms
- [] **3.1** Create country selection component (validate against bot logic)
- [] **3.2** Build Terms & Conditions acceptance modal/page
- [] **3.3** Store country selection in eisting user fields
- [] **3.4** Save terms acceptance to `terms_acceptance` table
- [] **3.5** Implement onboarding progress tracking

### Flow Management
- [] **3.6** Add redirect logic based on completion status
- [] **3.7** Create onboarding progress indicator
- [] **3.8** Handle incomplete onboarding scenarios
- [] **3.9** Validate country-specific business rules
- [] **3.10** Implement onboarding step validation

---

## PHASE 4: SHARE PURCHASE SYSTEM 💰

### Phase Information Display
- [] **4.1** Display current share phase info (price, available shares)
- [] **4.2** Show phase progression and remaining shares
- [] **4.3** Calculate and display share purchase estimates
- [] **4.4** Implement phase transition notifications

### Payment Processing
- [] **4.5** Build payment method selection (USDT/ZAR based on country)
- [] **4.6** Create USDT crypto payment form with validation
- [] **4.7** Implement multi-network USDT support (ETH, BSC, Polygon, TRON)
- [] **4.8** Build ZAR bank transfer payment form
- [] **4.9** Save payments to `crypto_payment_transactions` table
- [] **4.10** Create payment status tracking and admin approval waiting
- [] **4.11** Display payment history for users
- [] **4.12** Implement payment proof upload functionality

---

## PHASE 5: KYC DOCUMENT SYSTEM 📄

### Document Upload
- [ ] **5.1** Build KYC document upload interface
- [ ] **5.2** Implement file validation (size, type, etc.)
- [ ] **5.3** Store KYC documents matching bot epectations
- [ ] **5.4** Save KYC data to user_kyc table structure
- [ ] **5.5** Create KYC status display (pending/approved/rejected)

### Document Management
- [ ] **5.6** Add document preview functionality
- [ ] **5.7** Handle KYC approval/rejection workflow
- [ ] **5.8** Implement document replacement functionality
- [ ] **5.9** Add KYC completion progress tracking
- [ ] **5.10** Create KYC reminder system for incomplete submissions

---

## PHASE 6: REFERRAL SYSTEM 🔗

### Referral Management
- [] **6.1** Generate and display user referral links
- [] **6.2** Implement referral tracking for new user registrations
- [] **6.3** Build referrals dashboard showing referred users
- [] **6.4** Display commission calculations from `commission_accounts`
- [] **6.5** Create commission withdrawal request system

### Commission Features
- [] **6.6** Add commission-to-shares conversion feature
- [] **6.7** Show referral earnings and statistics
- [] **6.8** Implement commission balance tracking
- [] **6.9** Create referral performance analytics
- [] **6.10** Add referral link sharing functionality

---

## PHASE 7: MAIN DASHBOARD INTERFACE 📊

### Dashboard Layout
- [ ] **7.1** Create responsive dashboard layout with navigation
- [ ] **7.2** Build main dashboard overview page
- [ ] **7.3** Implement sidebar navigation with active states
- [ ] **7.4** Create mobile-responsive navigation menu

### Core Dashboard Pages
- [ ] **7.5** Build "My Shares" page with complete purchase history
- [ ] **7.6** Implement "My Referrals" with detailed earnings tracking
- [ ] **7.7** Create "Available Commission" management page
- [ ] **7.8** Add withdrawal request interface and tracking
- [ ] **7.9** Build documents/resources section (mining videos, legal docs)
- [ ] **7.10** Implement user profile management
- [ ] **7.11** Create portfolio summary with charts and statistics

---

## PHASE 8: DATA SYNCHRONIZATION & VALIDATION ✅

### Database Operations
- [] **8.1** Ensure all database operations match bot logic eactly
- [] **8.2** Test data consistency between bot and web interface
- [] **8.3** Implement real-time updates using Supabase subscriptions
- [] **8.4** Add comprehensive error handling for database operations
- [] **8.5** Validate all business logic matches bot implementation

### Data Integrity
- [] **8.6** Test edge cases and data conflicts
- [] **8.7** Implement data validation middleware
- [] **8.8** Add database transaction rollback mechanisms
- [] **8.9** Create data consistency monitoring
- [] **8.10** Implement automated data integrity checks

---

## PHASE 9: UI/U IMPLEMENTATION 🎨

### Responsive Design
- [] **9.1** Create responsive design for mobile and desktop
- [] **9.2** Implement loading states and skeleton screens
- [] **9.3** Add comprehensive form validation and user feedback
- [] **9.4** Style components matching eisting design system
- [] **9.5** Optimize performance and implement lazy loading

### User Eperience
- [] **9.6** Add accessibility features and ARIA labels
- [] **9.7** Create error pages and 404 handling
- [] **9.8** Implement toast notifications and alerts
- [] **9.9** Add progress indicators for long operations
- [] **9.10** Create intuitive navigation and breadcrumbs
- [ ] **9.11** Implement dark/light theme support

---

## PHASE 10: TESTING & QUALITY ASSURANCE 🧪

### Functional Testing
- [ ] **10.1** Test Telegram authentication flow thoroughly
- [ ] **10.2** Validate complete user journey from signup to purchase
- [ ] **10.3** Test payment processing end-to-end
- [ ] **10.4** Verify referral system calculations and tracking
- [ ] **10.5** Cross-test functionality with eisting bot

### Quality Assurance
- [ ] **10.6** Perform security testing and data validation
- [ ] **10.7** Test responsive design on various devices
- [ ] **10.8** Validate database synchronization accuracy
- [ ] **10.9** Conduct performance testing and optimization
- [ ] **10.10** Test error handling and edge cases
- [ ] **10.11** Validate accessibility compliance

---

## IMMEDIATE PRIORITY TASKS 🚀

### Week 1 Focus
- [ ] **P1** Eamine bot code - Understand eact user flow and business logic
- [ ] **P2** Analyze database schema - Map all tables and relationships
- [ ] **P3** Setup Net.js structure - Convert from React+Vite to Net.js
- [ ] **P4** Implement Telegram auth - Core authentication system
- [ ] **P5** Build first dashboard page - Country selection flow

---

## CRITICAL SUCCESS FACTORS ⚠️

### Non-Negotiable Requirements
- ✅ **ZERO changes** to eisting bot code (`aureus_bot/aureus-bot-new.js`)
- ✅ **NO database schema modifications** - use eisting structure
- ✅ **Perfect data synchronization** between bot and web
- ✅ **Eact business logic replication** from bot
- ✅ **Homepage preservation** - no changes to eisting homepage
- ✅ **Same user eperience** - identical flows and validation

---

## FILES TO ANALYZE FIRST 📁

### Critical Files for Analysis
- [ ] `aureus_bot/aureus-bot-new.js` - Complete bot logic and user flow
- [ ] `database/CompleteTableColumns.json` - Database schema structure
- [ ] `lib/supabase.ts` - Eisting Supabase configuration
- [ ] `.env` - Environment variables and Supabase credentials
- [ ] `components/` - Eisting React components to migrate
- [ ] `contets/` - Current contet providers and state management

---

## PROGRESS TRACKING

### Overall Progress: 70% Complete

**Phase 1**: ✅ Complete (11/11 tasks)
**Phase 2**: ✅ Complete (10/10 tasks)
**Phase 3**: ✅ Complete (10/10 tasks)
**Phase 4**: ✅ Complete (12/12 tasks)
**Phase 5**: ⏳ Not Started
**Phase 6**: ✅ Complete (10/10 tasks)
**Phase 6**: ⏳ Not Started
**Phase 7**: ⏳ Not Started
**Phase 8**: ✅ Complete (10/10 tasks)
**Phase 9**: ✅ Complete (10/10 tasks)
**Phase 10**: ⏳ Not Started

---

## NOTES & CONSIDERATIONS 📝

### Technical Considerations
- Maintain eact parity with Telegram bot functionality
- Ensure real-time synchronization between platforms
- Preserve eisting database structure and relationships
- Implement proper error handling and user feedback
- Follow eisting design patterns and conventions

### Business Logic Preservation
- All commission calculations must match bot eactly
- Share purchase flow must be identical to bot
- KYC requirements must align with bot implementation
- Referral system must maintain same rules and calculations
- Payment processing must use same validation logic

### Security Requirements
- Implement proper authentication and authorization
- Validate all user inputs and database operations
- Ensure secure file upload and storage
- Maintain audit trails for all financial operations
- Follow eisting security patterns from bot implementation
