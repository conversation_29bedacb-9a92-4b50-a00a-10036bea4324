import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'
import { axe, toHaveNoViolations } from 'vitest-axe'
import { EnhancedButton } from '@/components/ui/EnhancedButton'
import { EnhancedCard } from '@/components/ui/EnhancedCard'
import { EnhancedInput } from '@/components/ui/EnhancedInput'
import Badge from '@/components/ui/Badge'

// Extend expect with axe matchers
expect.extend(toHaveNoViolations)

describe('Accessibility Tests', () => {
  describe('EnhancedButton Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <EnhancedButton>Accessible Button</EnhancedButton>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes when disabled', async () => {
      const { container } = render(
        <EnhancedButton disabled>Disabled But<PERSON></EnhancedButton>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes when loading', async () => {
      const { container } = render(
        <EnhancedButton loading>Loading Button</EnhancedButton>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should support keyboard navigation', async () => {
      const { container } = render(
        <div>
          <EnhancedButton>Button 1</EnhancedButton>
          <EnhancedButton>Button 2</EnhancedButton>
          <EnhancedButton>Button 3</EnhancedButton>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('EnhancedCard Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <EnhancedCard>
          <h2>Card Title</h2>
          <p>Card content goes here</p>
        </EnhancedCard>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes when interactive', async () => {
      const { container } = render(
        <EnhancedCard interactive onClick={() => {}}>
          <h2>Interactive Card</h2>
          <p>This card is clickable</p>
        </EnhancedCard>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should maintain accessibility during loading state', async () => {
      const { container } = render(
        <EnhancedCard loading>
          <h2>Loading Card</h2>
          <p>This content should be replaced by skeleton</p>
        </EnhancedCard>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('EnhancedInput Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <EnhancedInput
          label="Email Address"
          type="email"
          placeholder="Enter your email"
        />
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes for errors', async () => {
      const { container } = render(
        <EnhancedInput
          label="Password"
          type="password"
          error="Password is required"
          placeholder="Enter your password"
        />
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes for success state', async () => {
      const { container } = render(
        <EnhancedInput
          label="Username"
          type="text"
          success
          helperText="Username is available"
          placeholder="Enter username"
        />
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should support screen readers with icons', async () => {
      const { container } = render(
        <EnhancedInput
          label="Search"
          type="search"
          leftIcon={<span aria-hidden="true">🔍</span>}
          placeholder="Search for items"
        />
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Badge Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <Badge>Default Badge</Badge>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should have proper ARIA attributes for removable badges', async () => {
      const { container } = render(
        <Badge removable onRemove={() => {}}>
          Removable Badge
        </Badge>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })

    it('should support status indicators accessibly', async () => {
      const { container } = render(
        <div>
          <Badge variant="success" dot>Active</Badge>
          <Badge variant="warning" dot>Pending</Badge>
          <Badge variant="error" dot>Failed</Badge>
        </div>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Color Contrast', () => {
    it('should have sufficient color contrast for text', async () => {
      const { container } = render(
        <div>
          <h1 className="text-gray-900">Main Heading</h1>
          <p className="text-gray-700">Body text content</p>
          <small className="text-gray-600">Small text</small>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'color-contrast': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })

    it('should have sufficient contrast for interactive elements', async () => {
      const { container } = render(
        <div>
          <EnhancedButton variant="primary">Primary Button</EnhancedButton>
          <EnhancedButton variant="secondary">Secondary Button</EnhancedButton>
          <EnhancedButton variant="outline">Outline Button</EnhancedButton>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'color-contrast': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Focus Management', () => {
    it('should have visible focus indicators', async () => {
      const { container } = render(
        <div>
          <EnhancedButton>Focusable Button</EnhancedButton>
          <EnhancedInput label="Focusable Input" />
          <EnhancedCard interactive onClick={() => {}}>
            Focusable Card
          </EnhancedCard>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'focus-order-semantics': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Semantic HTML', () => {
    it('should use proper heading hierarchy', async () => {
      const { container } = render(
        <div>
          <h1>Main Page Title</h1>
          <section>
            <h2>Section Title</h2>
            <h3>Subsection Title</h3>
            <p>Content goes here</p>
          </section>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'heading-order': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })

    it('should use proper landmark roles', async () => {
      const { container } = render(
        <div>
          <header>
            <nav>
              <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/about">About</a></li>
              </ul>
            </nav>
          </header>
          <main>
            <h1>Main Content</h1>
            <p>Page content</p>
          </main>
          <footer>
            <p>Footer content</p>
          </footer>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'landmark-one-main': { enabled: true },
          'landmark-complementary-is-top-level': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Form Accessibility', () => {
    it('should have proper form labels and associations', async () => {
      const { container } = render(
        <form>
          <EnhancedInput
            label="First Name"
            type="text"
            required
            placeholder="Enter your first name"
          />
          <EnhancedInput
            label="Email"
            type="email"
            required
            placeholder="Enter your email"
          />
          <EnhancedButton type="submit">Submit Form</EnhancedButton>
        </form>
      )
      
      const results = await axe(container, {
        rules: {
          'label': { enabled: true },
          'label-title-only': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })

    it('should handle form validation accessibly', async () => {
      const { container } = render(
        <form>
          <EnhancedInput
            label="Password"
            type="password"
            required
            error="Password must be at least 8 characters"
            placeholder="Enter password"
          />
          <EnhancedInput
            label="Confirm Password"
            type="password"
            required
            error="Passwords do not match"
            placeholder="Confirm password"
          />
        </form>
      )
      
      const results = await axe(container)
      expect(results).toHaveNoViolations()
    })
  })

  describe('Images and Media', () => {
    it('should have proper alt text for images', async () => {
      const { container } = render(
        <div>
          <img src="/logo.png" alt="Aureus Alliance Holdings Logo" />
          <img src="/chart.png" alt="Portfolio performance chart showing 15% growth" />
          <img src="/decoration.png" alt="" role="presentation" />
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'image-alt': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })

  describe('Keyboard Navigation', () => {
    it('should support tab navigation', async () => {
      const { container } = render(
        <div>
          <EnhancedButton>Button 1</EnhancedButton>
          <EnhancedInput label="Input Field" />
          <EnhancedButton>Button 2</EnhancedButton>
          <a href="/link">Link</a>
        </div>
      )
      
      const results = await axe(container, {
        rules: {
          'tabindex': { enabled: true }
        }
      })
      expect(results).toHaveNoViolations()
    })
  })
})
