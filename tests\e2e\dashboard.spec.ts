import { test, expect } from '@playwright/test'

test.describe('Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authenticated state
    await page.addInitScript(() => {
      localStorage.setItem('sb-access-token', 'test-access-token')
      localStorage.setItem('user-profile', JSON.stringify({
        id: 'test-user-id',
        email: '<EMAIL>',
        telegram_profile: {
          id: 'test-profile-id',
          telegram_id: 123456789,
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User'
        }
      }))
    })
  })

  test('should load dashboard with user data', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-stats"]', { timeout: 10000 })
    
    // Check welcome message
    await expect(page.locator('h1')).toContainText('Welcome back, Test!')
    
    // Check dashboard statistics are visible
    await expect(page.locator('text=Total Shares')).toBeVisible()
    await expect(page.locator('text=Total Invested')).toBeVisible()
    await expect(page.locator('text=Commission Balance')).toBeVisible()
    await expect(page.locator('text=Current Phase')).toBeVisible()
  })

  test('should navigate to purchase shares page', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for dashboard to load
    await page.waitForSelector('text=Purchase Shares', { timeout: 10000 })
    
    // Click purchase shares button
    await page.click('text=Purchase Shares')
    
    // Should navigate to purchase page
    await expect(page).toHaveURL('/dashboard/purchase')
    
    // Check purchase page elements
    await expect(page.locator('h1')).toContainText('Purchase Gold Mining Shares')
  })

  test('should display portfolio overview correctly', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for portfolio section to load
    await page.waitForSelector('text=Portfolio Overview', { timeout: 10000 })
    
    // Check portfolio metrics
    await expect(page.locator('text=Portfolio Overview')).toBeVisible()
    
    // Should show portfolio value and performance
    const portfolioSection = page.locator('[data-testid="portfolio-overview"]')
    if (await portfolioSection.count() > 0) {
      await expect(portfolioSection).toBeVisible()
    }
  })

  test('should show recent activity', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for activity section to load
    await page.waitForSelector('text=Recent Activity', { timeout: 10000 })
    
    // Check recent activity section
    await expect(page.locator('text=Recent Activity')).toBeVisible()
    
    // Should show activity items or empty state
    const activitySection = page.locator('[data-testid="recent-activity"]')
    if (await activitySection.count() > 0) {
      await expect(activitySection).toBeVisible()
    }
  })

  test('should display quick actions', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for quick actions to load
    await page.waitForSelector('text=Quick Actions', { timeout: 10000 })
    
    // Check quick actions are visible
    await expect(page.locator('text=Quick Actions')).toBeVisible()
    
    // Check individual action buttons
    const actionButtons = [
      'Purchase Shares',
      'Payment History',
      'KYC Documents',
      'My Referrals'
    ]
    
    for (const buttonText of actionButtons) {
      const button = page.locator(`text=${buttonText}`)
      if (await button.count() > 0) {
        await expect(button).toBeVisible()
      }
    }
  })

  test('should show investment phase information', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for phase info to load
    await page.waitForSelector('text=Current Investment Phase', { timeout: 10000 })
    
    // Check phase information
    await expect(page.locator('text=Current Investment Phase')).toBeVisible()
    
    // Should show phase details
    const phaseSection = page.locator('[data-testid="investment-phase"]')
    if (await phaseSection.count() > 0) {
      await expect(phaseSection).toBeVisible()
    }
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/dashboard')
    
    // Wait for dashboard to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Check mobile layout
    await expect(page.locator('h1')).toBeVisible()
    
    // Check that elements stack vertically on mobile
    const statsGrid = page.locator('[data-testid="dashboard-stats"]')
    if (await statsGrid.count() > 0) {
      await expect(statsGrid).toBeVisible()
    }
    
    // Check mobile navigation if present
    const mobileMenu = page.locator('[data-testid="mobile-menu"]')
    if (await mobileMenu.count() > 0) {
      await expect(mobileMenu).toBeVisible()
    }
  })

  test('should handle loading states gracefully', async ({ page }) => {
    // Slow down network to test loading states
    await page.route('**/api/**', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      await route.continue()
    })
    
    await page.goto('/dashboard')
    
    // Should show loading indicators
    const loadingElements = page.locator('[data-testid*="loading"], .animate-pulse, .animate-spin')
    if (await loadingElements.count() > 0) {
      await expect(loadingElements.first()).toBeVisible()
    }
    
    // Eventually should show content
    await page.waitForSelector('h1', { timeout: 15000 })
    await expect(page.locator('h1')).toBeVisible()
  })

  test('should handle errors gracefully', async ({ page }) => {
    // Mock API errors
    await page.route('**/api/shares', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })
    
    await page.goto('/dashboard')
    
    // Should still load the page structure
    await page.waitForSelector('h1', { timeout: 10000 })
    await expect(page.locator('h1')).toBeVisible()
    
    // Should show error states or fallback content
    const errorElements = page.locator('text=Error, text=Failed, [data-testid*="error"]')
    if (await errorElements.count() > 0) {
      await expect(errorElements.first()).toBeVisible()
    }
  })

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for page to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Test tab navigation
    await page.keyboard.press('Tab')
    
    // Should focus on interactive elements
    const focusedElement = page.locator(':focus')
    if (await focusedElement.count() > 0) {
      await expect(focusedElement).toBeVisible()
    }
    
    // Test Enter key on buttons
    const firstButton = page.locator('button, a[role="button"]').first()
    if (await firstButton.count() > 0) {
      await firstButton.focus()
      // Note: We don't actually press Enter to avoid navigation in tests
      await expect(firstButton).toBeFocused()
    }
  })

  test('should maintain state across page refreshes', async ({ page }) => {
    await page.goto('/dashboard')
    
    // Wait for dashboard to load
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Get initial state
    const initialTitle = await page.locator('h1').textContent()
    
    // Refresh the page
    await page.reload()
    
    // Wait for page to load again
    await page.waitForSelector('h1', { timeout: 10000 })
    
    // Should maintain the same state
    const refreshedTitle = await page.locator('h1').textContent()
    expect(refreshedTitle).toBe(initialTitle)
  })
})
