import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for E2E tests...')
  
  // Launch browser for setup
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...')
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' })
    
    // Check if the application is responding
    await page.waitForSelector('body', { timeout: 30000 })
    console.log('✅ Application is ready')
    
    // Set up test data if needed
    await setupTestData(page)
    
    // Create authenticated state for tests that need it
    await setupAuthenticatedState(page)
    
  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
  
  console.log('✅ Global setup completed successfully')
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...')
  
  // Mock API responses for testing
  await page.route('**/api/**', (route: any) => {
    const url = route.request().url()
    
    // Mock authentication endpoints
    if (url.includes('/api/auth/')) {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: { telegram_id: 123456789 }
          }
        })
      })
    }
    
    // Mock shares endpoint
    if (url.includes('/api/shares')) {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'test-share-1',
            user_id: 'test-user-id',
            shares_purchased: 100,
            total_amount: '1000.00',
            package_name: 'Bronze Package',
            status: 'completed',
            created_at: new Date().toISOString()
          }
        ])
      })
    }
    
    // Mock payments endpoint
    if (url.includes('/api/payments')) {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'test-payment-1',
            user_id: 'test-user-id',
            amount: '1000.00',
            currency: 'USDT',
            status: 'approved',
            created_at: new Date().toISOString()
          }
        ])
      })
    }
    
    // Mock referrals endpoint
    if (url.includes('/api/referrals')) {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          commissionBalance: {
            usdt_balance: 250.00,
            share_balance: 50,
            total_earned: 500.00
          },
          referrals: []
        })
      })
    }
    
    // Mock KYC endpoint
    if (url.includes('/api/kyc')) {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          status: 'pending',
          documents: []
        })
      })
    }
    
    // Default: continue with actual request
    route.continue()
  })
  
  console.log('✅ Test data setup completed')
}

async function setupAuthenticatedState(page: any) {
  console.log('🔐 Setting up authenticated state...')
  
  // Set up localStorage with authentication tokens
  await page.addInitScript(() => {
    localStorage.setItem('sb-access-token', 'test-access-token')
    localStorage.setItem('sb-refresh-token', 'test-refresh-token')
    localStorage.setItem('user-profile', JSON.stringify({
      id: 'test-user-id',
      email: '<EMAIL>',
      telegram_profile: {
        id: 'test-profile-id',
        telegram_id: 123456789,
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User'
      }
    }))
  })
  
  // Set up cookies if needed
  await page.context().addCookies([
    {
      name: 'sb-access-token',
      value: 'test-access-token',
      domain: 'localhost',
      path: '/',
      httpOnly: false,
      secure: false,
      sameSite: 'Lax'
    }
  ])
  
  console.log('✅ Authenticated state setup completed')
}

export default globalSetup
