import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase-client'

// Mock Supabase client
vi.mock('@/lib/supabase-client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(),
      getSession: vi.fn(),
      signInWithOAuth: vi.fn(),
      signOut: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
      upsert: vi.fn(),
    })),
  },
}))

// Test component that uses the auth context
const TestComponent = () => {
  const { user, loading, signInWithTelegram, signOut } = useAuth()
  
  if (loading) {
    return <div>Loading...</div>
  }
  
  if (user) {
    return (
      <div>
        <div data-testid="user-email">{user.email}</div>
        <div data-testid="user-id">{user.id}</div>
        <button onClick={signOut} data-testid="sign-out">
          Sign Out
        </button>
      </div>
    )
  }
  
  return (
    <div>
      <div data-testid="not-authenticated">Not authenticated</div>
      <button onClick={signInWithTelegram} data-testid="sign-in">
        Sign In with Telegram
      </button>
    </div>
  )
}

const renderWithAuth = (component: React.ReactElement) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('AuthContext Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('shows loading state initially', async () => {
    // Mock initial loading state
    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    
    mockGetUser.mockImplementation(() => new Promise(() => {})) // Never resolves
    mockGetSession.mockImplementation(() => new Promise(() => {})) // Never resolves
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })

    renderWithAuth(<TestComponent />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('shows not authenticated state when no user', async () => {
    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    
    mockGetUser.mockResolvedValue({ data: { user: null }, error: null })
    mockGetSession.mockResolvedValue({ data: { session: null }, error: null })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })

    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('not-authenticated')).toBeInTheDocument()
    })
    
    expect(screen.getByTestId('sign-in')).toBeInTheDocument()
  })

  it('shows authenticated state when user exists', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: { telegram_id: 123456789 }
    }
    
    const mockTelegramProfile = {
      id: 'profile-id',
      telegram_id: 123456789,
      username: 'testuser',
      first_name: 'Test',
      last_name: 'User'
    }

    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    const mockFrom = vi.mocked(supabase.from)
    
    mockGetUser.mockResolvedValue({ data: { user: mockUser }, error: null })
    mockGetSession.mockResolvedValue({ 
      data: { session: { access_token: 'test-token' } }, 
      error: null 
    })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })
    
    // Mock the profile fetch
    const mockSelect = vi.fn().mockReturnThis()
    const mockEq = vi.fn().mockReturnThis()
    const mockSingle = vi.fn().mockResolvedValue({ 
      data: mockTelegramProfile, 
      error: null 
    })
    
    mockFrom.mockReturnValue({
      select: mockSelect,
      eq: mockEq,
      single: mockSingle,
      upsert: vi.fn(),
    } as any)

    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('user-email')).toBeInTheDocument()
    })
    
    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>')
    expect(screen.getByTestId('user-id')).toHaveTextContent('test-user-id')
    expect(screen.getByTestId('sign-out')).toBeInTheDocument()
  })

  it('handles sign in with Telegram', async () => {
    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    const mockSignInWithOAuth = vi.mocked(supabase.auth.signInWithOAuth)
    
    mockGetUser.mockResolvedValue({ data: { user: null }, error: null })
    mockGetSession.mockResolvedValue({ data: { session: null }, error: null })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })
    mockSignInWithOAuth.mockResolvedValue({ data: {}, error: null })

    const user = userEvent.setup()
    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('sign-in')).toBeInTheDocument()
    })
    
    await user.click(screen.getByTestId('sign-in'))
    
    expect(mockSignInWithOAuth).toHaveBeenCalledWith({
      provider: 'telegram',
      options: {
        redirectTo: expect.stringContaining('/auth/callback')
      }
    })
  })

  it('handles sign out', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: { telegram_id: 123456789 }
    }

    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    const mockSignOut = vi.mocked(supabase.auth.signOut)
    const mockFrom = vi.mocked(supabase.from)
    
    mockGetUser.mockResolvedValue({ data: { user: mockUser }, error: null })
    mockGetSession.mockResolvedValue({ 
      data: { session: { access_token: 'test-token' } }, 
      error: null 
    })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })
    mockSignOut.mockResolvedValue({ error: null })
    
    // Mock profile fetch
    mockFrom.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: {}, error: null }),
      upsert: vi.fn(),
    } as any)

    const user = userEvent.setup()
    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('sign-out')).toBeInTheDocument()
    })
    
    await user.click(screen.getByTestId('sign-out'))
    
    expect(mockSignOut).toHaveBeenCalled()
  })

  it('handles authentication errors gracefully', async () => {
    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    
    mockGetUser.mockResolvedValue({ 
      data: { user: null }, 
      error: { message: 'Authentication failed' } 
    })
    mockGetSession.mockResolvedValue({ 
      data: { session: null }, 
      error: { message: 'Session error' } 
    })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })

    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('not-authenticated')).toBeInTheDocument()
    })
    
    // Should still show the sign-in option despite errors
    expect(screen.getByTestId('sign-in')).toBeInTheDocument()
  })

  it('handles profile fetch errors', async () => {
    const mockUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: { telegram_id: 123456789 }
    }

    const mockGetUser = vi.mocked(supabase.auth.getUser)
    const mockGetSession = vi.mocked(supabase.auth.getSession)
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    const mockFrom = vi.mocked(supabase.from)
    
    mockGetUser.mockResolvedValue({ data: { user: mockUser }, error: null })
    mockGetSession.mockResolvedValue({ 
      data: { session: { access_token: 'test-token' } }, 
      error: null 
    })
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } }
    })
    
    // Mock profile fetch error
    mockFrom.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ 
        data: null, 
        error: { message: 'Profile not found' } 
      }),
      upsert: vi.fn(),
    } as any)

    renderWithAuth(<TestComponent />)
    
    await waitFor(() => {
      expect(screen.getByTestId('user-email')).toBeInTheDocument()
    })
    
    // Should still show user info even if profile fetch fails
    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>')
  })

  it('cleans up auth state change subscription on unmount', () => {
    const mockUnsubscribe = vi.fn()
    const mockOnAuthStateChange = vi.mocked(supabase.auth.onAuthStateChange)
    
    mockOnAuthStateChange.mockReturnValue({
      data: { subscription: { unsubscribe: mockUnsubscribe } }
    })

    const { unmount } = renderWithAuth(<TestComponent />)
    
    expect(mockOnAuthStateChange).toHaveBeenCalled()
    
    unmount()
    
    expect(mockUnsubscribe).toHaveBeenCalled()
  })
})
