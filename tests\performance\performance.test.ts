import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { performance } from 'perf_hooks'

// Mock performance API for testing
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
}

// Replace global performance with mock
Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
})

// Performance testing utilities
class PerformanceProfiler {
  private startTime: number = 0
  private measurements: Map<string, number[]> = new Map()

  start(label: string) {
    this.startTime = performance.now()
    performance.mark(`${label}-start`)
  }

  end(label: string): number {
    const endTime = performance.now()
    const duration = endTime - this.startTime
    
    performance.mark(`${label}-end`)
    performance.measure(label, `${label}-start`, `${label}-end`)
    
    if (!this.measurements.has(label)) {
      this.measurements.set(label, [])
    }
    this.measurements.get(label)!.push(duration)
    
    return duration
  }

  getAverageTime(label: string): number {
    const times = this.measurements.get(label) || []
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0
  }

  getMedianTime(label: string): number {
    const times = this.measurements.get(label) || []
    if (times.length === 0) return 0
    
    const sorted = [...times].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid]
  }

  clear() {
    this.measurements.clear()
    performance.clearMarks()
    performance.clearMeasures()
  }
}

describe('Performance Tests', () => {
  let profiler: PerformanceProfiler

  beforeEach(() => {
    profiler = new PerformanceProfiler()
    vi.clearAllMocks()
  })

  describe('Component Rendering Performance', () => {
    it('should render EnhancedButton within performance budget', async () => {
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      profiler.start('button-render')
      
      render(<EnhancedButton>Test Button</EnhancedButton>)
      
      const renderTime = profiler.end('button-render')
      
      // Button should render within 50ms
      expect(renderTime).toBeLessThan(50)
    })

    it('should render EnhancedCard within performance budget', async () => {
      const { EnhancedCard } = await import('@/components/ui/EnhancedCard')
      
      profiler.start('card-render')
      
      render(
        <EnhancedCard>
          <h2>Card Title</h2>
          <p>Card content with some text</p>
        </EnhancedCard>
      )
      
      const renderTime = profiler.end('card-render')
      
      // Card should render within 100ms
      expect(renderTime).toBeLessThan(100)
    })

    it('should render complex dashboard components efficiently', async () => {
      const { EnhancedCard, StatCard } = await import('@/components/ui/EnhancedCard')
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      profiler.start('dashboard-render')
      
      render(
        <div>
          <EnhancedCard>
            <h1>Dashboard</h1>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '1rem' }}>
              <StatCard title="Total Shares" value="1,234" />
              <StatCard title="Total Value" value="$12,340" />
              <StatCard title="Commission" value="$567" />
              <StatCard title="Referrals" value="8" />
            </div>
            <div style={{ marginTop: '2rem' }}>
              <EnhancedButton variant="gold">Purchase Shares</EnhancedButton>
              <EnhancedButton variant="outline">View Portfolio</EnhancedButton>
            </div>
          </EnhancedCard>
        </div>
      )
      
      const renderTime = profiler.end('dashboard-render')
      
      // Complex dashboard should render within 200ms
      expect(renderTime).toBeLessThan(200)
    })
  })

  describe('Re-rendering Performance', () => {
    it('should handle prop changes efficiently', async () => {
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      const { rerender } = render(<EnhancedButton>Initial Text</EnhancedButton>)
      
      profiler.start('button-rerender')
      
      // Simulate multiple prop changes
      for (let i = 0; i < 10; i++) {
        rerender(<EnhancedButton variant={i % 2 === 0 ? 'primary' : 'secondary'}>
          Updated Text {i}
        </EnhancedButton>)
      }
      
      const rerenderTime = profiler.end('button-rerender')
      
      // 10 re-renders should complete within 100ms
      expect(rerenderTime).toBeLessThan(100)
    })

    it('should handle state changes efficiently', async () => {
      const { EnhancedInput } = await import('@/components/ui/EnhancedInput')
      
      const { rerender } = render(
        <EnhancedInput label="Test Input" value="" onChange={() => {}} />
      )
      
      profiler.start('input-state-changes')
      
      // Simulate typing
      for (let i = 0; i < 20; i++) {
        const value = 'a'.repeat(i)
        rerender(
          <EnhancedInput label="Test Input" value={value} onChange={() => {}} />
        )
      }
      
      const stateChangeTime = profiler.end('input-state-changes')
      
      // 20 state changes should complete within 150ms
      expect(stateChangeTime).toBeLessThan(150)
    })
  })

  describe('Memory Usage', () => {
    it('should not create memory leaks with component mounting/unmounting', async () => {
      const { EnhancedCard } = await import('@/components/ui/EnhancedCard')
      
      // Track initial memory usage (simulated)
      let componentCount = 0
      
      profiler.start('memory-test')
      
      // Mount and unmount components multiple times
      for (let i = 0; i < 50; i++) {
        const { unmount } = render(
          <EnhancedCard key={i}>
            <div>Component {i}</div>
          </EnhancedCard>
        )
        componentCount++
        unmount()
        componentCount--
      }
      
      const testTime = profiler.end('memory-test')
      
      // Should complete within reasonable time
      expect(testTime).toBeLessThan(500)
      expect(componentCount).toBe(0) // All components should be unmounted
    })
  })

  describe('Bundle Size Impact', () => {
    it('should have reasonable import times for UI components', async () => {
      profiler.start('component-import')
      
      // Dynamic imports to test loading time
      await Promise.all([
        import('@/components/ui/EnhancedButton'),
        import('@/components/ui/EnhancedCard'),
        import('@/components/ui/EnhancedInput'),
        import('@/components/ui/Badge'),
      ])
      
      const importTime = profiler.end('component-import')
      
      // Component imports should complete within 100ms
      expect(importTime).toBeLessThan(100)
    })
  })

  describe('Animation Performance', () => {
    it('should handle hover animations efficiently', async () => {
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      render(<EnhancedButton data-testid="animated-button">Hover Me</EnhancedButton>)
      
      const button = screen.getByTestId('animated-button')
      
      profiler.start('hover-animation')
      
      // Simulate multiple hover events
      for (let i = 0; i < 10; i++) {
        button.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }))
        button.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }))
      }
      
      const animationTime = profiler.end('hover-animation')
      
      // Hover animations should complete within 200ms
      expect(animationTime).toBeLessThan(200)
    })
  })

  describe('Large Dataset Performance', () => {
    it('should handle large lists efficiently', async () => {
      const { EnhancedCard } = await import('@/components/ui/EnhancedCard')
      
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: i,
        title: `Item ${i}`,
        description: `Description for item ${i}`
      }))
      
      profiler.start('large-list-render')
      
      render(
        <div>
          {largeDataset.map(item => (
            <EnhancedCard key={item.id}>
              <h3>{item.title}</h3>
              <p>{item.description}</p>
            </EnhancedCard>
          ))}
        </div>
      )
      
      const renderTime = profiler.end('large-list-render')
      
      // 100 cards should render within 1 second
      expect(renderTime).toBeLessThan(1000)
    })
  })

  describe('Performance Regression Tests', () => {
    it('should maintain consistent performance across multiple runs', async () => {
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      const runs = 5
      const times: number[] = []
      
      for (let i = 0; i < runs; i++) {
        profiler.start(`consistency-test-${i}`)
        
        render(<EnhancedButton>Test Button {i}</EnhancedButton>)
        
        times.push(profiler.end(`consistency-test-${i}`))
      }
      
      // Calculate variance
      const average = times.reduce((a, b) => a + b, 0) / times.length
      const variance = times.reduce((acc, time) => acc + Math.pow(time - average, 2), 0) / times.length
      const standardDeviation = Math.sqrt(variance)
      
      // Standard deviation should be low (consistent performance)
      expect(standardDeviation).toBeLessThan(average * 0.3) // Within 30% of average
    })
  })

  describe('Resource Cleanup', () => {
    it('should clean up event listeners properly', async () => {
      const { EnhancedButton } = await import('@/components/ui/EnhancedButton')
      
      let listenerCount = 0
      const originalAddEventListener = Element.prototype.addEventListener
      const originalRemoveEventListener = Element.prototype.removeEventListener
      
      Element.prototype.addEventListener = function(...args) {
        listenerCount++
        return originalAddEventListener.apply(this, args)
      }
      
      Element.prototype.removeEventListener = function(...args) {
        listenerCount--
        return originalRemoveEventListener.apply(this, args)
      }
      
      const { unmount } = render(
        <EnhancedButton onClick={() => {}}>
          Button with Event Listener
        </EnhancedButton>
      )
      
      const listenersAfterMount = listenerCount
      
      unmount()
      
      const listenersAfterUnmount = listenerCount
      
      // Restore original methods
      Element.prototype.addEventListener = originalAddEventListener
      Element.prototype.removeEventListener = originalRemoveEventListener
      
      // Should clean up listeners on unmount
      expect(listenersAfterUnmount).toBeLessThanOrEqual(listenersAfterMount)
    })
  })
})
