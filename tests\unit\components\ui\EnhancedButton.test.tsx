import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { EnhancedButton, ButtonGroup, FloatingActionButton, IconButton } from '@/components/ui/EnhancedButton'

describe('EnhancedButton', () => {
  it('renders with default props', () => {
    render(<EnhancedButton>Click me</EnhancedButton>)
    const button = screen.getByRole('button', { name: /click me/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveClass('bg-gradient-to-r', 'from-blue-600', 'to-blue-700')
  })

  it('renders different variants correctly', () => {
    const { rerender } = render(<EnhancedButton variant="gold">Gold Button</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('from-yellow-400', 'to-yellow-500')

    rerender(<EnhancedButton variant="secondary">Secondary Button</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('bg-white', 'text-gray-900')

    rerender(<EnhancedButton variant="outline">Outline Button</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('border-2', 'border-primary-500')
  })

  it('renders different sizes correctly', () => {
    const { rerender } = render(<EnhancedButton size="xs">Extra Small</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('px-2.5', 'py-1.5', 'text-xs')

    rerender(<EnhancedButton size="lg">Large</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('px-6', 'py-3', 'text-base')

    rerender(<EnhancedButton size="xl">Extra Large</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('px-8', 'py-4', 'text-lg')
  })

  it('handles loading state correctly', () => {
    render(<EnhancedButton loading>Loading Button</EnhancedButton>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(screen.getByText('Loading Button')).toBeInTheDocument()
    // Check for loading spinner
    expect(button.querySelector('svg')).toBeInTheDocument()
  })

  it('handles disabled state correctly', () => {
    render(<EnhancedButton disabled>Disabled Button</EnhancedButton>)
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed')
  })

  it('renders with icons correctly', () => {
    const TestIcon = () => <span data-testid="test-icon">🔥</span>
    
    const { rerender } = render(
      <EnhancedButton icon={<TestIcon />} iconPosition="left">
        With Left Icon
      </EnhancedButton>
    )
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()

    rerender(
      <EnhancedButton icon={<TestIcon />} iconPosition="right">
        With Right Icon
      </EnhancedButton>
    )
    expect(screen.getByTestId('test-icon')).toBeInTheDocument()
  })

  it('handles click events correctly', async () => {
    const handleClick = vi.fn()
    const user = userEvent.setup()
    
    render(<EnhancedButton onClick={handleClick}>Clickable</EnhancedButton>)
    const button = screen.getByRole('button')
    
    await user.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('does not trigger click when disabled', async () => {
    const handleClick = vi.fn()
    const user = userEvent.setup()
    
    render(<EnhancedButton onClick={handleClick} disabled>Disabled</EnhancedButton>)
    const button = screen.getByRole('button')
    
    await user.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('applies fullWidth correctly', () => {
    render(<EnhancedButton fullWidth>Full Width</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('w-full')
  })

  it('applies custom className', () => {
    render(<EnhancedButton className="custom-class">Custom</EnhancedButton>)
    expect(screen.getByRole('button')).toHaveClass('custom-class')
  })

  it('supports keyboard navigation', async () => {
    const handleClick = vi.fn()
    const user = userEvent.setup()
    
    render(<EnhancedButton onClick={handleClick}>Keyboard Test</EnhancedButton>)
    const button = screen.getByRole('button')
    
    button.focus()
    expect(button).toHaveFocus()
    
    await user.keyboard('{Enter}')
    expect(handleClick).toHaveBeenCalledTimes(1)
    
    await user.keyboard(' ')
    expect(handleClick).toHaveBeenCalledTimes(2)
  })
})

describe('ButtonGroup', () => {
  it('renders children correctly', () => {
    render(
      <ButtonGroup>
        <EnhancedButton>Button 1</EnhancedButton>
        <EnhancedButton>Button 2</EnhancedButton>
        <EnhancedButton>Button 3</EnhancedButton>
      </ButtonGroup>
    )
    
    expect(screen.getByText('Button 1')).toBeInTheDocument()
    expect(screen.getByText('Button 2')).toBeInTheDocument()
    expect(screen.getByText('Button 3')).toBeInTheDocument()
  })

  it('applies horizontal orientation by default', () => {
    render(
      <ButtonGroup data-testid="button-group">
        <EnhancedButton>Button 1</EnhancedButton>
        <EnhancedButton>Button 2</EnhancedButton>
      </ButtonGroup>
    )
    
    expect(screen.getByTestId('button-group')).toHaveClass('flex', 'flex-row')
  })

  it('applies vertical orientation correctly', () => {
    render(
      <ButtonGroup orientation="vertical" data-testid="button-group">
        <EnhancedButton>Button 1</EnhancedButton>
        <EnhancedButton>Button 2</EnhancedButton>
      </ButtonGroup>
    )
    
    expect(screen.getByTestId('button-group')).toHaveClass('flex', 'flex-col')
  })
})

describe('FloatingActionButton', () => {
  it('renders with icon correctly', () => {
    const TestIcon = () => <span data-testid="fab-icon">+</span>
    
    render(<FloatingActionButton icon={<TestIcon />} />)
    expect(screen.getByTestId('fab-icon')).toBeInTheDocument()
  })

  it('applies position correctly', () => {
    const TestIcon = () => <span>+</span>
    const { rerender } = render(
      <FloatingActionButton icon={<TestIcon />} position="bottom-right" data-testid="fab" />
    )
    expect(screen.getByTestId('fab')).toHaveClass('fixed', 'bottom-6', 'right-6')

    rerender(
      <FloatingActionButton icon={<TestIcon />} position="top-left" data-testid="fab" />
    )
    expect(screen.getByTestId('fab')).toHaveClass('fixed', 'top-6', 'left-6')
  })

  it('shows tooltip on hover', async () => {
    const TestIcon = () => <span>+</span>
    const user = userEvent.setup()
    
    render(<FloatingActionButton icon={<TestIcon />} tooltip="Add Item" />)
    const button = screen.getByRole('button')
    
    expect(button).toHaveAttribute('title', 'Add Item')
  })

  it('handles click events', async () => {
    const handleClick = vi.fn()
    const TestIcon = () => <span>+</span>
    const user = userEvent.setup()
    
    render(<FloatingActionButton icon={<TestIcon />} onClick={handleClick} />)
    const button = screen.getByRole('button')
    
    await user.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})

describe('IconButton', () => {
  it('renders with icon correctly', () => {
    const TestIcon = () => <span data-testid="icon">⚙️</span>
    
    render(<IconButton icon={<TestIcon />} />)
    expect(screen.getByTestId('icon')).toBeInTheDocument()
  })

  it('applies size correctly', () => {
    const TestIcon = () => <span>⚙️</span>
    const { rerender } = render(
      <IconButton icon={<TestIcon />} size="sm" data-testid="icon-button" />
    )
    expect(screen.getByTestId('icon-button')).toHaveClass('w-8', 'h-8')

    rerender(
      <IconButton icon={<TestIcon />} size="lg" data-testid="icon-button" />
    )
    expect(screen.getByTestId('icon-button')).toHaveClass('w-12', 'h-12')
  })

  it('applies rounded correctly', () => {
    const TestIcon = () => <span>⚙️</span>
    const { rerender } = render(
      <IconButton icon={<TestIcon />} rounded={true} data-testid="icon-button" />
    )
    expect(screen.getByTestId('icon-button')).toHaveClass('rounded-full')

    rerender(
      <IconButton icon={<TestIcon />} rounded={false} data-testid="icon-button" />
    )
    expect(screen.getByTestId('icon-button')).toHaveClass('rounded-md')
  })

  it('shows tooltip', () => {
    const TestIcon = () => <span>⚙️</span>
    
    render(<IconButton icon={<TestIcon />} tooltip="Settings" />)
    const button = screen.getByRole('button')
    
    expect(button).toHaveAttribute('title', 'Settings')
  })

  it('handles click events', async () => {
    const handleClick = vi.fn()
    const TestIcon = () => <span>⚙️</span>
    const user = userEvent.setup()
    
    render(<IconButton icon={<TestIcon />} onClick={handleClick} />)
    const button = screen.getByRole('button')
    
    await user.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
