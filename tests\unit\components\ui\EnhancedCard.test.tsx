import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { 
  EnhancedCard, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  CardFooter,
  StatCard,
  FeatureCard 
} from '@/components/ui/EnhancedCard'

describe('EnhancedCard', () => {
  it('renders with default props', () => {
    render(
      <EnhancedCard data-testid="card">
        <div>Card content</div>
      </EnhancedCard>
    )
    
    const card = screen.getByTestId('card')
    expect(card).toBeInTheDocument()
    expect(card).toHaveClass('bg-white', 'border', 'border-gray-200')
    expect(screen.getByText('Card content')).toBeInTheDocument()
  })

  it('renders different variants correctly', () => {
    const { rerender } = render(
      <EnhancedCard variant="gold" data-testid="card">
        Gold Card
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('bg-gradient-to-br', 'from-yellow-50', 'to-yellow-100')

    rerender(
      <EnhancedCard variant="elevated" data-testid="card">
        Elevated Card
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('bg-white', 'border-0')

    rerender(
      <EnhancedCard variant="glass" data-testid="card">
        Glass Card
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('bg-white/80', 'backdrop-blur-sm')
  })

  it('applies different padding sizes', () => {
    const { rerender } = render(
      <EnhancedCard padding="xs" data-testid="card">
        Extra Small Padding
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('p-2')

    rerender(
      <EnhancedCard padding="lg" data-testid="card">
        Large Padding
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('p-6')

    rerender(
      <EnhancedCard padding="xl" data-testid="card">
        Extra Large Padding
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('p-8')
  })

  it('applies hover effects when enabled', () => {
    render(
      <EnhancedCard hover data-testid="card">
        Hoverable Card
      </EnhancedCard>
    )
    
    expect(screen.getByTestId('card')).toHaveClass('hover:shadow-md', 'hover:border-gray-300')
  })

  it('handles interactive state correctly', async () => {
    const handleClick = vi.fn()
    const user = userEvent.setup()
    
    render(
      <EnhancedCard interactive onClick={handleClick} data-testid="card">
        Interactive Card
      </EnhancedCard>
    )
    
    const card = screen.getByTestId('card')
    expect(card).toHaveClass('cursor-pointer')
    expect(card).toHaveAttribute('role', 'button')
    expect(card).toHaveAttribute('tabIndex', '0')
    
    await user.click(card)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('shows loading state correctly', () => {
    render(
      <EnhancedCard loading data-testid="card">
        Loading Card
      </EnhancedCard>
    )
    
    const card = screen.getByTestId('card')
    expect(card).toHaveClass('animate-pulse')
    
    // Should show skeleton content instead of children
    expect(screen.queryByText('Loading Card')).not.toBeInTheDocument()
    
    // Should have skeleton elements
    const skeletonElements = card.querySelectorAll('.bg-gray-200')
    expect(skeletonElements.length).toBeGreaterThan(0)
  })

  it('applies custom rounded corners', () => {
    const { rerender } = render(
      <EnhancedCard rounded="none" data-testid="card">
        No Rounded
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('rounded-none')

    rerender(
      <EnhancedCard rounded="xl" data-testid="card">
        Extra Large Rounded
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('rounded-xl')

    rerender(
      <EnhancedCard rounded="2xl" data-testid="card">
        2XL Rounded
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('rounded-2xl')
  })

  it('applies shadow correctly', () => {
    const { rerender } = render(
      <EnhancedCard shadow="none" data-testid="card">
        No Shadow
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('shadow-none')

    rerender(
      <EnhancedCard shadow="lg" data-testid="card">
        Large Shadow
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('shadow-lg')

    rerender(
      <EnhancedCard shadow="xl" data-testid="card">
        Extra Large Shadow
      </EnhancedCard>
    )
    expect(screen.getByTestId('card')).toHaveClass('shadow-xl')
  })

  it('supports keyboard navigation when interactive', async () => {
    const handleClick = vi.fn()
    const user = userEvent.setup()
    
    render(
      <EnhancedCard interactive onClick={handleClick} data-testid="card">
        Keyboard Test
      </EnhancedCard>
    )
    
    const card = screen.getByTestId('card')
    card.focus()
    expect(card).toHaveFocus()
    
    await user.keyboard('{Enter}')
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})

describe('CardHeader', () => {
  it('renders children correctly', () => {
    render(
      <CardHeader>
        <h2>Card Title</h2>
        <button>Action</button>
      </CardHeader>
    )
    
    expect(screen.getByText('Card Title')).toBeInTheDocument()
    expect(screen.getByText('Action')).toBeInTheDocument()
  })

  it('applies divider when enabled', () => {
    render(
      <CardHeader divider data-testid="header">
        Header with divider
      </CardHeader>
    )
    
    expect(screen.getByTestId('header')).toHaveClass('border-b', 'border-gray-200', 'pb-4', 'mb-4')
  })
})

describe('CardTitle', () => {
  it('renders with default size', () => {
    render(<CardTitle>Default Title</CardTitle>)
    const title = screen.getByText('Default Title')
    expect(title).toBeInTheDocument()
    expect(title).toHaveClass('text-lg', 'font-semibold')
  })

  it('renders different sizes correctly', () => {
    const { rerender } = render(<CardTitle size="sm">Small Title</CardTitle>)
    expect(screen.getByText('Small Title')).toHaveClass('text-sm', 'font-medium')

    rerender(<CardTitle size="xl">Extra Large Title</CardTitle>)
    expect(screen.getByText('Extra Large Title')).toHaveClass('text-xl', 'font-bold')
  })
})

describe('CardContent', () => {
  it('renders children correctly', () => {
    render(
      <CardContent>
        <p>This is card content</p>
      </CardContent>
    )
    
    expect(screen.getByText('This is card content')).toBeInTheDocument()
  })

  it('applies default text color', () => {
    render(
      <CardContent data-testid="content">
        Content text
      </CardContent>
    )
    
    expect(screen.getByTestId('content')).toHaveClass('text-gray-600')
  })
})

describe('CardFooter', () => {
  it('renders children correctly', () => {
    render(
      <CardFooter>
        <span>Footer content</span>
        <button>Footer action</button>
      </CardFooter>
    )
    
    expect(screen.getByText('Footer content')).toBeInTheDocument()
    expect(screen.getByText('Footer action')).toBeInTheDocument()
  })

  it('applies divider when enabled', () => {
    render(
      <CardFooter divider data-testid="footer">
        Footer with divider
      </CardFooter>
    )
    
    expect(screen.getByTestId('footer')).toHaveClass('border-t', 'border-gray-200', 'pt-4', 'mt-4')
  })
})

describe('StatCard', () => {
  it('renders basic stat information', () => {
    render(
      <StatCard
        title="Total Sales"
        value="$12,345"
      />
    )
    
    expect(screen.getByText('Total Sales')).toBeInTheDocument()
    expect(screen.getByText('$12,345')).toBeInTheDocument()
  })

  it('renders with change indicator', () => {
    render(
      <StatCard
        title="Revenue"
        value="$50,000"
        change={{ value: "+12.5%", type: "increase" }}
      />
    )
    
    expect(screen.getByText('Revenue')).toBeInTheDocument()
    expect(screen.getByText('$50,000')).toBeInTheDocument()
    expect(screen.getByText('+12.5%')).toBeInTheDocument()
    expect(screen.getByText('↗')).toBeInTheDocument()
  })

  it('renders with icon', () => {
    const TestIcon = () => <span data-testid="stat-icon">💰</span>
    
    render(
      <StatCard
        title="Profit"
        value="$25,000"
        icon={<TestIcon />}
      />
    )
    
    expect(screen.getByTestId('stat-icon')).toBeInTheDocument()
  })

  it('applies variant styling correctly', () => {
    const { rerender } = render(
      <StatCard
        title="Test"
        value="100"
        variant="success"
        data-testid="stat-card"
      />
    )
    expect(screen.getByTestId('stat-card')).toHaveClass('border-green-200', 'bg-gradient-to-br', 'from-green-50', 'to-green-100')

    rerender(
      <StatCard
        title="Test"
        value="100"
        variant="warning"
        data-testid="stat-card"
      />
    )
    expect(screen.getByTestId('stat-card')).toHaveClass('border-orange-200', 'bg-gradient-to-br', 'from-orange-50', 'to-orange-100')
  })
})

describe('FeatureCard', () => {
  it('renders basic feature information', () => {
    render(
      <FeatureCard
        title="Feature Title"
        description="Feature description text"
      />
    )
    
    expect(screen.getByText('Feature Title')).toBeInTheDocument()
    expect(screen.getByText('Feature description text')).toBeInTheDocument()
  })

  it('renders with icon', () => {
    const TestIcon = () => <span data-testid="feature-icon">🚀</span>
    
    render(
      <FeatureCard
        title="Feature"
        description="Description"
        icon={<TestIcon />}
      />
    )
    
    expect(screen.getByTestId('feature-icon')).toBeInTheDocument()
  })

  it('renders with action button', async () => {
    const handleAction = vi.fn()
    const user = userEvent.setup()
    
    render(
      <FeatureCard
        title="Feature"
        description="Description"
        action={{
          label: "Learn More",
          onClick: handleAction
        }}
      />
    )
    
    const actionText = screen.getByText('Learn More →')
    expect(actionText).toBeInTheDocument()
    
    // Click the card to trigger action
    const card = actionText.closest('[role="button"]')
    if (card) {
      await user.click(card)
      expect(handleAction).toHaveBeenCalledTimes(1)
    }
  })

  it('applies hover effects when interactive', () => {
    render(
      <FeatureCard
        title="Feature"
        description="Description"
        action={{
          label: "Action",
          onClick: vi.fn()
        }}
        data-testid="feature-card"
      />
    )
    
    expect(screen.getByTestId('feature-card')).toHaveClass('group')
  })
})
