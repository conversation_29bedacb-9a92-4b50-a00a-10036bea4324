
export interface CalculatorInputs {
  landHa: number;
  avgGravelThickness: number;
  inSituGrade: number;
  recoveryFactor: number;
  goldPriceUsdPerKg: number;
  opexPercent: number;
  userShares: number;
  dividendPayoutPercent: number;
}

export interface CalculatedValues {
  numPlants: number;
  volumeM3: number;
  tonnesInSitu: number;
  containedGoldG: number;
  annualThroughputT: number;
  annualGoldKg: number;
  annualRevenue: number;
  annualOperatingCost: number;
  annualEbit: number;
  dividendPerShare: number;
  userAnnualDividend: number;
}

export interface ProjectionYear {
  year: number;
  cumulativeGoldKg: number;
  remainingReserveKg: number;
  ebit: number;
  dividendPerShare: number;
  userDividend: number;
}
